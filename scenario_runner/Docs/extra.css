.build-buttons{
    text-align: center;
}

.build-buttons > p {
    display: inline-block;
    vertical-align: top;
    padding: 5px;
}

.vector-zero {
    text-align: center;
}


/************************* DEFAULT TABLES **************************/

table {
  border: 1px solid #242424;
  background-color: #f3f6f6;
  text-align: left;
  border-collapse: collapse;
}

table thead {
  background: #ffffff; 
  border-bottom: 1px solid #444444;
}

table tr:nth-child(even) {
  background: #ffffff;
}

table thead th {
  padding: 7px 13px;
}

table tbody td{
  padding: 7px 13px;
}

/************************* TOWN SLIDER **************************/

 * {box-sizing:border-box}

/* Container */
.townslider-container {
  max-width: 1000px;
  position: relative;
  margin: auto;
}

/* Hide the images by default */
.townslide {
  display: none;
  text-align: center; 

}

/* Fading animation for slides */
.fade {
  -webkit-animation-name: fade;
  -webkit-animation-duration: 1.5s;
  animation-name: fade;
  animation-duration: 1.5s;
}

@-webkit-keyframes fade {
  from {opacity: .4}
  to {opacity: 1}
}

@keyframes fade {
  from {opacity: .4}
  to {opacity: 1}
}

/* "next" and "previous" buttons */
.prev, .next {
  cursor: pointer;
  position: absolute;
  top: 50%;
  width: auto;
  margin-top: -22px;
  padding: 16px;
  color: white;
  font-weight: bold;
  font-size: 18px;
  transition: 0.6s ease;
  border-radius: 0 3px 3px 0;
  user-select: none;
}

/* Position the "next" button*/
.next {
  right: 0;
  border-radius: 3px 0 0 3px;
}

/* Black background color to buttons when hovering*/
.prev:hover, .next:hover {
  background-color: rgba(0,0,0,0.8);
}

/* Caption text for towns */
.text {
  color: #f2f2f2;
  font-size: 15px;
  padding: 8px 12px;
  position: absolute;
  bottom: 8px;
  width: 100%;
  text-align: center;
  /*background-color:rgba(0,0,0,0.5);*/
}

/* The dot indicators for slides */
.dot {
  cursor: pointer;
  height: 15px;
  width: 15px;
  margin: 0 2px;
  background-color: #bbb;
  border-radius: 50%;
  display: inline-block;
  transition: background-color 0.6s ease;
}

.active, .dot:hover {
  background-color: #717171;
}

