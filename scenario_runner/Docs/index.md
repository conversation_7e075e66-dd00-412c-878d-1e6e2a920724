[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
![GitHub tag (latest SemVer)](https://img.shields.io/github/tag/carla-simulator/scenario_runner.svg)
[![Build Status](https://travis-ci.com/carla-simulator/scenario_runner.svg?branch=master)](https://travis-ci.com/carla/scenario_runner)

# ScenarioRunner

ScenarioRunner is a module that allows traffic scenario definition and execution for the [CARLA](http://carla.org/ ) simulator. The scenarios can be defined through a Python interface or using the [OpenSCENARIO](http://www.openscenario.org/) standard.  
  
ScenarioRunner can also be used to prepare AD agents for their evaluation, by easily creating complex traffic scenarios and routes for the agents to navigate through. These results can be validated and shared in the [CARLA Leaderboard](https://leaderboard.carla.org/), an open platform for the community to fairly compare their progress, evaluating agents in realistic traffic situatoins.


The CARLA forum has a specific section regarding <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, for users to post any doubts or suggestions that may arise during the reading of this documentation.  
<div class="build-buttons">
<a href="https://forum.carla.org/" target="_blank" class="btn btn-neutral" title="Go to the latest CARLA release">
CARLA forum</a>
</div>

---

## Quick start

[**Get ScenarioRunner**](<getting_scenariorunner>) — Tutorial on how to download and launch ScenarioRunner.<br>

[**First steps**](<getting_started>) — Brief tutorials on how to run different types of scenarios.<br>

[**Create a new scenario**](<creating_new_scenario>) — Tutorial on how to create a new scenario using ScenarioRunner.<br>

[**Metrics module**](<metrics_module>) — Explanation of the metrics module.<br>

[**F.A.Q.**](<FAQ>) — Some of the most frequent installation issues.<br>

[**Release notes**](<CHANGELOG>) — Features, fixes and other changes listed per release.<br>


## References

[**List of scenarios**](<list_of_scenarios>) — Example scenarios available in ScenarioRunner.<br>

[**OpenScenario support**](<openscenario_support>) — Support status of OpenSCENARIO features.<br>


## Contributing
[**Code of conduct**](<CODE_OF_CONDUCT>) — Standard rights and duties for contributors.<br>

[**Coding standard**](<coding_standard>) — Guidelines to write proper code.<br>

[**Contribution guidelines**](<CONTRIBUTING>) — The different ways to contribute to ScenarioRunner.<br>

