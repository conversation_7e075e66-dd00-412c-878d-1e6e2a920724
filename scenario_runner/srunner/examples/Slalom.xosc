<?xml version="1.0"?>
<OpenSCENARIO>
  <FileHeader revMajor="1" revMinor="0" date="2020-07-30T12:00:00" description="CARLA:Slalom" author="<PERSON>"/>
  <ParameterDeclarations/>
  <CatalogLocations/>
  <RoadNetwork>
    <LogicFile filepath="Town01"/>
    <SceneGraphFile filepath=""/>
  </RoadNetwork>
  <Entities>
    <ScenarioObject name="hero">
      <Vehicle name="vehicle.nissan.micra" vehicleCategory="car">
        <ParameterDeclarations/>
        <Performance maxSpeed="69.444" maxAcceleration="200" maxDeceleration="10.0"/>
        <BoundingBox>
          <Center x="1.5" y="0.0" z="0.9"/>
          <Dimensions width="2.1" length="4.5" height="1.8"/>
        </BoundingBox>
        <Axles>
          <FrontAxle maxSteering="0.5" wheelDiameter="0.6" trackWidth="1.8" positionX="3.1" positionZ="0.3"/>
          <RearAxle maxSteering="0.0" wheelDiameter="0.6" trackWidth="1.8" positionX="0.0" positionZ="0.3"/>
        </Axles>
        <Properties>
          <Property name="type" value="ego_vehicle"/>
        </Properties>
      </Vehicle>
    </ScenarioObject>
    <ScenarioObject name="c1">
      <MiscObject mass="500.0" name="static.prop.container" miscObjectCategory="obstacle">
        <ParameterDeclarations/>
        <BoundingBox>
          <Center x="-1.0" y="0.0" z="0.85"/>
          <Dimensions width="1.0" length="2.0" height="1.7"/>
        </BoundingBox>
        <Properties>
          <Property name="type" value="simulation"/>
        </Properties>
      </MiscObject>
    </ScenarioObject>
     <ScenarioObject name="c2">
      <MiscObject mass="500.0" name="static.prop.container" miscObjectCategory="obstacle">
        <ParameterDeclarations/>
        <BoundingBox>
          <Center x="-1.0" y="0.0" z="0.85"/>
          <Dimensions width="1.0" length="2.0" height="1.7"/>
        </BoundingBox>
        <Properties>
          <Property name="type" value="simulation"/>
        </Properties>
      </MiscObject>
    </ScenarioObject>
     <ScenarioObject name="c3">
      <MiscObject mass="500.0" name="static.prop.container" miscObjectCategory="obstacle">
        <ParameterDeclarations/>
        <BoundingBox>
          <Center x="-1.0" y="0.0" z="0.85"/>
          <Dimensions width="1.0" length="2.0" height="1.7"/>
        </BoundingBox>
        <Properties>
          <Property name="type" value="simulation"/>
        </Properties>
      </MiscObject>
    </ScenarioObject>
     <ScenarioObject name="c4">
      <MiscObject mass="500.0" name="static.prop.container" miscObjectCategory="obstacle">
        <ParameterDeclarations/>
        <BoundingBox>
          <Center x="-1.0" y="0.0" z="0.85"/>
          <Dimensions width="1.0" length="2.0" height="1.7"/>
        </BoundingBox>
        <Properties>
          <Property name="type" value="simulation"/>
        </Properties>
      </MiscObject>
    </ScenarioObject>
    <ScenarioObject name="c5">
      <MiscObject mass="500.0" name="static.prop.container" miscObjectCategory="obstacle">
        <ParameterDeclarations/>
        <BoundingBox>
          <Center x="-1.0" y="0.0" z="0.85"/>
          <Dimensions width="1.0" length="2.0" height="1.7"/>
        </BoundingBox>
        <Properties>
          <Property name="type" value="simulation"/>
        </Properties>
      </MiscObject>
    </ScenarioObject>
  </Entities>
  <Storyboard>
    <Init>
      <Actions>
        <GlobalAction>
          <EnvironmentAction>
            <Environment name="Environment1">
              <TimeOfDay animation="false" dateTime="2019-06-25T12:00:00"/>
              <Weather cloudState="free">
                <Sun intensity="0.35" azimuth="0" elevation="1.31"/>
                <Fog visualRange="100000.0"/>
                <Precipitation precipitationType="rain" intensity="0.2"/>
              </Weather>
              <RoadCondition frictionScaleFactor="1.0"/>
            </Environment>
          </EnvironmentAction>
        </GlobalAction>
        <Private entityRef="hero">
          <PrivateAction>
            <TeleportAction>
              <Position>
                <WorldPosition x="92.44" y="314" z="0" h="-1.5707963267948966"/>
              </Position>
            </TeleportAction>
          </PrivateAction>
          <PrivateAction>
            <ControllerAction>
              <AssignControllerAction>
                <Controller name="HeroAgent">
                  <Properties>
                    <Property name="module" value="external_control"/>
                  </Properties>
                </Controller>
              </AssignControllerAction>
              <OverrideControllerValueAction>
                <Throttle value="0" active="false"/>
                <Brake value="0" active="false"/>
                <Clutch value="0" active="false"/>
                <ParkingBrake value="0" active="false"/>
                <SteeringWheel value="0" active="false"/>
                <Gear number="0" active="false"/>
              </OverrideControllerValueAction>
            </ControllerAction>
          </PrivateAction>
        </Private>
        <Private entityRef="c1">
          <PrivateAction>
            <TeleportAction>
              <Position>
                <WorldPosition x="90" y="308" z="0.0" h="-1.57079632679"/>
              </Position>
            </TeleportAction>
          </PrivateAction>
        </Private>
        <Private entityRef="c2">
          <PrivateAction>
            <TeleportAction>
              <Position>
                <WorldPosition x="95" y="298" z="0.3" h="1.57079632679"/>
              </Position>
            </TeleportAction>
          </PrivateAction>
        </Private>
        <Private entityRef="c3">
          <PrivateAction>
            <TeleportAction>
              <Position>
                <WorldPosition x="91" y="288" z="0.3" h="0"/>
              </Position>
            </TeleportAction>
          </PrivateAction>
        </Private>
        <Private entityRef="c4">
          <PrivateAction>
            <TeleportAction>
              <Position>
                <WorldPosition x="93" y="278" z="0.0" h="0"/>
              </Position>
            </TeleportAction>
          </PrivateAction>
        </Private>
        <Private entityRef="c5">
          <PrivateAction>
            <TeleportAction>
              <Position>
                <WorldPosition x="91" y="268" z="0.0" h="0"/>
              </Position>
            </TeleportAction>
          </PrivateAction>
        </Private>
      </Actions>
    </Init>
    <Story name="MyStory">
      <Act name="Behavior">
        <ManeuverGroup name="NoManeuver" maximumExecutionCount="1">
          <Actors selectTriggeringEntities="false">
            <EntityRef entityRef="hero"/>
          </Actors>
        </ManeuverGroup>
        <StartTrigger>
          <ConditionGroup>
            <Condition name="OverallStartCondition" delay="0" conditionEdge="rising">
              <ByValueCondition>
                <SimulationTimeCondition value="120.0" rule="greaterThan"/>
              </ByValueCondition>
            </Condition>
          </ConditionGroup>
        </StartTrigger>
        <StopTrigger>
          <ConditionGroup>
            <Condition name="EndCondition" delay="1" conditionEdge="rising">
              <ByValueCondition>
                <SimulationTimeCondition value="120.0" rule="greaterThan"/>
              </ByValueCondition>
            </Condition>
          </ConditionGroup>
        </StopTrigger>
      </Act>
    </Story>
    <StopTrigger>
      <ConditionGroup>
        <Condition name="criteria_RunningStopTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_RunningRedLightTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_WrongLaneTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_OnSidewalkTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_KeepLaneTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_CollisionTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_DrivenDistanceTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="distance_success" value="70" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
      </ConditionGroup>
    </StopTrigger>
  </Storyboard>
</OpenSCENARIO>
