<?xml version="1.0"?>
<OpenSCENARIO>
  <FileHeader revMajor="1" revMinor="0" date="2020-03-24T12:00:00" description="CARLA:SynchronizeIntersectionEntry" author=""/>
  <ParameterDeclarations/>
  <CatalogLocations/>
  <RoadNetwork>
    <LogicFile filepath="Town01"/>
    <SceneGraphFile filepath=""/>
  </RoadNetwork>
  <Entities>
    <ScenarioObject name="hero">
      <Vehicle name="vehicle.tesla.model3" vehicleCategory="car">
        <ParameterDeclarations/>
        <Performance maxSpeed="69.444" maxAcceleration="200" maxDeceleration="10.0"/>
        <BoundingBox>
          <Center x="1.5" y="0.0" z="0.9"/>
          <Dimensions width="2.1" length="4.5" height="1.8"/>
        </BoundingBox>
        <Axles>
          <FrontAxle maxSteering="0.5" wheelDiameter="0.6" trackWidth="1.8" positionX="3.1" positionZ="0.3"/>
          <RearAxle maxSteering="0.0" wheelDiameter="0.6" trackWidth="1.8" positionX="0.0" positionZ="0.3"/>
        </Axles>
        <Properties>
          <Property name="type" value="ego_vehicle"/>
        </Properties>
      </Vehicle>
    </ScenarioObject>
    <ScenarioObject name="adversary">
      <Vehicle name="vehicle.audi.tt" vehicleCategory="bicycle">
        <ParameterDeclarations/>
        <Performance maxSpeed="69.444" maxAcceleration="200" maxDeceleration="10.0"/>
        <BoundingBox>
          <Center x="1.5" y="0.0" z="0.9"/>
          <Dimensions width="2.1" length="4.5" height="1.8"/>
        </BoundingBox>
        <Axles>
          <FrontAxle maxSteering="0.5" wheelDiameter="0.6" trackWidth="1.8" positionX="3.1" positionZ="0.3"/>
          <RearAxle maxSteering="0.0" wheelDiameter="0.6" trackWidth="1.8" positionX="0.0" positionZ="0.3"/>
        </Axles>
        <Properties>
          <Property name="type" value="simulation"/>
        </Properties>
      </Vehicle>
    </ScenarioObject>
  </Entities>
  <Storyboard>
    <Init>
      <Actions>
        <GlobalAction>
          <EnvironmentAction>
            <Environment name="Environment1">
              <TimeOfDay animation="false" dateTime="2019-06-25T12:00:00"/>
              <Weather cloudState="free">
                <Sun intensity="0.85" azimuth="0" elevation="1.31"/>
                <Fog visualRange="100000.0"/>
                <Precipitation precipitationType="dry" intensity="0.0"/>
              </Weather>
              <RoadCondition frictionScaleFactor="1.0"/>
            </Environment>
          </EnvironmentAction>
        </GlobalAction>
        <Private entityRef="hero">
          <PrivateAction>
            <TeleportAction>
              <Position>
                <WorldPosition x="140" y="55" z="0" h="3.14159265359"/>
              </Position>
            </TeleportAction>
          </PrivateAction>
          <PrivateAction>
            <ControllerAction>
              <AssignControllerAction>
                <Controller name="HeroAgent">
                  <Properties>
                    <Property name="module" value="external_control"/>
                  </Properties>
                </Controller>
              </AssignControllerAction>
              <OverrideControllerValueAction>
                <Throttle value="0" active="false"/>
                <Brake value="0" active="false"/>
                <Clutch value="0" active="false"/>
                <ParkingBrake value="0" active="false"/>
                <SteeringWheel value="0" active="false"/>
                <Gear number="0" active="false"/>
              </OverrideControllerValueAction>
            </ControllerAction>
          </PrivateAction>
        </Private>
        <Private entityRef="adversary">
          <PrivateAction>
            <TeleportAction>
              <Position>
                <WorldPosition x="92.5" y="91" z="0.2" h="-1.56"/>
              </Position>
            </TeleportAction>
          </PrivateAction>
          <PrivateAction>
            <ControllerAction>
              <AssignControllerAction>
                <Controller name="AdversaryAgent">
                  <Properties>
                    <Property name="module" value="npc_vehicle_control"/>
                  </Properties>
                </Controller>
              </AssignControllerAction>
              <OverrideControllerValueAction>
                <Throttle value="0" active="false"/>
                <Brake value="0" active="false"/>
                <Clutch value="0" active="false"/>
                <ParkingBrake value="0" active="false"/>
                <SteeringWheel value="0" active="false"/>
                <Gear number="0" active="false"/>
              </OverrideControllerValueAction>
            </ControllerAction>
          </PrivateAction>
        </Private>
      </Actions>
    </Init>
    <Story name="MyStory">
      <Act name="Behavior">
        <ManeuverGroup maximumExecutionCount="1" name="ManeuverSequence">
          <Actors selectTriggeringEntities="false">
            <EntityRef entityRef="adversary"/>
          </Actors>
          <Maneuver name="SynchronizeManeuver">
            <Event name="RouteCreation" priority="overwrite">
              <Action name="RouteCreation">
                <PrivateAction>
                  <RoutingAction>
                    <AcquirePositionAction>
                      <Position>
                        <WorldPosition x="92" y="13.5" z="0" h="0"/>
                      </Position>
                    </AcquirePositionAction>
                  </RoutingAction>
                </PrivateAction>
              </Action>
              <StartTrigger>
                <ConditionGroup>
                  <Condition name="StartCondition" delay="0" conditionEdge="rising">
                    <ByValueCondition>
                      <SimulationTimeCondition value="0.1" rule="greaterThan"/>
                    </ByValueCondition>
                  </Condition>
                </ConditionGroup>
              </StartTrigger>
            </Event>
            <Event name="ActorSynchronization" priority="overwrite">
              <Action name="ActorSynchronization">
                <PrivateAction>
                  <SynchronizeAction masterEntityRef="hero">
                    <TargetPositionMaster>
                      <WorldPosition x="103" y="55" z="0" h="3.14159"/>
                    </TargetPositionMaster>
                    <TargetPosition>
                      <WorldPosition x="92" y="63.8" z="0" h="-1.56"/>
                    </TargetPosition>
                    <FinalSpeed>
                      <RelativeSpeedToMaster value='3' speedTargetValueType='delta'/>
                    </FinalSpeed>
                  </SynchronizeAction>
                </PrivateAction>
              </Action>
              <StartTrigger>
                <ConditionGroup>
                  <Condition name="StartCondition" delay="0" conditionEdge="rising">
                    <ByValueCondition>
                      <SimulationTimeCondition value="0.1" rule="greaterThan"/>
                    </ByValueCondition>
                  </Condition>
                </ConditionGroup>
              </StartTrigger>
            </Event>
          </Maneuver>
        </ManeuverGroup>
        <StartTrigger>
          <ConditionGroup>
            <Condition name="OverallStartCondition" delay="0" conditionEdge="rising">
              <ByEntityCondition>
                <TriggeringEntities triggeringEntitiesRule="any">
                  <EntityRef entityRef="hero"/>
                </TriggeringEntities>
                <EntityCondition>
                  <TraveledDistanceCondition value="0.1"/>
                </EntityCondition>
              </ByEntityCondition>
            </Condition>
          </ConditionGroup>
        </StartTrigger>
        <StopTrigger>
          <ConditionGroup>
            <Condition name="EndCondition" delay="0" conditionEdge="rising">
              <ByEntityCondition>
                <TriggeringEntities triggeringEntitiesRule="any">
                  <EntityRef entityRef="hero"/>
                </TriggeringEntities>
                <EntityCondition>
                  <TraveledDistanceCondition value="200.0"/>
                </EntityCondition>
              </ByEntityCondition>
            </Condition>
          </ConditionGroup>
        </StopTrigger>
      </Act>
    </Story>
    <StopTrigger>
      <ConditionGroup>
        <Condition name="criteria_RunningStopTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_RunningRedLightTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_WrongLaneTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_OnSidewalkTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_KeepLaneTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_CollisionTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="" value="" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
        <Condition name="criteria_DrivenDistanceTest" delay="0" conditionEdge="rising">
          <ByValueCondition>
            <ParameterCondition parameterRef="distance_success" value="100" rule="lessThan"/>
          </ByValueCondition>
        </Condition>
      </ConditionGroup>
    </StopTrigger>
  </Storyboard>
</OpenSCENARIO>
