<?xml version="1.0" encoding="UTF-8"?>
<OpenSCENARIO>
  <FileHeader revMajor="1" revMinor="0" date="2020-03-20T12:00:00" description="CARLA:ChangingWeatherExample" author=""/>
  <ParameterDeclarations/>
  <CatalogLocations/>
  <RoadNetwork>
    <LogicFile filepath="Town01"/>
    <SceneGraphFile filepath=""/>
  </RoadNetwork>
  <Entities>
    <ScenarioObject name="hero">
      <Vehicle name="vehicle.lincoln.mkz_2017" vehicleCategory="car">
        <ParameterDeclarations/>
        <Performance maxSpeed="69.444" maxAcceleration="200" maxDeceleration="10.0"/>
        <BoundingBox>
          <Center x="1.5" y="0.0" z="0.9"/>
          <Dimensions width="2.1" length="4.5" height="1.8"/>
        </BoundingBox>
        <Axles>
          <FrontAxle maxSteering="0.5" wheelDiameter="0.6" trackWidth="1.8" positionX="3.1" positionZ="0.3"/>
          <RearAxle maxSteering="0.0" wheelDiameter="0.6" trackWidth="1.8" positionX="0.0" positionZ="0.3"/>
        </Axles>
        <Properties>
          <Property name="type" value="ego_vehicle"/>
          <Property name="color" value="0,0,255"/>
        </Properties>
      </Vehicle>
    </ScenarioObject>
  </Entities>
  <Storyboard>
    <Init>
      <Actions>
        <GlobalAction>
          <EnvironmentAction>
            <Environment name="Environment1">
              <TimeOfDay animation="true" dateTime="2020-01-01T12:00:00"/>
              <Weather cloudState="free">
                <Sun intensity="0.85" azimuth="0" elevation="1.31"/>
                <Fog visualRange="100000.0"/>
                <Precipitation precipitationType="dry" intensity="0.0"/>
              </Weather>
              <RoadCondition frictionScaleFactor="1.0"/>
            </Environment>
          </EnvironmentAction>
        </GlobalAction>
        <Private entityRef="hero">
          <PrivateAction>
            <TeleportAction>
              <Position>
                <LanePosition roadId="4" laneId="-1" offset="1.0" s="48.58"/>
              </Position>
            </TeleportAction>
          </PrivateAction>
          <PrivateAction>
            <ControllerAction>
              <AssignControllerAction>
                <Controller name="HeroAgent">
                  <Properties>
                    <Property name="module" value="external_control"/>
                  </Properties>
                </Controller>
              </AssignControllerAction>
              <OverrideControllerValueAction>
                <Throttle value="0" active="false"/>
                <Brake value="0" active="false"/>
                <Clutch value="0" active="false"/>
                <ParkingBrake value="0" active="false"/>
                <SteeringWheel value="0" active="false"/>
                <Gear number="0" active="false"/>
              </OverrideControllerValueAction>
            </ControllerAction>
          </PrivateAction>
        </Private>
      </Actions>
    </Init>
    <Story name="MyStory">
      <Act name="Behavior">
        <ManeuverGroup name="ManeuverSequence" maximumExecutionCount="1">
          <Actors selectTriggeringEntities="false"/>
          <Maneuver name="WeatherChange">
            <Event name="WeatherChangeEvent" priority="overwrite">
              <Action name="WeatherChangeAction">
                <GlobalAction>
                  <EnvironmentAction>
                    <Environment name="Environment1">
                      <TimeOfDay animation="true" dateTime="2020-01-01T22:00:00"/>
                      <Weather cloudState="free">
                        <Sun intensity="0.05" azimuth="0" elevation="1.31"/>
                        <Fog visualRange="100000.0"/>
                        <Precipitation precipitationType="rain" intensity="0.9"/>
                      </Weather>
                      <RoadCondition frictionScaleFactor="1.0"/>
                    </Environment>
                  </EnvironmentAction>
                </GlobalAction>
              </Action>
              <StartTrigger>
                <ConditionGroup>
                  <Condition name="WeatherChangeStartTime" delay="0" conditionEdge="rising">
                    <ByValueCondition>
                      <SimulationTimeCondition value="20" rule="greaterThan"/>
                    </ByValueCondition>
                  </Condition>
                </ConditionGroup>
              </StartTrigger>
            </Event>
          </Maneuver>
          <Maneuver name="DummyManeuver">
            <Event name="DummyManeuver" priority="overwrite">
              <Action name="DummyManeuver"/>
              <StartTrigger>
                <ConditionGroup>
                  <Condition name="DummyManeuverStartTime" delay="0" conditionEdge="rising">
                    <ByValueCondition>
                      <SimulationTimeCondition value="10000000" rule="greaterThan"/>
                    </ByValueCondition>
                  </Condition>
                </ConditionGroup>
              </StartTrigger>
            </Event>
          </Maneuver>
        </ManeuverGroup>
        <StartTrigger>
          <ConditionGroup>
            <Condition name="StartTime" delay="0" conditionEdge="rising">
              <ByValueCondition>
                <SimulationTimeCondition value="5" rule="greaterThan"/>
              </ByValueCondition>
            </Condition>
          </ConditionGroup>
        </StartTrigger>
        <StopTrigger/>
      </Act>
    </Story>
    <StopTrigger/>
  </Storyboard>
</OpenSCENARIO>
