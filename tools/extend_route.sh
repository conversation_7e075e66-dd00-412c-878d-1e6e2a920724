#!/bin/bash

# Define parameters
INPUT_XML="leaderboard/data/bench2drive_extend/ours_train.xml"
BASE_OUTPUT_DIR="leaderboard/data/bench2drive_extend"
EXTENSION_LENGTHS=("250" "500" "750")  # For 0.5km, 1km, 1.5km extensions
INTERVAL=50
TOWNS=("Town01" "Town02" "Town03" "Town04" "Town05" "Town06" "Town07" "Town10HD" "Town11" "Town12")  # Add all towns you need

# Process each extension length
for length in "${EXTENSION_LENGTHS[@]}"; do
    # Calculate the output directory based on length
    if [ "$length" == "250" ]; then
        output_dir="${BASE_OUTPUT_DIR}/ours_train_extend_500_50"
    elif [ "$length" == "500" ]; then
        output_dir="${BASE_OUTPUT_DIR}/ours_train_extend_1000_50"
    elif [ "$length" == "750" ]; then
        output_dir="${BASE_OUTPUT_DIR}/ours_train_extend_1500_50"
    fi
    
    # Create output directory if it doesn't exist
    mkdir -p "$output_dir"
    
    # Process each town
    for town in "${TOWNS[@]}"; do
        output_file="${output_dir}/${town}_extended.xml"
        
        echo "Processing town $town with length $length..."
        python tools/extend_route_town.py \
            --input "$INPUT_XML" \
            --output "$output_file" \
            --length "$length" \
            --interval "$INTERVAL" \
            --town "$town"
        
        # Add a small delay between towns to allow Carla to shut down properly
        sleep 10
    done
    
    # Merge all town files into one final output
    final_output="${output_dir}/merged_extended.xml"
    echo "Merging all town files into $final_output..."
    
    # Create the base XML structure
    echo '<routes>' > "$final_output"
    
    # Append each town's routes (skipping the root <routes> tag)
    for town in "${TOWNS[@]}"; do
        town_file="${output_dir}/${town}_extended.xml"
        if [ -f "$town_file" ]; then
            # Remove the first and last lines (<routes> and </routes>)
            sed '1d;$d' "$town_file" >> "$final_output"
        fi
    done
    
    # Close the XML
    echo '</routes>' >> "$final_output"
    
    # Prettify the final merged XML
    xmllint --format "$final_output" -o "$final_output"
done

echo "All processing complete!"