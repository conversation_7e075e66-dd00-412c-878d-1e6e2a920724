import os
import json
import numpy as np
from scipy.signal import savgol_filter

def _phase_unwrap(angular_rates):
    """Unwrap angular rates to handle discontinuities."""
    return np.unwrap(angular_rates)

def compute_comfort_metric(
    acceleration,
    angular_velocity,
    forward_vector,
    right_vector,
    window_size: int = 7,
    poly_order: int = 2,
    deriv_order: int = 1,
    time_interval: float = 0.01
):
    """
    Compute and filter longitudinal and lateral acceleration components.
    
    Parameters:
        acceleration: List of acceleration vectors [ax, ay, az]
        angular_velocity: List of angular velocity vectors [wx, wy, wz]
        forward_vector: List of forward direction vectors
        right_vector: List of right direction vectors
        window_size: Window size for <PERSON><PERSON>tzky-<PERSON>lay filter
        poly_order: Polynomial order for <PERSON><PERSON>tzky-<PERSON>lay filter
        deriv_order: Derivative order for <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON> filter
        time_interval: Time between samples in seconds
    
    Returns:
        Dictionary containing filtered acceleration components and related metrics
    """
    window_size = min(window_size, len(acceleration))
    if not (poly_order < window_size):
        raise ValueError(f"{poly_order} < {window_size} does not hold!")
    
    # Convert to numpy arrays
    _2d_acceleration = np.array([[v[0], v[1]] for v in acceleration])
    _2d_forward_vector = np.array([[vec[0], vec[1]] for vec in forward_vector])
    _2d_right_vector = np.array([[vec[0], vec[1]] for vec in right_vector])
    _z_angular_rate = np.array([a[2] for a in angular_velocity])
    
    # Calculate raw acceleration components
    lon_acc = np.einsum('ij,ij->i', _2d_acceleration, _2d_forward_vector)
    lat_acc = np.einsum('ij,ij->i', _2d_acceleration, _2d_right_vector)
    magnitude_acc = np.hypot(_2d_acceleration[:, 0], _2d_acceleration[:, 1])
    
    # Process angular rates
    _z_yaw_rate = _phase_unwrap(_z_angular_rate)
    _z_yaw_acc = savgol_filter(
        _z_yaw_rate,
        polyorder=poly_order,
        window_length=window_size,
        deriv=deriv_order,
        delta=time_interval,
        axis=-1,
    )
    
    _z_yaw_rate = savgol_filter(
        _z_yaw_rate,
        polyorder=poly_order,
        window_length=window_size,
        axis=-1,
    )
    
    # Filter acceleration components
    lon_acc = savgol_filter(
        lon_acc,
        polyorder=poly_order,
        window_length=window_size,
        axis=-1,
    )
    
    lat_acc = savgol_filter(
        lat_acc,
        polyorder=poly_order,
        window_length=window_size,
        axis=-1,
    )
    
    magnitude_acc = savgol_filter(
        magnitude_acc,
        polyorder=poly_order,
        window_length=window_size,
        axis=-1,
    )
    
    return {
        'longitudinal_acceleration': lon_acc,
        'lateral_acceleration': lat_acc,
        'magnitude_acceleration': magnitude_acc,
        'yaw_rate': _z_yaw_rate,
        'yaw_acceleration': _z_yaw_acc
    }


def main():
    file_path = "/home/<USER>/Desktop/Evaluation/Bench2Drive/record/Town10HD_extended/hanxiao/087/20250510203739"
    file_name = "metric_info.json"
    # Load JSON data
    with open(os.path.join(file_path, file_name), 'r') as f:
        data = json.load(f)
    
    # Prepare lists for each parameter
    accelerations = []
    angular_velocities = []
    forward_vectors = []
    right_vectors = []
    
    # Extract data in order
    for key in sorted(data.keys(), key=lambda x: int(x)):
        frame = data[key]
        accelerations.append(frame['acceleration'])
        angular_velocities.append(frame['angular_velocity'])
        forward_vectors.append(frame['forward_vector'])
        right_vectors.append(frame['right_vector'])
    
    # Calculate and filter acceleration components
    results = compute_comfort_metric(
        accelerations,
        angular_velocities,
        forward_vectors,
        right_vectors
    )
    
    # Print results for each frame
    # print("Frame\tLongitudinal Acc\tLateral Acc\tMagnitude Acc\tYaw Rate\tYaw Acc")
    # for i in range(len(results['longitudinal_acceleration'])):
    #     print(f"{i}\t"
    #           f"{results['longitudinal_acceleration'][i]:.6f}\t"
    #           f"{results['lateral_acceleration'][i]:.6f}\t")
        # print(f"{results['magnitude_acceleration'][i]:.6f}\t"
        #       f"{results['yaw_rate'][i]:.6f}\t"
        #       f"{results['yaw_acceleration'][i]:.6f}")

    
    # 过滤绝对值小于0.1的加速度数据
    filtered_lon_acc = results['longitudinal_acceleration'][np.abs(results['longitudinal_acceleration']) >= 0.1]
    filtered_lat_acc = results['lateral_acceleration'][np.abs(results['lateral_acceleration']) >= 0.1]
    
    # 对横向加速度统计绝对值后的指标
    abs_lat_acc = np.abs(filtered_lat_acc)
    lat_stats = {
        'mean': np.mean(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan,
        'std': np.std(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan,
        'max': np.max(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan,
        'min': np.min(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan,
        'count': len(filtered_lat_acc)
    }
    
    # 对纵向加速度分别统计正值(加速)和负值(减速)
    lon_acc_pos = filtered_lon_acc[filtered_lon_acc > 0]  # 正值(加速)
    lon_acc_neg = filtered_lon_acc[filtered_lon_acc < 0]  # 负值(减速)
    
    lon_stats_pos = {
        'mean': np.mean(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan,
        'std': np.std(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan,
        'max': np.max(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan,
        'min': np.min(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan,
        'count': len(lon_acc_pos)
    }
    
    lon_stats_neg = {
        'mean': np.mean(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan,
        'std': np.std(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan,
        'max': np.max(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan,
        'min': np.min(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan,
        'count': len(lon_acc_neg)
    }
    
    print("\nFiltered Acceleration Statistics (|acc| >= 0.1):")
    
    print("\nLongitudinal Acceleration - Positive (Acceleration):")
    print(f"  Mean: {lon_stats_pos['mean']:.6f}")
    print(f"  Std: {lon_stats_pos['std']:.6f}")
    print(f"  Max: {lon_stats_pos['max']:.6f}")
    print(f"  Min: {lon_stats_pos['min']:.6f}")
    print(f"  Valid count: {lon_stats_pos['count']}/{len(results['longitudinal_acceleration'])}")
    
    print("\nLongitudinal Acceleration - Negative (Deceleration):")
    print(f"  Mean: {lon_stats_neg['mean']:.6f}")
    print(f"  Std: {lon_stats_neg['std']:.6f}")
    print(f"  Max: {lon_stats_neg['max']:.6f}")
    print(f"  Min: {lon_stats_neg['min']:.6f}")
    print(f"  Valid count: {lon_stats_neg['count']}/{len(results['longitudinal_acceleration'])}")
    
    print("\nLateral Acceleration (Absolute Values):")
    print(f"  Mean: {lat_stats['mean']:.6f}")
    print(f"  Std: {lat_stats['std']:.6f}")
    print(f"  Max: {lat_stats['max']:.6f}")
    print(f"  Min: {lat_stats['min']:.6f}")
    print(f"  Valid count: {lat_stats['count']}/{len(results['lateral_acceleration'])}")

if __name__ == "__main__":
    main()