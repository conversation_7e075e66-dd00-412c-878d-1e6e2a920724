import carla
import numpy as np
import xml.etree.ElementTree as ET
from xml.dom import minidom
import random
import os

import sys

try:
    sys.path.append("/home/<USER>/Desktop/Evaluation/Bench2Drive")
except IndexError:
    pass

from nav_utils.global_route_planner import GlobalRoutePlanner
from nav_utils.local_planner import LocalPlanner 


def interpolate_trajectory(waypoints_trajectory, map, world, hop_resolution=2.0):
    """
    Given some raw keypoints interpolate a full dense trajectory to be used by the user.
    returns the full interpolated route both in GPS coordinates and also in its original form.
    
    Args:
        - waypoints_trajectory: the current coarse trajectory
        - hop_resolution: distance between the trajectory's waypoints
    """

    grp = GlobalRoutePlanner(map, hop_resolution)
    # Obtain route plan

    route = []

    for i in range(len(waypoints_trajectory) - 1):

        waypoint = waypoints_trajectory[i]
        waypoint_next = waypoints_trajectory[i + 1]
        interpolated_trace = grp.trace_route(waypoint, waypoint_next)

        if len(interpolated_trace) > 120:
            return None

        route.extend(interpolated_trace)

    return route

def connect_to_carla():
    try:
        client = carla.Client('localhost', 2000)
        client.set_timeout(10.0)
        return client
    except Exception as e:
        print(f"can not connect to carla: {e}")
        return None

def get_random_start_point(map, num_points=1):
    all_waypoints = map.generate_waypoints(10.0) 
    sampled_points = random.sample(all_waypoints, min(num_points, len(all_waypoints)))
    return [wp.transform.location for wp in sampled_points]

def extend_route_forward(client, start_location, extend_distance=100, interval=50):
    world = client.get_world()
    map = world.get_map()
    
    extended_waypoints = []
    current_wp = map.get_waypoint(start_location)
    remaining_distance = extend_distance
    visited = set()
    
    while remaining_distance > 0 and current_wp.id not in visited:
        visited.add(current_wp.id)
        extended_waypoints.append(current_wp.transform.location)
        
        next_wps = current_wp.next(interval)  
        if not next_wps:
            break
            
        # current_wp = min(next_wps, 
        #                key=lambda wp: abs(wp.transform.rotation.yaw - current_wp.transform.rotation.yaw))
        current_wp = random.choice(next_wps)

        remaining_distance -= interval
    
    return extended_waypoints

def calculate_midpoint(waypoints):
    if not waypoints:
        return None
    
    mid_index = len(waypoints) // 2
    midpoint = waypoints[mid_index]
    
    # 计算平均朝向(yaw)
    if mid_index > 0:
        prev_point = waypoints[mid_index - 1]
        dx = midpoint.x - prev_point.x
        dy = midpoint.y - prev_point.y
        yaw = np.degrees(np.arctan2(dy, dx))
    else:
        yaw = 0.0
    
    return {
        'x': f"{midpoint.x:.1f}",
        'y': f"{midpoint.y:.1f}",
        'z': f"{midpoint.z:.1f}",
        'yaw': f"{yaw:.1f}"
    }

def prettify_xml(elem):
    rough_string = ET.tostring(elem, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    pretty_xml = reparsed.toprettyxml(indent="    ")
    
    lines = [line for line in pretty_xml.split('\n') if line.strip()]
    return '\n'.join(lines)

def generate_expanded_routes(input_xml, output_xml, num_routes=10, length=1000, town="Town10HD", interval=50):
    client = connect_to_carla()
    if client is None:
        return
    
    try:
        current_map = client.get_world().get_map()
        sim_world = client.get_world()
        current_town = current_map.name.split('/')[-1]  
        
        if current_town != town:  
            print(f"当前地图是 {current_town}，正在切换到 {town}...")
            world = client.load_world(town)
        else:
            print(f"当前已经是 {town} 地图，无需切换")
            world = client.get_world()
        
        map = world.get_map()
    except Exception as e:
        print(f"地图操作错误({town}): {e}")
        return
    
    # 从输入XML中提取天气配置
    input_tree = ET.parse(input_xml)
    weather_configs = []
    for weather in input_tree.findall('.//weather'):
        weather_configs.append({k: v for k, v in weather.items()})
    
    # 创建新的XML结构
    root = ET.Element('routes')
    
    # 生成指定数量的路线
    for i in range(1, num_routes + 1):
        max_attempts = 50
        attempt = 0
        success = False
        
        while attempt < max_attempts and not success:
            attempt += 1
            print(f"\n尝试生成路线 {i} (尝试 {attempt}/{max_attempts})")
            
            # 获取随机起点
            start_points = get_random_start_point(map)
            if not start_points:
                print("无法获取起点位置")
                continue
                
            start_loc = start_points[0]
            print(f"起点: {start_loc.x}, {start_loc.y}, {start_loc.z}")
            
            # 向前扩展指定长度
            waypoints = extend_route_forward(client, start_loc, length, interval)
            if not waypoints:
                print(f"路线扩展失败")
                continue

            nav_keypoint_list = waypoints

            global_plan = interpolate_trajectory(waypoints_trajectory=nav_keypoint_list,
                                                map=current_map,
                                                world=sim_world)
            if global_plan is None:
                print(f"global plan is None, 重试...")
                continue
            
            # 计算中间点作为触发点
            trigger_data = calculate_midpoint(waypoints)
            
            # 创建route元素
            route_elem = ET.SubElement(root, 'route', {'id': str(i), 'town': town})
            
            # 添加waypoints
            waypoints_elem = ET.SubElement(route_elem, 'waypoints')
            for loc in waypoints:
                ET.SubElement(waypoints_elem, 'position', {
                    'x': f"{loc.x:.1f}",
                    'y': f"{loc.y:.1f}",
                    'z': f"{loc.z:.1f}"
                })
            
            # 添加scenarios
            scenarios_elem = ET.SubElement(route_elem, 'scenarios')
            scenario_elem = ET.SubElement(scenarios_elem, 'scenario', {
                'name': 'None',
                'type': 'None'
            })
            ET.SubElement(scenario_elem, 'distance', {'value': '42'})
            ET.SubElement(scenario_elem, 'direction', {'value': 'right'})
            ET.SubElement(scenario_elem, 'speed', {'value': '53'})
            
            # 添加触发点
            if trigger_data:
                ET.SubElement(scenario_elem, 'trigger_point', {
                    'x': trigger_data['x'],
                    'y': trigger_data['y'],
                    'yaw': trigger_data['yaw'],
                    'z': trigger_data['z']
                })
            
            # 随机选择一个天气配置
            if weather_configs:
                weather_elem = ET.SubElement(route_elem, 'weathers')
                weather_data = random.choice(weather_configs)
                ET.SubElement(weather_elem, 'weather', weather_data)
            
            success = True
            print(f"路线 {i} 生成成功")
        
        if not success:
            print(f"警告: 无法生成路线 {i}，已达到最大尝试次数 {max_attempts}")
            # 删除最后添加的失败route（如果有）
            if len(root) > 0 and root[-1].get('id') == str(i):
                root.remove(root[-1])
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_xml), exist_ok=True)
    
    # 保存XML文件
    with open(output_xml, 'w') as f:
        f.write(prettify_xml(root))
    
    # 统计成功生成的路线数量
    successful_routes = len(root.findall('route'))
    print(f"\n生成完成，成功生成 {successful_routes}/{num_routes} 条路线")
    print(f"结果已保存到 {output_xml}")

if __name__ == "__main__":
    extend_town_list = ["Town03", "Town05", "Town10HD", "Town04"]
    # extend_town_list = ["Town10HD"]
    
    # 创建不同长度的路线
    for town in extend_town_list:
        input_xml = "leaderboard/data/bench2drive_extend/ours_train.xml"
        
        # 生成800米路线
        output_xml = f"leaderboard/data/ours/{town}_expanded_routes_800_50.xml"
        generate_expanded_routes(input_xml, output_xml, 10, 800, town, 50)
        
        # 生成1000米路线
        output_xml = f"leaderboard/data/ours/{town}_expanded_routes_1000_50.xml"
        generate_expanded_routes(input_xml, output_xml, 10, 1000, town, 50)
        
        # 生成1500米路线
        output_xml = f"leaderboard/data/ours/{town}_expanded_routes_1500_50.xml"
        generate_expanded_routes(input_xml, output_xml, 10, 1500, town, 50)