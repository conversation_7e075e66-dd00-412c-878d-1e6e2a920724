import gzip
import json
import os
# import matplotlib.pyplot as plt
import numpy as np
import math
import csv

from collections import defaultdict
from collections import deque


def read_json_gz(file_path):
    """读取并解压json.gz文件"""
    with gzip.open(file_path, 'rt', encoding='utf-8') as f:
        data = json.load(f)
    return data

root_dir = "/home/<USER>/Desktop/Evaluation/Bench2Drive/record/hx_short/041"
# root_dir = "/home/<USER>/Desktop/Bench2Drive_Offline/Bench2Drive/collect_data/hanxiao"
# root_dir ="D:/.1111孤独面店/test_data/folder1"


# folder_path = "D:/.1111孤独面店/test_data/20250421_094201_s_24_n_hanxiao_v_50_p_20/anno"


for dir_name in os.listdir(root_dir):
    folder_path = os.path.join(root_dir, dir_name, "anno")
    #file_path = os.path.join(root_dir, dir_name, "event.json")
    csv_path = os.path.join(root_dir, dir_name, "driving_statisics.csv")


    # 获取文件夹中所有的.json.gz文件
    json_gz_files = [f for f in os.listdir(folder_path) if f.endswith('.json.gz')][1250:1420]
    
    
    
    #print(len(json_gz_files))
    #2912
    ###########  ceshi  ########
    # prev_location = None
    # for filename in sorted(json_gz_files)[:80]:  
    #     file_path = os.path.join(folder_path, filename)
    #     try:
    #         data = read_json_gz(file_path)
            
    #         if 'bounding_boxes' in data and isinstance(data['bounding_boxes'], list) and len(data['bounding_boxes']) > 0:
    #             # 自车数据
    #             ego_box = data['bounding_boxes'][0]
    #             #ego_location = ego_box.get('location')
    #             current_location = ego_box.get('location')  # 假设 location 是 [x, y, z] 格式
            
    #         if prev_location is not None:
    #             # 计算位移变化（欧氏距离）
    #             print(filename, current_location)
    #             displacement = np.linalg.norm(np.array(current_location) - np.array(prev_location))
    #             print(f"位移变化（{filename}）: {displacement} 单位")
            
    #         prev_location = current_location  # 更新前一次的位置

    #     except Exception as e:
    #         print(f"Error processing {filename}: {str(e)}")

    # ##################


    # 用于跟踪车辆历史位置
    vehicle_history = defaultdict(list)

    # 统计超车次数
    overtake_count = 0

    distances_ahead =[]
    distances_behind =[]

    for filename in sorted(json_gz_files):  # 确保按时间顺序处理
        file_path = os.path.join(folder_path, filename)
        try:
            data = read_json_gz(file_path)

            if 'bounding_boxes' in data and isinstance(data['bounding_boxes'], list) and len(data['bounding_boxes']) > 0:
                # 自车数据
                ego_box = data['bounding_boxes'][0]
                ego_lane_id = ego_box.get('lane_id')
                ego_road_id = ego_box.get('road_id')
                ego_location = ego_box.get('location')
                ego_rotation = ego_box.get('rotation')
                
                current_frame_vehicles = {}  # 记录本帧车辆位置
                
                for box in data['bounding_boxes'][1:]:  # 跳过自车
                    if box.get('class') == 'vehicle':
                        vehicle_id = box.get('id')
                        box_lane_id = box.get('lane_id')
                        box_road_id = box.get('road_id')
                        box_distance = box.get('distance')
                        box_location = box.get('location') 

                        # 只关心同road的车辆
                        if box_road_id == ego_road_id:
                            # 计算相对位置
                            relative_position = "unknown"
                            if all(k in box for k in ['location', 'rotation']) and ego_location and ego_rotation:
                                try:
                                    #自车指向他车向量
                                    dx = box['location'][0] - ego_location[0]
                                    dy = box['location'][1] - ego_location[1]

                                    #yaw_rad = math.radians(ego_rotation[2])

                                    #他车偏航角
                                    rotation_box = box['rotation'][2]  # 类似原始代码的 ego_rotation[2]
                                    yaw_rad = math.radians(rotation_box)
                                    # 自车指向他车向量 在 他车前进方向上的投影
                                    x_local = dx * math.cos(yaw_rad) + dy * math.sin(yaw_rad)

                                    # angle_1 =                                   
                                    # angle_result = abs(box['rotation'][1] - ego_rotation[1])
                                    #delta_yaw = (angle_result+ 180) % 360 - 180 

                                    if x_local > 0:
                                        #distances_ahead.append(box_distance)
                                        relative_position = "ahead"
                                    elif x_local < 0:
                                        #distances_behind.append(box_distance)
                                        relative_position = "behind"
                                    else:
                                        relative_position = "same_position"
                                except (TypeError, IndexError):
                                    pass
                            
                            # 记录当前帧车辆信息
                            current_frame_vehicles[vehicle_id] = {
                                'position': relative_position,
                                'timestamp': filename,
                                'location': box_location
                            }
                
                # 检查历史记录中的超车行为
                for vehicle_id, history in list(vehicle_history.items()):
                    if vehicle_id in current_frame_vehicles:
                        current_pos = current_frame_vehicles[vehicle_id]['position']
                        current_location = current_frame_vehicles[vehicle_id]['location']

                        last_pos = history[-1]['position'] if history else None
                        last_location = history[-1]['location'] if history else None

                        time_1 = int(history[-1]['timestamp'].split('.')[0])
                        time_2 = int(current_frame_vehicles[vehicle_id]['timestamp'].split('.')[0])
                        time_result = time_2 - time_1

                        # 检测位置变化：之前在前方，现在在后方
                        # 对方位置有变化或许不够，得改成对方位置变化a小于多少
                        if last_pos == "ahead" and current_pos == "behind" and time_result < 5 and last_location != current_location:

                            overtake_count += 1
                            print(f"\n🚗 检测到超车! (Count: {overtake_count})")
                            print(f"Vehicle ID: {vehicle_id}")
                            print(f"Time: {history[-1]['timestamp']}   ->   {current_frame_vehicles[vehicle_id]['timestamp']}")
                            print(f"Position changed: {last_pos} -> {current_pos}")
                            # print(angle_result)
                            
                            # print(box['rotation'][1])
                            # print(ego_rotation[1])
                            # del vehicle_history[vehicle_id]
                
                # 更新车辆历史记录
                for vehicle_id, info in current_frame_vehicles.items():
                    vehicle_history[vehicle_id].append(info)
                    
                    # 限制历史记录长度（例如只保留最近5条记录）
                    if len(vehicle_history[vehicle_id]) > 5:
                        vehicle_history[vehicle_id].pop(0)
        
        except Exception as e:
            print(f"Error processing {filename}: {str(e)}")
            
    # if overtake_count == 0:
    #     distances_ahead = [0]
    #     distances_behind = [0]
        
    print(f"\n=== Final Overtake Count ===\nTotal overtakes: {overtake_count}")
    #print('变道前，自车与前车最近距离：',  min(distances_ahead))
    #print('变道后，自车与后方被超车辆距离：', min( distances_behind))
    print('#' * 50)
    print('\n')




    #######################################################################################
    
    # 求与前车的跟车距离

    #######################################################################################

    #all_ahead_distances = []
    #all_ahead_distances_0 = []
    time_interval = 0.05  # 时间间隔（秒）

    # 求速度
    position_history = deque(maxlen=2)          # 只保留最近两帧的位置
    position_history_2 = deque(maxlen=2)          # 只保留最近两帧的位置

    #vehicle_history = {}  

    ahead_distances = []    # 所有前车距离信息
    ahead_distances_0 = []
    all_speed = []

    for filename in sorted(json_gz_files):
        file_path = os.path.join(folder_path, filename)
        time_name = int(filename.split('.')[0])
        try:
            data = read_json_gz(file_path)
            
            # 先求自车位移用于求速度
            if 'x' in data and 'y' in data:
                current_x = data['x']
                current_y = data['y']
            
                # 存储当前帧位置
                position_history.append({'x': current_x, 'y': current_y, 'filename': filename})
            
                # 只有有上一帧数据时才计算速度
                if len(position_history) == 2:
                    prev = position_history[0]
                    curr = position_history[1]
                
                    # 计算位移（米）
                    dx = curr['x'] - prev['x']
                    dy = curr['y'] - prev['y']
                    displacement = (dx**2 + dy**2)**0.5  # 欧氏距离
                
                    # 计算速度（m/s）
                    speed_mps = displacement / time_interval
                
                    # 转换为km/h（1 m/s = 3.6 km/h）
                    speed_kmh = speed_mps * 3.6

                    if speed_kmh > 0.33:
                        print(speed_kmh)
                        all_speed.append(speed_kmh)


            if 'bounding_boxes' in data and isinstance(data['bounding_boxes'], list) and len(data['bounding_boxes']) > 0:
                # 自车数据（第一个bounding box是自车）
                ego_box = data['bounding_boxes'][0]
                print(time_name)
                print('\n')
                print("ego_box: ", ego_box)
                print('\n')
                ego_lane_id = ego_box.get('lane_id')
                ego_road_id = ego_box.get('road_id')
                ego_location = ego_box.get('location')
                ego_rotation = ego_box.get('rotation')
                
                # matching_vehicles = []  # 存储匹配车辆信息
                # closest_vehicle_speed = None


                min_distance = float('inf')  # 初始设为无穷大
                closest_box = None  # 存储距离最小的box
                # 找到这一帧离自己最近的车
                for box in data['bounding_boxes'][1:]:  # 跳过自车
                 ################################################################################   
                #     if box.get('class') == 'vehicle':
                #         has_vehicle = True
                #         break  # 只要找到一个 vehicle 就停止检查

                # if not has_vehicle:
                #     print(f"文件 {filename} 中没有检测到任何 vehicle 类别的 box")
                ###################


                    if box.get('class') == 'vehicle': 
                        print("周围有车：", time_name )  
                        print(time_name)
                        print('\n')
                        print("box: ", box) 
                        print('\n')
                        box_lane_id = box.get('lane_id')
                        box_road_id = box.get('road_id')
                        
                        # 检查是否在同一车道
                        if box_lane_id == ego_lane_id and box_road_id == ego_road_id:  
                            # print("同车道有车：", time_name ) 
                            if box is not None and all(k in box for k in ['location', 'rotation']) and  ego_location and ego_rotation:
                                try:
                                    dx = box['location'][0] - ego_location[0]
                                    dy = box['location'][1] - ego_location[1]
                                    yaw_rad = math.radians(ego_rotation[2])
                                    x_local = dx * math.cos(yaw_rad) + dy * math.sin(yaw_rad)
                                    
                                    if x_local > 0:
                                        distance = box.get('distance')
                                        if distance is not None and distance < min_distance:
                                            min_distance = distance
                                            closest_box = box  # 更新最近box
                                    # else:
                                    #     print("车在后面：", time_name )
                                except (TypeError, IndexError):
                                    pass
                            # else:
                            #     print("同车道无车：", time_name )
                        # else:
                        #     print("同车道无车*：", time_name )
                                    #####################################################################
                    # else:
                    #     print("周围无车：", time_name )



















                # if closest_box is not None:
                #     idd = closest_box.get('id')
                #     print(idd)
                # 计算前后关系
                #relative_position = "unknown"
                # if closest_box is None:
                #     print(f"No closest box found in {filename}")


                            
                # 求跟车时前车的速度
                if closest_box is not None:  # 确保存在最近车辆
                    current_x_veh = closest_box['location'][0]
                    current_y_veh = closest_box['location'][1]               

                    position_history_2.append({'x': current_x_veh, 'y': current_y_veh, 'filename': filename})
                    if len(position_history_2) == 2:
                        prev_2 = position_history_2[0]
                        curr_2 = position_history_2[1]

                        dx2 = curr_2['x'] - prev_2['x']
                        dy2 = curr_2['y'] - prev_2['y']
                        displacement_2 = (dx2**2 + dy2**2)**0.5  # 欧氏距离

                        speed_mps_2 = displacement_2 / time_interval
                        speed_kmh_2 = speed_mps_2 * 3.6
                        # # 求得距离最小的车辆的速度
                        # if distance < min_distance:
                        #     min_distance = distance
                        #     closest_vehicle_speed = speed_kmh_2

                        
                        speed_limit = 0.33
                        #print(closest_vehicle_speed)
                        #print(speed_kmh)

                        if speed_kmh >= speed_limit and speed_kmh_2 >= speed_limit:
                            print("动态:",time_name)
                            ahead_distances.append(min_distance)
                        elif speed_kmh < speed_limit and speed_kmh_2 < speed_limit:
                            print("静态:",time_name)
                            ahead_distances_0.append(min_distance)
                        else:
                            print("缓慢停车或前车启动: ", time_name)
                                

                                
                                # vehicle_info = {
                                #     'id': box.get('id'),
                                #     'distance': distance,
                                #     'location': box.get('location'),
                                #     'rotation': box.get('rotation'),
                                #     'relative_position': relative_position  # 存储位置关系
                                # }
                                # matching_vehicles.append(vehicle_info)
                # else:
                #     print("前方无车辆：", time_name)      

                # if closest_box is None:
                #     print("前方无车辆：", time_name) 


                # if ahead_distances:
                #     nearest_distance = min(ahead_distances)
                #     all_ahead_distances.append(nearest_distance)
                
                # if ahead_distances_0:
                #     nearest_distance_0 = min(ahead_distances_0)
                #     all_ahead_distances_0.append(nearest_distance_0)
            
                # 输出当前文件的结果（完全保留原有输出格式）
    #             if matching_vehicles:
    #                 print(f"\nFile: {filename}")
    #                 print(f"Ego Vehicle - Lane ID: {ego_lane_id} | Road ID: {ego_road_id}")
                    
    #                 for vehicle in matching_vehicles:
    #                     print(
    #                         f"Matching Vehicle - ID: {vehicle.get('id')} | "
    #                         f"Distance: {vehicle.get('distance')} | "
    #                         f"Position: {vehicle['relative_position']}"
    #                     )
                    
        except Exception as e:
            print(f"Error loading {filename}: {str(e)}")

    # 修改全局统计部分：只使用前车距离数据
    if ahead_distances:
        print("\n动态跟车距离数据\n")
        print(f"跟车帧数: {len(ahead_distances)}")
        print(f"均值: {sum(ahead_distances)/len(ahead_distances):.2f}")
        print(f"最大值: {max(ahead_distances):.2f}")
        print(f"最小值: {min(ahead_distances):.2f}")
        print(f"中位数: {np.median(ahead_distances):.2f}")
        print(f"标准差：{np.std(ahead_distances)}")
    else:
        print("\nNo valid ahead vehicle distances collected from any files.")

    if ahead_distances_0:
        print("\n静态跟车距离数据\n")
        print(f"跟车帧数: {len(ahead_distances_0)}")
        print(f"均值: {sum(ahead_distances_0)/len(ahead_distances_0):.2f}")
        print(f"最大值: {max(ahead_distances_0):.2f}")
        print(f"最小值: {min(ahead_distances_0):.2f}")
        print(f"中位数: {np.median(ahead_distances_0):.2f}")
        print(f"标准差: {np.std(ahead_distances_0):.2f}")
    
    else:
        print("\nNo valid ahead stop vehicle distances collected from any files.")       


    print('#' * 50)
    print('\n')


   



    # 求车辆的油门次数、最大和均值力度
    # 求车辆的刹车次数、最大和均值力度
    # 求车辆的变道次数

    # 油门相关统计
    throttle_events = 0          # 油门次数
    current_throttle_status = 0  # 当前是否处于油门状态（0=未踩油门）
    throttle_values = []         # 存储所有非零 throttle 值
    max_throttle = 0             # 最大油门值

    # 刹车相关统计
    brake_events = 0          # 刹车次数
    current_brake_status = 0  # 当前是否处于刹车状态（0=未刹车）
    brake_values = []         # 存储所有非零 brake 值
    max_brake = 0             # 最大刹车值

    # 变道相关统计
    lane_change_count = 0       # 有效变道次数
    prev_lane_id = None         # 前一帧的 lane_id
    prev_road_id = None         # 前一帧的 road_id


    for filename in sorted(json_gz_files):
        file_path = os.path.join(folder_path, filename)
        try:
            data = read_json_gz(file_path)
            
            ######## 获取油门值（直接从data顶层获取）
            throttle = data.get('throttle', 0)  
        
            if throttle > max_throttle:
                max_throttle = throttle            
        
            if throttle != 0:
                throttle_values.append(throttle)
                if current_throttle_status == 0:
                    throttle_events += 1
                    current_throttle_status = 1
            else:
                current_throttle_status = 0

            # 检查是否存在 bounding_boxes
            if 'bounding_boxes' in data and isinstance(data['bounding_boxes'], list) and len(data['bounding_boxes']) > 0:
                first_box = data['bounding_boxes'][0]  # 取第一个 bounding_box
                
                ######## 检查刹车
                brake = first_box.get('brake', 'brake_id not found') 

                if brake > max_brake:
                    max_brake = brake            
                
                if brake != 0:
                    brake_values.append(brake)  # 记录刹车值
                    # 如果之前未刹车，则开始新的刹车事件
                    if current_brake_status == 0:
                        brake_events += 1
                        current_brake_status = 1
                
                # 如果当前未刹车（brake == 0），重置状态
                else:
                    current_brake_status = 0    
                
                #########  检查边道
                current_lane_id = first_box.get('lane_id', None)
                current_road_id = first_box.get('road_id', None)
                
                # 如果是第一帧，仅更新前一帧数据，不进行比较
                if prev_lane_id is None:
                    prev_lane_id = current_lane_id
                    prev_road_id = current_road_id
                    continue
                
                # 检查是否满足有效变道条件
                if (current_lane_id != prev_lane_id) and (current_road_id == prev_road_id):
                    lane_change_count += 1
                    print(f"变道事件 #{lane_change_count} | 文件: {filename}")
                    print(f"  Lane ID: {prev_lane_id} -> {current_lane_id}")
                    print(f"  Road ID: {prev_road_id} (未变化)\n")
                
                # 更新前一帧数据
                prev_lane_id = current_lane_id
                prev_road_id = current_road_id
                
                #########  
                # lane_id = first_box.get('lane_id', 'lane_id not found') 
                # road_id = first_box.get('road_id', 'road_id not found')
                #########  

            else:
                print(f"File: {filename} | No valid bounding_boxes found")
                
        except Exception as e:
            print(f"Error loading {filename}: {str(e)}")
            
    # 计算均值
    mean_brake = np.mean(brake_values) if brake_values else np.nan
    mean_throttle = np.mean(throttle_values) if throttle_values else np.nan

    mean_speed = np.mean(all_speed) if all_speed else np.nan


    if max_brake == 0:
        max_brake =np.nan
    if brake_events == 0:
        brake_events =np.nan
    if lane_change_count == 0:
        lane_change_count =np.nan
    # 输出结果
    print('变道次数：', lane_change_count)
    print('#' * 50 + '\n')

    print("=== 刹车统计 ===")
    print(f"总刹车次数: {brake_events}")
    print(f"刹车均值力度: {mean_brake:.2f}")
    print(f"刹车最大力度: {max_brake:.2f}\n")
    # print(f"所有非零 brake 值: {brake_values}")       
    print('#' * 50 + '\n')


    ############################### speed #########################################







    # 将所有结果写入到csv

    if ahead_distances:
        mean_value = sum(ahead_distances) / len(ahead_distances)
        max_distance_1 = max(ahead_distances)
        min_distance_1 = min(ahead_distances)
        media_value = np.median(ahead_distances)
        std_value = np.std(ahead_distances)
    else:
        mean_value = np.nan  # 或 0，表示无数据
        max_distance_1 = np.nan   # 或 0，取决于你的需求
        min_distance_1 = np.nan 
        media_value = np.nan 
        std_value = np.nan 

    if ahead_distances_0:
        mean_value_0 = sum(ahead_distances_0) / len(ahead_distances_0)
        max_distance_1_0 = max(ahead_distances_0)
        min_distance_1_0 = min(ahead_distances_0)
        media_value_0 = np.median(ahead_distances_0)
        std_value_0 = np.std(ahead_distances_0)
    else:
        mean_value_0 = np.nan 
        max_distance_1_0 = np.nan 
        min_distance_1_0 = np.nan 
        media_value_0 = np.nan 
        std_value_0 = np.nan 

    row = {
        "超车次数": overtake_count,
        #"超车前与前车距离": min(distances_ahead),
        #"超车后与后车距离":  min(distances_behind),

        "跟车距离均值": mean_value,
        "跟车距离最大值": max_distance_1,
        "跟车距离最小值": min_distance_1,
        "跟车距离中位数": media_value,
        "跟车距离标准差": std_value,

        "静态跟车距离均值": mean_value_0,
        "静态距离最大值": max_distance_1_0,
        "静态距离最小值": min_distance_1_0,
        "静态距离中位数": media_value_0,
        "静态距离标准差":std_value_0,


        "变道次数": lane_change_count,
        "总油门次数": throttle_events,
        "油门均值力度": mean_throttle,
        "油门最大力度": max_throttle,
        "油门标准差":np.std(throttle_values),
        "总刹车次数": brake_events,
        "刹车均值": mean_brake,
        "刹车最大力度": max_brake,

        "速度均值": mean_speed,
        "速度最大值": max(all_speed),
        "速度最小值": min(all_speed),
        "速度标准差": np.std(all_speed)

        # "平均速度": mean_speed,
        # "最大速度": max_speed,
        # "0-5加速时间": acceleration,
        # "停车减速度": deceleration
    }

    # 将所有浮点数格式化为保留两位小数的字符串
    formatted_row = {}
    for key, value in row.items():
        if isinstance(value, float):
            formatted_row[key] = f"{value:.2f}"  # 保留两位小数，变成字符串
        else:
            formatted_row[key] = value  # 非 float 保留原样


    # 写入CSV文件
    with open(csv_path, mode="w", newline="") as file:  # 使用csv_path而不是硬编码的"data.csv"
        writer = csv.writer(file)
        writer.writerow(["label", "value"])
        for key, value in formatted_row.items():
            writer.writerow([key, value])

    print(f"文件已保存到: {csv_path}")
