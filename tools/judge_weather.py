import xml.etree.ElementTree as ET

def is_weather_consistent(weather_elements):
    """
    检查一组<weather>标签的天气参数是否完全一致（忽略route_percentage）
    """
    if not weather_elements:
        return True  # 无天气配置视为一致
    
    # 获取第一个weather标签的所有属性（排除route_percentage）
    first_weather = weather_elements[0]
    base_attrs = {k: v for k, v in first_weather.attrib.items() if k != 'route_percentage'}
    
    # 检查后续weather标签是否与第一个完全一致
    for weather in weather_elements[1:]:
        current_attrs = {k: v for k, v in weather.attrib.items() if k != 'route_percentage'}
        if current_attrs != base_attrs:
            return False
    return True

def check_routes_weather_consistency(xml_content):
    """
    解析XML并检查每个route的天气是否一致
    """
    try:
        root = ET.fromstring(xml_content)
        results = {}
        
        for route in root.findall('route'):
            route_id = route.get('id')
            weathers_elem = route.find('weathers')
            
            if weathers_elem is None:
                results[route_id] = True  # 无天气配置视为一致
                continue
                
            weather_elements = list(weathers_elem.iter('weather'))
            results[route_id] = is_weather_consistent(weather_elements)
        
        return results
    
    except ET.ParseError as e:
        print(f"XML解析错误: {e}")
        return None

# 从文件中读取XML内容
def read_xml_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read()

# 主程序
if __name__ == "__main__":
    # 假设XML文件名为routes.xml
    xml_file = '/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/bench2drive_extend/ours_test.xml'
    
    try:
        # 读取XML文件内容
        xml_content = read_xml_file(xml_file)
        
        # 检查天气一致性
        consistency_results = check_routes_weather_consistency(xml_content)
        
        if consistency_results is not None:
            for route_id, is_consistent in consistency_results.items():
                print(f"Route {route_id}: 天气{'一致' if is_consistent else '不一致'}")
        else:
            print("无法解析XML内容")
            
    except FileNotFoundError:
        print(f"错误：找不到文件 {xml_file}")
    except Exception as e:
        print(f"发生错误: {str(e)}")