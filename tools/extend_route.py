import carla
import numpy as np
import xml.etree.ElementTree as ET
from xml.dom import minidom
from lxml import etree
import random

import sys

try:
    sys.path.append("/home/<USER>/Desktop/Evaluation/Bench2Drive")
except IndexError:
    pass

from nav_utils.global_route_planner import GlobalRoutePlanner
from nav_utils.local_planner import LocalPlanner 
import os

import subprocess
import time


def interpolate_trajectory(waypoints_trajectory, map, world, hop_resolution=2.0):
    """
    Given some raw keypoints interpolate a full dense trajectory to be used by the user.
    returns the full interpolated route both in GPS coordinates and also in its original form.
    
    Args:
        - waypoints_trajectory: the current coarse trajectory
        - hop_resolution: distance between the trajectory's waypoints
    """

    grp = GlobalRoutePlanner(map, hop_resolution)
    # Obtain route plan

    route = []

    for i in range(len(waypoints_trajectory) - 1):

        waypoint = waypoints_trajectory[i]
        waypoint_next = waypoints_trajectory[i + 1]
        interpolated_trace = grp.trace_route(waypoint, waypoint_next)

        if len(interpolated_trace) > 120:
            return None

        route.extend(interpolated_trace)

    return route

def start_carla_server(carla_path="~/Desktop/carla0915/CarlaUE4.sh", port=2000, graphics_adapter=0):
    """启动Carla服务器并指定端口和图形适配器"""
    carla_path = os.path.expanduser(carla_path)  # 处理~扩展
    print(f"Starting Carla server from {carla_path} with port {port} and graphics adapter {graphics_adapter}")
    
    # 构建命令参数
    command = [
        carla_path,
        "-carla-rpc-port={}".format(port),
        "-graphicsadapter={}".format(graphics_adapter)
    ]
    
    # 使用nohup和&让进程在后台运行
    process = subprocess.Popen(command, 
                             stdout=subprocess.PIPE, 
                             stderr=subprocess.PIPE,
                             preexec_fn=os.setsid)
    time.sleep(30)  # 等待服务器启动
    return process

def connect_to_carla():
    try:
        client = carla.Client('localhost', 2000)
        client.set_timeout(300.0)
        return client
    except Exception as e:
        print(f"can not connect to carla server: {e}")
        return None
    
def stop_carla_server(process):
    print("Stopping Carla server")
    os.killpg(os.getpgid(process.pid), 15)  # 15是SIGTERM信号
    time.sleep(30)

def extend_route_waypoints(client, original_waypoints, extend_distance=1000, interval=10):
    world = client.get_world()
    map = world.get_map()
    
    original_locations = [
        carla.Location(x=loc.x, y=loc.y, z=loc.z) 
        for loc in original_waypoints
    ]
    
    extended_waypoints = []
    
    if extend_distance > 0:
        first_loc = original_locations[0]
        current_wp = map.get_waypoint(first_loc)
        
        remaining_distance = extend_distance
        visited = set()
        forward_points = []
        
        while remaining_distance > 0 and current_wp.id not in visited:
            visited.add(current_wp.id)
            previous_wps = current_wp.previous(interval)
            
            if not previous_wps:
                break
                
            # current_wp = min(previous_wps,
            #                key=lambda wp: abs(wp.transform.rotation.yaw - current_wp.transform.rotation.yaw))
            current_wp = random.choice(previous_wps)
            
            forward_points.append(current_wp.transform.location)
            remaining_distance -= interval
        
        extended_waypoints.extend(forward_points[::-1])
    
    extended_waypoints.extend(original_locations)  
    

    if extend_distance > 0:
        last_loc = original_locations[-1]
        current_wp = map.get_waypoint(last_loc)
        
        remaining_distance = extend_distance
        visited = set()
        
        while remaining_distance > 0 and current_wp.id not in visited:
            visited.add(current_wp.id)
            next_wps = current_wp.next(interval)
            
            if not next_wps:
                break
                
            # current_wp = min(next_wps,
            #                key=lambda wp: abs(wp.transform.rotation.yaw - current_wp.transform.rotation.yaw))
            current_wp = random.choice(next_wps)
            
            extended_waypoints.append(current_wp.transform.location)
            remaining_distance -= interval
    
    return extended_waypoints

def prettify_xml(elem):
    xml_str = ET.tostring(elem, encoding='utf-8')
    parser = etree.XMLParser(remove_blank_text=True)
    tree = etree.fromstring(xml_str, parser)
    
    etree.indent(tree, space="    ")
    return etree.tostring(tree, encoding='unicode', pretty_print=True)

def process_xml_routes(xml_file, output_file, length, interval):

    carla_process = None
    carla_process = start_carla_server()

    client = connect_to_carla()
    if client is None:
        return
    
    tree = ET.parse(xml_file)
    root = tree.getroot()

    routes_to_remove = []
    
    for route in root.findall('route'):
        town = route.get('town')
        print(f"process route {route.get('id')} in {town}")
        
        max_attempts = 50  
        attempt = 0
        success = False
        
        while attempt < max_attempts and not success:
            attempt += 1
            print(f"attempt {attempt}/{max_attempts}")
            
            try:
                current_map = client.get_world().get_map()
                sim_world = client.get_world()
                current_town = current_map.name.split('/')[-1] 
                
                if current_town != town:
                    # restart carla to avoid carla die
                    if carla_process is not None:
                        stop_carla_server(carla_process)
                        start_carla_server()
                        client = connect_to_carla()
                        sim_world = client.get_world()

                    print(f"current map is {current_town}, change to {town}...")
                    world = client.load_world(town)
                else:
                    print(f"current map is {town}")
                    world = client.get_world()
                
                map = world.get_map()
            except Exception as e:
                print(f"map error ({town}): {e}")
                continue
            
            waypoints_elem = route.find('waypoints')
            original_waypoints = [
                carla.Location(
                    x=float(pos.get('x')),
                    y=float(pos.get('y')),
                    z=float(pos.get('z'))
                ) for pos in waypoints_elem.findall('position')
            ]
            
            extended_waypoints = extend_route_waypoints(client, original_waypoints, length, interval)

            nav_keypoint_list = extended_waypoints

            global_plan = interpolate_trajectory(waypoints_trajectory=nav_keypoint_list,
                                                     map=current_map,
                                                     world=sim_world)
            if global_plan is None:
                print(f"global plan is None, process this route again (attempt {attempt}/{max_attempts})")
                continue
            
            success = True
            
            waypoints_elem.clear()
            for loc in extended_waypoints:
                ET.SubElement(waypoints_elem, 'position', {
                    'x': f"{loc.x:.1f}",
                    'y': f"{loc.y:.1f}",
                    'z': f"{loc.z:.1f}"
                })
        
        if not success:
            print(f"warning: can not process route {route.get('id')}, achieve max attempts")
            routes_to_remove.append(route)

    for route in routes_to_remove:
        root.remove(route)
    
    with open(output_file, 'w') as f:
        f.write(prettify_xml(root))
    print(f"done, save into {output_file}")

if __name__ == "__main__":
    # extend 0.5km
    input_xml = "leaderboard/data/bench2drive_extend/ours_train.xml"  
    output_xml = "leaderboard/data/bench2drive_extend/ours_train_extend_500_50.xml" 
    
    process_xml_routes(input_xml, output_xml, 250, 50)

    # extend 1km
    input_xml = "leaderboard/data/bench2drive_extend/ours_train.xml"  
    output_xml = "leaderboard/data/obench2drive_extend/ours_train_extend_1000_50.xml" 
    
    process_xml_routes(input_xml, output_xml, 500, 50)

    # extend 1.5km
    input_xml = "leaderboard/data/bench2drive_extend/ours_train.xml"  
    output_xml = "leaderboard/data/bench2drive_extend/ours_train_extend_1500_50.xml"  
    
    process_xml_routes(input_xml, output_xml, 750, 50)