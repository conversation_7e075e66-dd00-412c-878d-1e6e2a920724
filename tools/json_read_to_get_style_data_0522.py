import gzip
import json
import os
import numpy as np
import math
import csv
import pandas as pd
#from collections import defaultdict
from collections import deque
from scipy.signal import savgol_filter
from numpy.lib.stride_tricks import sliding_window_view
from scipy.signal import argrelextrema

def read_json_gz(file_path):
    """读取并解压json.gz文件"""
    with gzip.open(file_path, 'rt', encoding='utf-8') as f:
        data = json.load(f)
    return data

def calculate_sliding_max_mean(data, window_size=100):
    """
    计算滑动窗口最大值的均值
    
    参数:
        data: 输入数据数组
        window_size: 滑动窗口大小(默认5)
    
    返回:
        滑动窗口最大值的均值
    """
    if len(data) == 0:
        return np.nan
    
    # 确保窗口大小不超过数据长度
    window_size = min(window_size, len(data))
    
    # 使用滑动窗口视图(需要numpy 1.20.0+)
    try:
        sliding_windows = sliding_window_view(data, window_shape=window_size)
    except AttributeError:
        # 兼容旧版numpy
        sliding_windows = np.lib.stride_tricks.as_strided(
            data, 
            shape=(len(data) - window_size + 1, window_size),
            strides=(data.strides[0], data.strides[0])
        )
    
    # 计算每个窗口的最大值
    window_maxes = np.max(sliding_windows, axis=1)
    
    # 计算这些最大值的均值
    return np.mean(window_maxes) if len(window_maxes) > 0 else np.nan

def _phase_unwrap(angular_rates):
    """Unwrap angular rates to handle discontinuities."""
    return np.unwrap(angular_rates)

def compute_comfort_metric(
    acceleration,
    angular_velocity,
    forward_vector,
    right_vector,
    window_size: int = 7,
    poly_order: int = 2,
    deriv_order: int = 1,
    time_interval: float = 0.05
):
    """
    Compute and filter longitudinal and lateral acceleration components.
    
    Parameters:
        acceleration: List of acceleration vectors [ax, ay, az]
        angular_velocity: List of angular velocity vectors [wx, wy, wz]
        forward_vector: List of forward direction vectors
        right_vector: List of right direction vectors
        window_size: Window size for Savitzky-Golay filter
        poly_order: Polynomial order for Savitzky-Golay filter
        deriv_order: Derivative order for Savitzky-Golay filter
        time_interval: Time between samples in seconds
    
    Returns:
        Dictionary containing filtered acceleration components and related metrics
    """
    window_size = min(window_size, len(acceleration))
    if not (poly_order < window_size):
        raise ValueError(f"{poly_order} < {window_size} does not hold!")
    
    # Convert to numpy arrays
    _2d_acceleration = np.array([[v[0], v[1]] for v in acceleration])
    _2d_forward_vector = np.array([[vec[0], vec[1]] for vec in forward_vector])
    _2d_right_vector = np.array([[vec[0], vec[1]] for vec in right_vector])
    _z_angular_rate = np.array([a[2] for a in angular_velocity])
    
    # Calculate raw acceleration components
    lon_acc = np.einsum('ij,ij->i', _2d_acceleration, _2d_forward_vector)
    lat_acc = np.einsum('ij,ij->i', _2d_acceleration, _2d_right_vector)
    # magnitude_acc = np.hypot(_2d_acceleration[:, 0], _2d_acceleration[:, 1])
    
    # Process angular rates
    _z_yaw_rate = _phase_unwrap(_z_angular_rate)
    _z_yaw_acc = savgol_filter(
        _z_yaw_rate,
        polyorder=poly_order,
        window_length=window_size,
        deriv=deriv_order,
        delta=time_interval,
        axis=-1,
    )
    
    _z_yaw_rate = savgol_filter(
        _z_yaw_rate,
        polyorder=poly_order,
        window_length=window_size,
        axis=-1,
    )
    
    # Filter acceleration components
    lon_acc = savgol_filter(
        lon_acc,
        polyorder=poly_order,
        window_length=window_size,
        axis=-1,
    )
    
    lat_acc = savgol_filter(
        lat_acc,
        polyorder=poly_order,
        window_length=window_size,
        axis=-1,
    )
    
    # magnitude_acc = savgol_filter(
    #     magnitude_acc,
    #     polyorder=poly_order,
    #     window_length=window_size,
    #     axis=-1,
    # )


    dx = time_interval 
    # magnitude_jerk = savgol_filter(
    #     magnitude_acc,
    #     polyorder=poly_order,
    #     window_length=window_size,
    #     deriv=deriv_order,
    #     delta=dx,
    #     axis=-1,
    # )
    
    lon_jerk = savgol_filter(
        lon_acc,
        polyorder=poly_order,
        window_length=window_size,
        deriv=deriv_order,
        delta=dx,
        axis=-1,
    )    

    lat_jerk = savgol_filter(
        lat_acc,
        polyorder=poly_order,
        window_length=window_size,
        deriv=deriv_order,
        delta=dx,
        axis=-1,
    )   

    # # 求加加速度（Jerk)
    # lon_jerk = np.diff(lon_acc) / time_interval
    # lat_jerk = np.diff(lat_acc) / time_interval
    # lon_jerk = np.insert(lon_jerk, 0, 0)  # 第一帧Jerk设为0
    # lat_jerk = np.insert(lat_jerk, 0, 0)  # 第一帧Jerk设为0


    return {
        'longitudinal_acceleration': lon_acc,
        'lateral_acceleration': lat_acc,
        # 'magnitude_acceleration': magnitude_acc,
        'yaw_rate': _z_yaw_rate,
        'yaw_acceleration': _z_yaw_acc,
        'longitudinal_jerk': lon_jerk,
        'lateral_jerk': lat_jerk
    }

root_dir = "/home/<USER>/Desktop/Evaluation/Bench2Drive/record/Town04_extended"

# BASE_PATH = "/home/<USER>/Desktop/Evaluation/Bench2Drive/record/Town10HD_extended/liruiqin/088/20250510175226"
# BASE_PATH = "D:/.1111孤独面店/2_sh_tech/0515/Town04_extended/dongxr/108/20250509195313"

# folder_path = f"{BASE_PATH}/anno"
# file_path = BASE_PATH
# csv_path = os.path.join(BASE_PATH, "driving_statisics.csv")

for dir_name in os.listdir(root_dir):
    dir_path = os.path.join(root_dir, dir_name)

    for dir_name2 in os.listdir(dir_path):
        dir_path2 = os.path.join(dir_path, dir_name2)

        if dir_name2.startswith('.') or dir_name2.startswith('~') or dir_name2.endswith('.csv'):
            continue
        
        for sub_name in os.listdir(dir_path2):
            if sub_name.startswith('.') or sub_name.startswith('~') or sub_name.endswith('.csv'):
                continue

            folder_path = os.path.join(dir_path2, sub_name, "anno")
            file_path2 = os.path.join(dir_path2, sub_name)
            csv_path = os.path.join(dir_path2, sub_name, "driving_statisics.csv")

            # 获取文件夹中所有的.json.gz文件
            json_gz_files = sorted(
                [f for f in os.listdir(folder_path) if f.endswith('.json.gz')],
                key = lambda x: int(x.split('.')[0])
            )


            file_name = "metric_info.json"
            # Load JSON data
            with open(os.path.join(file_path2, file_name), 'r') as f:
                data = json.load(f)

            # Prepare lists for each parameter
            accelerations = []
            angular_velocities = []
            forward_vectors = []
            right_vectors = []

            # Extract data in order
            for key in sorted(data.keys(), key=lambda x: int(x)):
                frame = data[key]
                accelerations.append(frame['acceleration'])
                angular_velocities.append(frame['angular_velocity'])
                forward_vectors.append(frame['forward_vector'])
                right_vectors.append(frame['right_vector'])

            # Calculate and filter acceleration components
            results = compute_comfort_metric(
                accelerations,
                angular_velocities,
                forward_vectors,
                right_vectors
            )

            # Print results for each frame
            # print("Frame\tLongitudinal Acc\tLateral Acc\tMagnitude Acc\tYaw Rate\tYaw Acc")
            # for i in range(len(results['longitudinal_acceleration'])):
            #     print(f"{i}\t"
            #           f"{results['longitudinal_acceleration'][i]:.6f}\t"
            #           f"{results['lateral_acceleration'][i]:.6f}\t")
                # print(f"{results['magnitude_acceleration'][i]:.6f}\t"
                #       f"{results['yaw_rate'][i]:.6f}\t"
                #       f"{results['yaw_acceleration'][i]:.6f}")


            filtered_lon_acc = results['longitudinal_acceleration']
            filtered_lat_acc = results['lateral_acceleration']
            # filtered_lon_acc = results['longitudinal_acceleration'][np.abs(results['longitudinal_acceleration']) >= 0.1]
            # filtered_lat_acc = results['lateral_acceleration'][np.abs(results['lateral_acceleration']) >= 0.1]


            # 对横向加速度统计绝对值后的指标
            abs_lat_acc = np.abs(filtered_lat_acc)    # 绝对值

            # 检测局部极大值（峰值）
            peak_indices = argrelextrema(abs_lat_acc, np.greater)[0]
            peak_values = abs_lat_acc[peak_indices]

            # 找出最大的10个极值（降序排列）
            top_10_indices = np.argsort(peak_values)[-10:][::-1]  # 取最大的10个，并降序排列
            top_10_peaks = peak_values[top_10_indices]


            lat_median = np.median(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan
            lat_upper = abs_lat_acc[abs_lat_acc > lat_median] if len(abs_lat_acc) > 0 else np.array([])
            lat_lower = abs_lat_acc[abs_lat_acc <= lat_median] if len(abs_lat_acc) > 0 else np.array([])

            lat_stats = {
                'mean': np.mean(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan,
                'std': np.std(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan,
                'max': np.max(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan,
                'min': np.min(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan,
                'median': np.median(abs_lat_acc) if len(abs_lat_acc) > 0 else np.nan,

                'upper_half_mean': np.mean(lat_upper) if len(lat_upper) > 0 else np.nan,
                'lower_half_mean': np.mean(lat_lower) if len(lat_lower) > 0 else np.nan,

                'sliding_max_mean': calculate_sliding_max_mean(abs_lat_acc),
                'mean_peak_10': np.mean(top_10_peaks) if len(top_10_peaks) > 0 else np.nan,

                'count': len(filtered_lat_acc)
            }


            # 对纵向加速度分别统计正值(加速)和负值(减速)
            lon_acc_pos = filtered_lon_acc[filtered_lon_acc > 0]  # 正值(加速)
            lon_acc_neg = filtered_lon_acc[filtered_lon_acc < 0]  # 负值(减速)
            lon_acc_neg_abs = abs(lon_acc_neg)

            # 检测局部极大值（峰值）
            peak_indices_lon_p = argrelextrema(lon_acc_pos, np.greater)[0]
            peak_values_lon_p = lon_acc_pos[peak_indices_lon_p]

            # 找出最大的10个极值（降序排列）
            top_10_indices_lon_p = np.argsort(peak_values_lon_p)[-10:][::-1]  # 取最大的10个，并降序排列
            top_10_peaks_lon_p = peak_values_lon_p[top_10_indices_lon_p]

            pos_median = np.median(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan
            pos_upper = lon_acc_pos[lon_acc_pos > pos_median] if len(lon_acc_pos) > 0 else np.array([])
            pos_lower = lon_acc_pos[lon_acc_pos <= pos_median] if len(lon_acc_pos) > 0 else np.array([])

            lon_stats_pos = {
                'mean': np.mean(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan,
                'std': np.std(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan,
                'max': np.max(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan,
                'min': np.min(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan,
                'median': np.median(lon_acc_pos) if len(lon_acc_pos) > 0 else np.nan,

                'upper_half_mean': np.mean(pos_upper) if len(pos_upper) > 0 else np.nan,
                'lower_half_mean': np.mean(pos_lower) if len(pos_lower) > 0 else np.nan,

                'sliding_max_mean': calculate_sliding_max_mean(lon_acc_pos),
                'mean_peak_10': np.mean(top_10_peaks_lon_p) if len(top_10_peaks_lon_p) > 0 else np.nan,

                'count': len(lon_acc_pos)
            }


            # 检测局部极大值（峰值）
            peak_indices_lon_n = argrelextrema(lon_acc_neg_abs, np.greater)[0]
            peak_values_lon_n = lon_acc_neg_abs[peak_indices_lon_n]

            # 找出最大的10个极值（降序排列）
            top_10_indices_lon_n = np.argsort(peak_values_lon_n)[-10:][::-1]  # 取最大的10个，并降序排列
            top_10_peaks_lon_n = peak_values_lon_n[top_10_indices_lon_n]

            neg_median = np.median(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan
            neg_upper = lon_acc_neg[lon_acc_neg > neg_median] if len(lon_acc_neg) > 0 else np.array([])
            neg_lower = lon_acc_neg[lon_acc_neg <= neg_median] if len(lon_acc_neg) > 0 else np.array([])

            lon_stats_neg = {
                'mean': np.mean(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan,
                'std': np.std(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan,
                'max': np.max(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan,
                'min': np.min(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan,
                'median': np.median(lon_acc_neg) if len(lon_acc_neg) > 0 else np.nan,

                'upper_half_mean': np.mean(neg_upper) if len(neg_upper) > 0 else np.nan,
                'lower_half_mean': np.mean(neg_lower) if len(neg_lower) > 0 else np.nan,
                'sliding_max_mean': calculate_sliding_max_mean(abs(lon_acc_neg)) * -1,
                'mean_peak_10': np.mean(top_10_peaks_lon_n) if len(top_10_peaks_lon_n) > 0 else np.nan,
                'count': len(lon_acc_neg)
            }



            # # 过滤绝对值小于0.1的加速度数据
            # filtered_lon_acc_01 = results['longitudinal_acceleration'][np.abs(results['longitudinal_acceleration']) >= 0.3]
            # filtered_lat_acc_01 = results['lateral_acceleration'][np.abs(results['lateral_acceleration']) >= 0.3]
            # abs_lat_acc_01 = np.abs(filtered_lat_acc_01)    # 绝对值
            # lat_stats_01 = {
            #     'mean': np.mean(abs_lat_acc_01) if len(abs_lat_acc_01) > 0 else np.nan,
            #     'std': np.std(abs_lat_acc_01) if len(abs_lat_acc_01) > 0 else np.nan,
            #     'max': np.max(abs_lat_acc_01) if len(abs_lat_acc_01) > 0 else np.nan,
            #     'min': np.min(abs_lat_acc_01) if len(abs_lat_acc_01) > 0 else np.nan,
            #     'count': len(filtered_lat_acc_01)
            # }
            # lon_acc_pos_01 = filtered_lon_acc_01[filtered_lon_acc_01 > 0]  # 正值(加速)
            # lon_acc_neg_01 = filtered_lon_acc_01[filtered_lon_acc_01 < 0]  # 负值(减速)


            # lon_stats_pos_01 = {
            #     'mean': np.mean(lon_acc_pos_01) if len(lon_acc_pos_01) > 0 else np.nan,
            #     'std': np.std(lon_acc_pos_01) if len(lon_acc_pos_01) > 0 else np.nan,
            #     'max': np.max(lon_acc_pos_01) if len(lon_acc_pos_01) > 0 else np.nan,
            #     'min': np.min(lon_acc_pos_01) if len(lon_acc_pos_01) > 0 else np.nan,
            #     'count': len(lon_acc_pos_01)
            # }

            # lon_stats_neg_01 = {
            #     'mean': np.mean(lon_acc_neg_01) if len(lon_acc_neg_01) > 0 else np.nan,
            #     'std': np.std(lon_acc_neg_01) if len(lon_acc_neg_01) > 0 else np.nan,
            #     'max': np.max(lon_acc_neg_01) if len(lon_acc_neg_01) > 0 else np.nan,
            #     'min': np.min(lon_acc_neg_01) if len(lon_acc_neg_01) > 0 else np.nan,
            #     'count': len(lon_acc_neg_01)
            # }




            ###########  加加速度Jerk ###############

            filtered_lon_jerk = results['longitudinal_jerk'][np.abs(results['longitudinal_jerk']) >= 0.1]             
            filtered_lat_jerk = results['lateral_jerk'][np.abs(results['lateral_jerk']) >= 0.1]

            # 统计横向加加速度（绝对值）
            abs_lat_jerk = np.abs(filtered_lat_jerk)
            lat_jerk_stats = {
                'mean': np.mean(abs_lat_jerk) if len(abs_lat_jerk) > 0 else np.nan,
                'std': np.std(abs_lat_jerk) if len(abs_lat_jerk) > 0 else np.nan,
                'max': np.max(abs_lat_jerk) if len(abs_lat_jerk) > 0 else np.nan,
                'min': np.min(abs_lat_jerk) if len(abs_lat_jerk) > 0 else np.nan,
                'sliding_max_mean': calculate_sliding_max_mean(abs_lat_jerk),
                'count': len(filtered_lat_jerk)
            }

            # 统计纵向加加速度（分正负）
            lon_jerk_pos = filtered_lon_jerk[filtered_lon_jerk > 0]  # 正向加加速度
            lon_jerk_neg = filtered_lon_jerk[filtered_lon_jerk < 0]  # 负向加加速度

            lon_jerk_stats_pos = {
                'mean': np.mean(lon_jerk_pos) if len(lon_jerk_pos) > 0 else np.nan,
                'std': np.std(lon_jerk_pos) if len(lon_jerk_pos) > 0 else np.nan,
                'max': np.max(lon_jerk_pos) if len(lon_jerk_pos) > 0 else np.nan,
                'min': np.min(lon_jerk_pos) if len(lon_jerk_pos) > 0 else np.nan,
                'sliding_max_mean': calculate_sliding_max_mean(lon_jerk_pos),
                'count': len(lon_jerk_pos)
            }

            lon_jerk_stats_neg = {
                'mean': np.mean(lon_jerk_neg) if len(lon_jerk_neg) > 0 else np.nan,
                'std': np.std(lon_jerk_neg) if len(lon_jerk_neg) > 0 else np.nan,
                'max': np.max(lon_jerk_neg) if len(lon_jerk_neg) > 0 else np.nan,
                'min': np.min(lon_jerk_neg) if len(lon_jerk_neg) > 0 else np.nan,
                'sliding_max_mean': calculate_sliding_max_mean(abs(lon_jerk_neg)) * -1,
                'count': len(lon_jerk_neg)
            }



            # print("\nFiltered Acceleration Statistics (|acc| >= 0.1):")

            # print("\nLongitudinal Acceleration - Positive (Acceleration):")
            # print(f"  Mean: {lon_stats_pos['mean']:.6f}")
            # print(f"  Std: {lon_stats_pos['std']:.6f}")
            # print(f"  Max: {lon_stats_pos['max']:.6f}")
            # print(f"  Min: {lon_stats_pos['min']:.6f}")
            # print(f"  Valid count: {lon_stats_pos['count']}/{len(results['longitudinal_acceleration'])}")

            # print("\nLongitudinal Acceleration - Negative (Deceleration):")
            # print(f"  Mean: {lon_stats_neg['mean']:.6f}")
            # print(f"  Std: {lon_stats_neg['std']:.6f}")
            # print(f"  Max: {lon_stats_neg['max']:.6f}")
            # print(f"  Min: {lon_stats_neg['min']:.6f}")
            # print(f"  Valid count: {lon_stats_neg['count']}/{len(results['longitudinal_acceleration'])}")

            # print("\nLateral Acceleration (Absolute Values):")
            # print(f"  Mean: {lat_stats['mean']:.6f}")
            # print(f"  Std: {lat_stats['std']:.6f}")
            # print(f"  Max: {lat_stats['max']:.6f}")
            # print(f"  Min: {lat_stats['min']:.6f}")
            # print(f"  Valid count: {lat_stats['count']}/{len(results['lateral_acceleration'])}")


            ######################## 统计超车 ########################################

            # vehicle_history = defaultdict(list)     # 当访问字典中不存在的键时，自动为该键初始化一个默认值[]

            # 统计超车次数
            overtake_count = 0
            overtake_candidates = set()          # 待超车候选车辆集合（自动去重）
            last_ego_lane = None                 # 记录上一帧自车车道ID
            last_ego_road = None

            for filename in sorted(json_gz_files):  # 确保时间顺序处理
                file_path = os.path.join(folder_path, filename)
                try:
                    data = read_json_gz(file_path)
                    
                    # 获取自车数据
                    ego_box = data['bounding_boxes'][0]
                    ego_lane_id = ego_box.get('lane_id')
                    ego_road_id = ego_box.get('road_id')
                    ego_location = ego_box.get('location')
                    ego_rotation = ego_box.get('rotation')
                    
                    # ===== 1. 变道检测 =====
                    if (last_ego_lane is not None and 
                        ego_lane_id != last_ego_lane and 
                        last_ego_road is not None and
                        ego_road_id == last_ego_road):
                        print(filename)
                        
                        # 寻找旧车道前方最近车辆
                        closest_vehicle = None
                        min_distance = float('inf')
                        
                        for box in data['bounding_boxes'][1:]:
                            if (box.get('class') == 'vehicle' and
                                box.get('state') == 'dynamic' and
                                box.get('road_id') == ego_road_id and
                                box.get('lane_id') == last_ego_lane and
                                box.get('location')):
                                
                                # 计算距离和相对位置
                                dx = box['location'][0] - ego_location[0]
                                dy = box['location'][1] - ego_location[1]
                                distance = box.get('distance')
                                
                                if 'rotation' in box:
                                    yaw_rad = math.radians(box['rotation'][2])
                                    x_local = dx * math.cos(yaw_rad) + dy * math.sin(yaw_rad)
                                    
                                    # 必须满足：同车道 + 在前方 + 距离最近
                                    if x_local > 0 and distance < min_distance:
                                        min_distance = distance
                                        closest_vehicle = box.get('id')
                        
                        if closest_vehicle:
                            overtake_candidates.add(closest_vehicle)
                            print(f"🚩 跟踪新车：{closest_vehicle}（变道自车道 {last_ego_lane}）")
                            # print(ego_lane_id)
                    last_ego_lane = ego_lane_id  # 更新车道记录
                    last_ego_road = ego_road_id  # 更新车道记录



                    # ===== 2. 超车判定 =====
                    current_positions = {}  # 本帧车辆位置状态
                    current_road = {}
                    for box in data['bounding_boxes'][1:]:
                        if box.get('class') == 'vehicle' and box.get('state') == 'dynamic' :
                            vehicle_id = box.get('id')

                            if vehicle_id not in overtake_candidates:
                                continue  # 关键优化：只处理跟踪列表中的车辆
                        
                            # 计算相对位置
                            relative_position = "unknown"
                            if all(k in box for k in ['location', 'rotation']) and ego_location and ego_rotation:
                                dx = box['location'][0] - ego_location[0]
                                dy = box['location'][1] - ego_location[1]
                                yaw_rad = math.radians(box['rotation'][2])
                                x_local = dx * math.cos(yaw_rad) + dy * math.sin(yaw_rad)
                                
                                if x_local > 0:
                                    relative_position = "ahead"
                                elif x_local < 0:
                                    relative_position = "behind"
                            
                            current_positions[vehicle_id] = relative_position
                            current_road[vehicle_id] = box.get('road_id')
                    
                    # 检查超车完成
                    for vehicle_id in list(overtake_candidates):
                        if vehicle_id in current_positions:
                            if current_positions[vehicle_id] == "behind" and current_road[vehicle_id] == ego_road_id:
                                overtake_count += 1
                                # print
                                print(f"\n🚗 超车成功 #{overtake_count}")
                                print(filename)
                                print(f"目标车辆: {vehicle_id}")
                                print(f"当前车道: {ego_lane_id}")
                                overtake_candidates.remove(vehicle_id)
                        else:
                            overtake_candidates.remove(vehicle_id)  # 车辆消失

                except Exception as e:
                    print(f"Error processing {filename}: {str(e)}")

            print(f"\n🔥 最终超车次数: {overtake_count}")


            # distances_ahead =[]
            # distances_behind =[]

            # for filename in sorted(json_gz_files):  # sorted  确保按时间顺序处理
            #     file_path = os.path.join(folder_path, filename)
            #     try:
            #         data = read_json_gz(file_path)

            #         if 'bounding_boxes' in data and isinstance(data['bounding_boxes'], list) and len(data['bounding_boxes']) > 0:
            #             # 自车数据
            #             ego_box = data['bounding_boxes'][0]
            #             ego_lane_id = ego_box.get('lane_id')
            #             ego_road_id = ego_box.get('road_id')
            #             ego_location = ego_box.get('location')
            #             ego_rotation = ego_box.get('rotation')
                        
            #             current_frame_vehicles = {}  # 记录本帧车辆位置
            #             closest_vehicle = None      # 记录本帧最近的同车道车辆
            #             min_distance = float('inf') # 初始最小距离

            #             for box in data['bounding_boxes'][1:]:  # 跳过自车
            #                 if box.get('class') == 'vehicle':
            #                     vehicle_id = box.get('id')
            #                     box_lane_id = box.get('lane_id')
            #                     box_road_id = box.get('road_id')
            #                     box_distance = box.get('distance')
            #                     box_location = box.get('location')

            #                     # 只关心同road的车辆
            #                     if box_road_id == ego_road_id and box_lane_id == ego_lane_id:

            #                         if all(k in box for k in ['location', 'rotation']) and ego_location and ego_rotation:
            #                             try:
            #                                 #自车指向他车向量
            #                                 dx = box['location'][0] - ego_location[0]
            #                                 dy = box['location'][1] - ego_location[1]

            #                                 #他车偏航角
            #                                 rotation_box = box['rotation'][2]  # 类似原始代码的 ego_rotation[2]
            #                                 yaw_rad = math.radians(rotation_box)

            #                                 # 自车指向他车向量 在 他车前进方向上的投影
            #                                 x_local = dx * math.cos(yaw_rad) + dy * math.sin(yaw_rad)


            #                                 if x_local > 0 and box_distance < min_distance:
            #                                     min_distance = distance
            #                                     closest_vehicle = {
            #                                     'id': vehicle_id,
            #                                     'distance': distance,
            #                                     'position': "ahead",
            #                                     'timestamp': filename,
            #                                     'location': box_location
            #                                 }
                    
            #                             except (TypeError, IndexError):                 # 找到了在我前面最近的车,得到closeset vehicle
            #                                 pass
                        
            #             if closest_vehicle:
            #                 vehicle_id = closest_vehicle['id']
            #                 current_frame_vehicles[vehicle_id] = closest_vehicle  # ​将本帧检测到的最近车辆添加到当前帧车辆记录中​​，并以vehicle_id作为键名存储。 

            #                 # 检查历史记录中的超车行为
            #                 if vehicle_id in vehicle_history:           # 检查该车辆是否被跟踪过  
            #                     history = vehicle_history[vehicle_id]   # 获取该车辆的全部历史记录（列表）
            #                     if history:  # 确保有历史记录
            #                         last_record = history[-1]           # 取出最新的一条记录


            #                         time_1 = int(last_record['timestamp'].split('.')[0])
            #                         time_2 = int(filename.split('.')[0])
            #                         time_diff = time_2 - time_1


            #                         if (last_record['position'] == "ahead" and 
            #                             closest_vehicle['position'] == "behind" and 
            #                             last_location != current_location and
            #                             time_diff < 5):
                                            
            #                             overtake_count += 1
            #                             print(f"\n🚗 检测到超车! (Count: {overtake_count})")
            #                             print(f"Vehicle ID: {vehicle_id}")
            #                             print(f"Time: {last_record['timestamp']} -> {filename}")
            #                             print(f"Distance: {last_record['distance']:.2f}m -> {closest_vehicle['distance']:.2f}m")



            #             # 更新车辆历史记录（只记录最近的同车道前车）
            #             for vehicle_id, info in current_frame_vehicles.items():
            #                 vehicle_history[vehicle_id].append(info)
                            
            #                 # 限制历史记录长度
            #                 if len(vehicle_history[vehicle_id]) > 5:  # 调整为5条
            #                     vehicle_history[vehicle_id].pop(0)



            #                         # 记录当前帧车辆信息
            #                         current_frame_vehicles[vehicle_id] = {
            #                             'position': relative_position,
            #                             'timestamp': filename,
            #                             'location': box_location
            #                         }
                        
            #             # 检查历史记录中的超车行为
            #             for vehicle_id, history in list(vehicle_history.items()):
            #                 if vehicle_id in current_frame_vehicles:
            #                     current_pos = current_frame_vehicles[vehicle_id]['position']
            #                     current_location = current_frame_vehicles[vehicle_id]['location']

            #                     last_pos = history[-1]['position'] if history else None
            #                     last_location = history[-1]['location'] if history else None

            #                     time_1 = int(history[-1]['timestamp'].split('.')[0])
            #                     time_2 = int(current_frame_vehicles[vehicle_id]['timestamp'].split('.')[0])
            #                     time_result = time_2 - time_1

            #                     # 检测位置变化：之前在前方，现在在后方
            #                     # 对方位置有变化或许不够，得改成对方位置变化a小于多少
            #                     if last_pos == "ahead" and current_pos == "behind" and time_result < 5 and last_location != current_location:

            #                         overtake_count += 1
            #                         print(f"\n🚗 检测到超车! (Count: {overtake_count})")
            #                         print(f"Vehicle ID: {vehicle_id}")
            #                         print(f"Time: {history[-1]['timestamp']}   ->   {current_frame_vehicles[vehicle_id]['timestamp']}")
            #                         print(f"Position changed: {last_pos} -> {current_pos}")
            #                         # print(angle_result)
                                    
            #                         # print(box['rotation'][1])
            #                         # print(ego_rotation[1])
            #                         # del vehicle_history[vehicle_id]
                        
            #             # 更新车辆历史记录
            #             for vehicle_id, info in current_frame_vehicles.items():
            #                 vehicle_history[vehicle_id].append(info)
                            
            #                 # 限制历史记录长度（例如只保留最近5条记录）
            #                 if len(vehicle_history[vehicle_id]) > 20:
            #                     vehicle_history[vehicle_id].pop(0)
                
            #     except Exception as e:
            #         print(f"Error processing {filename}: {str(e)}")
                    
            # if overtake_count == 0:
            #     distances_ahead = [0]
            #     distances_behind = [0]
                
            print(f"\n=== Final Overtake Count ===\nTotal overtakes: {overtake_count}")
            #print('变道前，自车与前车最近距离：',  min(distances_ahead))
            #print('变道后，自车与后方被超车辆距离：', min( distances_behind))
            print('#' * 50)
            print('\n')


            #######################################################################################

            # 求与前车的跟车距离

            #######################################################################################

            #all_ahead_distances = []
            #all_ahead_distances_0 = []
            time_interval = 0.1  # 时间间隔（秒）

            # 求速度
            position_history = deque(maxlen=2)          # 只保留最近两帧的位置
            position_history_2 = deque(maxlen=2)          # 只保留最近两帧的位置

            #vehicle_history = {}  

            ahead_distances = []    # 所有前车距离信息
            ahead_distances_0 = []
            all_thw = []
            all_ttc = []
            all_ttci = []



            all_speed = []

            for filename in sorted(json_gz_files):
                file_path = os.path.join(folder_path, filename)
                time_name = int(filename.split('.')[0])
                try:
                    data = read_json_gz(file_path)
                    
                    # 先求自车位移用于求速度
                    if 'x' in data and 'y' in data:
                        current_x = data['x']
                        current_y = data['y']
                    
                        # 存储当前帧位置
                        position_history.append({'x': current_x, 'y': current_y, 'filename': filename})
                    
                        # 只有有上一帧数据时才计算速度
                        if len(position_history) == 2:
                            prev = position_history[0]
                            curr = position_history[1]
                        
                            # 计算位移（米）
                            dx = curr['x'] - prev['x']
                            dy = curr['y'] - prev['y']
                            displacement = (dx**2 + dy**2)**0.5  # 欧氏距离
                        
                            # 计算速度（m/s）
                            speed_mps = displacement / time_interval
                        
                            # 转换为km/h（1 m/s = 3.6 km/h）
                            speed_kmh = speed_mps * 3.6

                            if speed_kmh > 0.66:
                                #print(speed_kmh)
                                all_speed.append(speed_kmh)


                    if 'bounding_boxes' in data and isinstance(data['bounding_boxes'], list) and len(data['bounding_boxes']) > 0:
                        # 自车数据（第一个bounding box是自车）
                        ego_box = data['bounding_boxes'][0]
                        
                        ego_lane_id = ego_box.get('lane_id')
                        ego_road_id = ego_box.get('road_id')
                        ego_location = ego_box.get('location')
                        ego_rotation = ego_box.get('rotation')
                        
                        # matching_vehicles = []  # 存储匹配车辆信息
                        # closest_vehicle_speed = None

                        min_distance = float('inf')  # 初始设为无穷大
                        closest_box = None  # 存储距离最小的box
                        # 找到这一帧离自己最近的车
                        for box in data['bounding_boxes'][1:]:  # 跳过自车

                            if box.get('class') == 'vehicle' and box.get('state') == 'dynamic' : 
                                #print("周围有车：", time_name )  
                                # print(time_name)
                                # print('\n')
                                # print("box: ", box) 
                                # print('\n')
                                box_lane_id = box.get('lane_id')
                                box_road_id = box.get('road_id')
                                
                                # 检查是否在同一车道
                                if box_lane_id == ego_lane_id and box_road_id == ego_road_id:  
                                    # print("同车道有车：", time_name ) 
                                    if box is not None and all(k in box for k in ['location', 'rotation']) and  ego_location and ego_rotation:
                                        try:
                                            dx = box['location'][0] - ego_location[0]
                                            dy = box['location'][1] - ego_location[1]
                                            yaw_rad = math.radians(ego_rotation[2])
                                            x_local = dx * math.cos(yaw_rad) + dy * math.sin(yaw_rad)
                                            
                                            if x_local > 0:
                                                distance = box.get('distance')
                                                if distance is not None and distance < min_distance:
                                                    min_distance = distance
                                                    closest_box = box  # 更新最近box
                                            # else:
                                            #     print("车在后面：", time_name )
                                        except (TypeError, IndexError):
                                            pass
                                    # else:
                                    #     print("同车道无车：", time_name )
                                # else:
                                #     print("同车道无车*：", time_name )
                                            #####################################################################
                            # else:
                            #     print("周围无车：", time_name )



                        # if closest_box is not None:
                        #     idd = closest_box.get('id')
                        #     print(idd)
                        # 计算前后关系
                        #relative_position = "unknown"
                        # if closest_box is None:
                        #     print(f"No closest box found in {filename}")


                                    
                        # 求跟车时前车的速度
                        if closest_box is not None:  # 确保存在最近车辆
                            current_x_veh = closest_box['location'][0]
                            current_y_veh = closest_box['location'][1]               

                            position_history_2.append({'x': current_x_veh, 'y': current_y_veh, 'filename': filename})
                            if len(position_history_2) == 2:
                                prev_2 = position_history_2[0]
                                curr_2 = position_history_2[1]

                                dx2 = curr_2['x'] - prev_2['x']
                                dy2 = curr_2['y'] - prev_2['y']
                                displacement_2 = (dx2**2 + dy2**2)**0.5  # 欧氏距离

                                speed_mps_2 = displacement_2 / time_interval
                                speed_kmh_2 = speed_mps_2 * 3.6
                                
                                speed_limit = 0.66

                                if speed_kmh >= speed_limit and speed_kmh_2 >= speed_limit:
                                    # print("动态:",time_name)
                                    ahead_distances.append(min_distance)                # 动态跟车时的距离  还要整个列表相除得到想要的值

                                    thw = min_distance / speed_kmh
                                    ttc = min_distance / (speed_kmh - speed_kmh_2)
                                    ttci  = 1 / ttc
                                    all_thw.append(thw)
                                    all_ttc.append(ttc)
                                    all_ttci.append(ttci)

                                elif speed_kmh < speed_limit and speed_kmh_2 < speed_limit:
                                    # print("静态:",time_name)
                                    ahead_distances_0.append(min_distance)
                                # else:
                                    # print("缓慢停车或前车启动: ", time_name)
                                        

                                        
                                        # vehicle_info = {
                                        #     'id': box.get('id'),
                                        #     'distance': distance,
                                        #     'location': box.get('location'),
                                        #     'rotation': box.get('rotation'),
                                        #     'relative_position': relative_position  # 存储位置关系
                                        # }
                                        # matching_vehicles.append(vehicle_info)
                        # else:
                        #     print("前方无车辆：", time_name)      

                        # if closest_box is None:
                        #     print("前方无车辆：", time_name) 


                        # if ahead_distances:
                        #     nearest_distance = min(ahead_distances)
                        #     all_ahead_distances.append(nearest_distance)
                        
                        # if ahead_distances_0:
                        #     nearest_distance_0 = min(ahead_distances_0)
                        #     all_ahead_distances_0.append(nearest_distance_0)
                    
                        # 输出当前文件的结果（完全保留原有输出格式）
            #             if matching_vehicles:
            #                 print(f"\nFile: {filename}")
            #                 print(f"Ego Vehicle - Lane ID: {ego_lane_id} | Road ID: {ego_road_id}")
                            
            #                 for vehicle in matching_vehicles:
            #                     print(
            #                         f"Matching Vehicle - ID: {vehicle.get('id')} | "
            #                         f"Distance: {vehicle.get('distance')} | "
            #                         f"Position: {vehicle['relative_position']}"
            #                     )
                            
                except Exception as e:
                    print(f"Error loading {filename}: {str(e)}")

            # 修改全局统计部分：只使用前车距离数据
            if ahead_distances:
                print("\n动态跟车距离数据\n")
                print(f"跟车帧数: {len(ahead_distances)}")
                print(f"均值: {sum(ahead_distances)/len(ahead_distances):.2f}")
                print(f"最大值: {max(ahead_distances):.2f}")
                print(f"最小值: {min(ahead_distances):.2f}")
                print(f"中位数: {np.median(ahead_distances):.2f}")
                print(f"标准差：{np.std(ahead_distances)}")
            else:
                print("\nNo valid ahead vehicle distances collected from any files.")

            if ahead_distances_0:
                print("\n静态跟车距离数据\n")
                print(f"跟车帧数: {len(ahead_distances_0)}")
                print(f"均值: {sum(ahead_distances_0)/len(ahead_distances_0):.2f}")
                print(f"最大值: {max(ahead_distances_0):.2f}")
                print(f"最小值: {min(ahead_distances_0):.2f}")
                print(f"中位数: {np.median(ahead_distances_0):.2f}")
                print(f"标准差: {np.std(ahead_distances_0):.2f}")

            else:
                print("\nNo valid ahead stop vehicle distances collected from any files.")       


            print('#' * 50)
            print('\n')





            # 求车辆的油门次数、最大和均值力度
            # 求车辆的刹车次数、最大和均值力度
            # 求车辆的变道次数

            # 油门相关统计
            throttle_events = 0          # 油门次数
            current_throttle_status = 0  # 当前是否处于油门状态（0=未踩油门）
            throttle_values = []         # 存储所有非零 throttle 值
            max_throttle = 0             # 最大油门值

            # 刹车相关统计
            brake_events = 0          # 刹车次数
            current_brake_status = 0  # 当前是否处于刹车状态（0=未刹车）
            brake_values = []         # 存储所有非零 brake 值
            max_brake = 0             # 最大刹车值

            # 变道相关统计
            lane_change_count = 0       # 有效变道次数
            MIN_CONSISTENT_FRAMES = 5  # 需要连续保持在新车道的最小帧数
            potential_lane_change_frames = 0  # 记录潜在变道的连续帧数

            potential_new_lane_id = None  # 记录潜在的新车道ID
            confirmed_prev_lane_id = None  # 确认的上一车道ID

            prev_road_id = None  # 前一帧的道路ID


            for filename in sorted(json_gz_files):
                file_path = os.path.join(folder_path, filename)
                try:
                    data = read_json_gz(file_path)
                    
                    ######## 获取油门值（直接从data顶层获取）
                    throttle = data.get('throttle', 0)  
                
                    if throttle > max_throttle:
                        max_throttle = throttle            
                
                    if throttle != 0:
                        throttle_values.append(throttle)
                        if current_throttle_status == 0:
                            throttle_events += 1
                            current_throttle_status = 1
                    else:
                        current_throttle_status = 0

                    # 检查是否存在 bounding_boxes
                    if 'bounding_boxes' in data and isinstance(data['bounding_boxes'], list) and len(data['bounding_boxes']) > 0:
                        first_box = data['bounding_boxes'][0]  # 取第一个 bounding_box
                        
                        ######## 检查刹车  #########
                        brake = first_box.get('brake', 'brake_id not found') 

                        if brake > max_brake:
                            max_brake = brake            
                        
                        if brake != 0:
                            brake_values.append(brake)  # 记录刹车值
                            # 如果之前未刹车，则开始新的刹车事件
                            if current_brake_status == 0:
                                brake_events += 1
                                current_brake_status = 1
                        
                        # 如果当前未刹车（brake == 0），重置状态
                        else:
                            current_brake_status = 0    
                        


                        #########  检查变道  #########
                        current_lane_id = first_box.get('lane_id', None)
                        current_road_id = first_box.get('road_id', None)
                        
                        # 如果是第一帧，仅更新前一帧数据，不进行比较
                        if confirmed_prev_lane_id is None:
                            confirmed_prev_lane_id = current_lane_id
                            prev_road_id = current_road_id
                            continue
                        
                        # 检查是否满足有效变道条件
                        if (current_lane_id != confirmed_prev_lane_id) and (current_road_id == prev_road_id):

                            if potential_new_lane_id != current_lane_id:
                                potential_new_lane_id = current_lane_id
                                potential_lane_change_frames = 1
                            else:
                                potential_lane_change_frames += 1


                            # 如果连续保持在新车道足够帧数，确认变道
                            if potential_lane_change_frames >= MIN_CONSISTENT_FRAMES:
                                lane_change_count += 1
                                print(f"变道事件 #{lane_change_count} | 文件: {filename}")
                                print(f"  Lane ID: {confirmed_prev_lane_id} -> {current_lane_id}")
                                print(f"  Road ID: {prev_road_id} (未变化)\n")
                                confirmed_prev_lane_id = current_lane_id    # 完成变道时间检测, 再更新车道变化

                                # 重置潜在变道计数器
                                potential_lane_change_frames = 0
                                potential_new_lane_id = None
                        else:
                            # 重置潜在变道状态
                            potential_lane_change_frames = 0
                            potential_new_lane_id = None
                            confirmed_prev_lane_id = current_lane_id        # 更新车道变化

                        prev_road_id = current_road_id    

                        
                except Exception as e:
                    print(f"Error loading {filename}: {str(e)}")
                    

            # 计算均值
            mean_brake = np.mean(brake_values) if brake_values else np.nan
            mean_throttle = np.mean(throttle_values) if throttle_values else np.nan

            mean_speed = np.mean(all_speed) if all_speed else np.nan
            mean_thw = np.mean(all_thw) if all_thw else np.nan
            mean_ttc = np.mean(all_ttc) if all_ttc else np.nan
            mean_ttci = np.mean(all_ttci) if all_ttci else np.nan

            if max_brake == 0:
                max_brake =np.nan
            if brake_events == 0:
                brake_events =np.nan
            if lane_change_count == 0:
                lane_change_count =np.nan
            # 输出结果
            print('变道次数：', lane_change_count)
            print('#' * 50 + '\n')

            print("=== 刹车统计 ===")
            print(f"总刹车次数: {brake_events}")
            print(f"刹车均值力度: {mean_brake:.2f}")
            print(f"刹车最大力度: {max_brake:.2f}\n")
            # print(f"所有非零 brake 值: {brake_values}")       
            print('#' * 50 + '\n')


            # 得到汽车开的公里数
            with open(f"{file_path2}/result.json") as f:
                data = json.load(f)

            # 获取route_length
            route_length = data["_checkpoint"]["records"][0]["meta"]["route_length"]



            # 将所有结果写入到csv

            if ahead_distances:
                mean_value = sum(ahead_distances) / len(ahead_distances)
                max_distance_1 = max(ahead_distances)
                min_distance_1 = min(ahead_distances)
                media_value = np.median(ahead_distances)
                std_value = np.std(ahead_distances)
            else:
                mean_value = np.nan  # 或 0，表示无数据
                max_distance_1 = np.nan   # 或 0，取决于你的需求
                min_distance_1 = np.nan 
                media_value = np.nan 
                std_value = np.nan 

            if ahead_distances_0:
                mean_value_0 = sum(ahead_distances_0) / len(ahead_distances_0)
                max_distance_1_0 = max(ahead_distances_0)
                min_distance_1_0 = min(ahead_distances_0)
                media_value_0 = np.median(ahead_distances_0)
                std_value_0 = np.std(ahead_distances_0)
            else:
                mean_value_0 = np.nan 
                max_distance_1_0 = np.nan 
                min_distance_1_0 = np.nan 
                media_value_0 = np.nan 
                std_value_0 = np.nan 

            row = {
                "超车次数(每公里)": overtake_count * 1000 / route_length,
                #"超车前与前车距离": min(distances_ahead),
                #"超车后与后车距离":  min(distances_behind),

                "跟车距离均值": mean_value,
                "跟车距离最大值": max_distance_1,
                "跟车距离最小值": min_distance_1,
                "跟车距离中位数": media_value,
                "跟车距离标准差": std_value,
                
                "THW=D/v": mean_thw,
                "TTC=D/vr": mean_ttc,
                "TTCi=1/TTC": mean_ttci,

                "静态跟车距离均值": mean_value_0,
                "静态距离最大值": max_distance_1_0,
                "静态距离最小值": min_distance_1_0,
                "静态距离中位数": media_value_0,
                "静态距离标准差":std_value_0,


                "变道次数(每公里)": lane_change_count * 1000 / route_length,
                # "油门次数(每公里)": throttle_events * 1000 / route_length,
                "油门均值力度": mean_throttle,
                "油门最大力度": max_throttle,
                "油门标准差":np.std(throttle_values),
                "刹车次数(每公里)": brake_events * 1000 / route_length,
                "刹车均值": mean_brake,
                "刹车最大力度": max_brake,

                "纵向加速度_均值": lon_stats_pos['mean'],
                "纵向加速度_最大值": lon_stats_pos['max'],
                "纵向加速度_中位数": lon_stats_pos['median'],
                "纵向加速度_截断上": lon_stats_pos['upper_half_mean'], 
                "纵向加速度_截断下": lon_stats_pos['lower_half_mean'],  
                "纵向加速度_窗口均值": lon_stats_pos['sliding_max_mean'],
                "纵向加速度_峰值均值": lon_stats_pos['mean_peak_10'],
                
                # "纵向加速度_均值_01": lon_stats_pos_01['mean'],
                # "纵向加速度_最大值_01": lon_stats_pos_01['max'],

                "纵向加速度_标准差": lon_stats_pos['std'],
                #"纵向加速度_最小值": lon_stats_pos['min'],
                #"纵向加速度_有效次数": lon_stats_pos['count'],

                # 纵向加速度（负值：减速）
                "纵向减速度_均值": lon_stats_neg['mean'],
                "纵向减速度_最大值":lon_stats_neg['min'],    # 减速度有负号，最小值为绝对值最大值
                "纵向减速度_中位数": lon_stats_neg['median'],
                "纵向减速度_截断上": lon_stats_neg['upper_half_mean'], 
                "纵向减速度_截断下": lon_stats_neg['lower_half_mean'], 
                "纵向减速度_窗口均值": lon_stats_neg['sliding_max_mean'], 
                "纵向减速度_峰值均值": lon_stats_neg['mean_peak_10'] * -1,
                # "纵向减速度_均值_01": lon_stats_neg_01['mean'],
                # "纵向减速度_最大值_01":lon_stats_neg_01['min'], 

                "纵向减速度_标准差": lon_stats_neg['std'],
                # "纵向减速度_最大值": lon_stats_neg['max'],
                # "纵向减速度_最小值": lon_stats_neg['min'],
                #"纵向减速度_有效次数": lon_stats_neg['count'],

                # 横向加速度（绝对值）
                "横向加速度_均值": lat_stats['mean'],
                "横向加速度_最大值": lat_stats['max'],
                "横向加速度_中位数": lat_stats['median'],
                "横向加速度_截断上": lat_stats['upper_half_mean'], 
                "横向加速度_截断下": lat_stats['lower_half_mean'], 
                "横向加速度_窗口均值": lat_stats['sliding_max_mean'], 
                "横向加速度_峰值均值": lat_stats['mean_peak_10'],
                # "横向加速度_均值_01": lat_stats_01['mean'],
                # "横向加速度_最大值_01": lat_stats_01['max'],

                "横向加速度_标准差": lat_stats['std'],
                #"横向加速度_最小值": lat_stats['min'],
                #"横向加速度_有效次数": lat_stats['count'],

                # 纵向加加速度（正向：加速阶段）
                "纵向加加速度_均值": lon_jerk_stats_pos['mean'],
                "纵向加加速度_最大值": lon_jerk_stats_pos['max'],
                "纵向加加速度_窗口均值": lon_jerk_stats_pos['sliding_max_mean'],
                "纵向加加速度_标准差": lon_jerk_stats_pos['std'],
                # "纵向加加速度_最小值": lon_jerk_stats_pos['min'],  # 可选
                # "纵向加加速度_有效次数": lon_jerk_stats_pos['count'],  # 可选

                # 纵向加加速度（负向：减速阶段）
                "纵向加减速度_均值": lon_jerk_stats_neg['mean'],
                "纵向加减速度_最大值": lon_jerk_stats_neg['min'],  # 负值中最小=绝对值最大
                "纵向加减速度_窗口均值": lon_jerk_stats_neg['sliding_max_mean'],
                "纵向加减速度_标准差": lon_jerk_stats_neg['std'],
                # "纵向减加加速度_最小值": lon_jerk_stats_neg['max'],  # 可选
                # "纵向减加加速度_有效次数": lon_jerk_stats_neg['count'],  # 可选

                # 横向加加速度（绝对值）
                "横向加加速度_均值": lat_jerk_stats['mean'],
                "横向加加速度_最大值": lat_jerk_stats['max'],
                "横向加加速度_窗口均值": lat_jerk_stats['sliding_max_mean'],
                "横向加加速度_标准差": lat_jerk_stats['std'],
                # "横向加加速度_最小值": lat_jerk_stats['min'],  # 可选
                # "横向加加速度_有效次数": lat_jerk_stats['count']  # 可选


                # 速度
                "速度均值": mean_speed,
                "速度最大值": max(all_speed),
                # "速度最小值": min(all_speed),
                "速度标准差": np.std(all_speed)

                
                # "0-5加速时间": acceleration,
                # "停车减速度": deceleration
            }

            # 将所有浮点数格式化为保留两位小数的字符串
            formatted_row = {}

            for key, value in row.items():
                if isinstance(value, float):
                    formatted_row[key] = f"{value:.2f}"  # 保留两位小数，变成字符串
                else:
                    formatted_row[key] = value  # 非 float 保留原样


            # 写入CSV文件
            with open(csv_path, mode="w", newline="") as file:  # 使用csv_path而不是硬编码的"data.csv"
                writer = csv.writer(file)
                writer.writerow(["label", "value"])
                for key, value in formatted_row.items():
                    writer.writerow([key, value])

            print(f"文件已保存到: {csv_path}")




        # 创建一个空的DataFrame来存储合并后的数据
        merged_df = pd.DataFrame()

        for sub_name in os.listdir(dir_path2):
            csv_path = os.path.join(dir_path2, sub_name, "driving_statisics.csv")
            if os.path.exists(csv_path):
                # 读取CSV文件
                df = pd.read_csv(csv_path)
                
                # 假设第一列是标签列，第二列是数据列
                if len(df.columns) >= 2:
                    # 如果是第一个文件，设置标签列
                    if merged_df.empty:
                        merged_df[df.columns[0]] = df.iloc[:, 0]  # 标签列
                    
                    # 添加数据列，使用目录名作为列名
                    col_num = len(merged_df.columns) 
                    merged_df[col_num] = df.iloc[:, 1]

        # 计算各统计量
        if not merged_df.empty:
            # 获取所有数据列（排除第一列标签列）
            data_cols = [col for col in merged_df.columns if col != merged_df.columns[0]]
            num_data_cols = len(data_cols)
            
            # 计算所有数据列的均值
            merged_df['mean_all'] = merged_df[data_cols].mean(axis=1).round(2)
            
            # 计算前一半列的均值
            half = num_data_cols // 2
            first_half_cols = data_cols[:half]
            second_half_cols = data_cols[half:]
            
            merged_df['mean_first_half'] = merged_df[first_half_cols].mean(axis=1).round(2)
            merged_df['mean_second_half'] = merged_df[second_half_cols].mean(axis=1).round(2)
            
            # 计算前后半均值差的绝对值
            merged_df['abs_diff'] = (merged_df['mean_first_half'] - merged_df['mean_second_half']).abs().round(2)

        # 保存结果
        output_path = os.path.join(dir_path2, "combined_statistics.csv")
        merged_df.to_csv(output_path, index=False)
        print(f"处理完成，结果已保存到: {output_path}")
        print("最终列结构:", merged_df.columns.tolist())



        # # 保存合并后的CSV文件
        # output_path = os.path.join(dir_path2, "combined_statistics.csv")
        # merged_df.to_csv(output_path, index=False)
        # print(f"合并完成，结果已保存到: {output_path}")


