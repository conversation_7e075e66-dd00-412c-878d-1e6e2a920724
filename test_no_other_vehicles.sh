#!/bin/bash

# Test script to run leaderboard with --no-other-vehicles flag

export LEADERBOARD_ROOT=/home/<USER>/wangjm/Bench2Drive/leaderboard
export TEAM_AGENT=$LEADERBOARD_ROOT/leaderboard/autoagents/human_agent.py
export ROUTES=/home/<USER>/wangjm/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town04_extended.xml
export ROUTES_SUBSET=108
export REPETITIONS=1
export DEBUG_CHALLENGE=2
export CHALLENGE_TRACK_CODENAME=SENSORS
export CHECKPOINT_ENDPOINT="${LEADERBOARD_ROOT}/results.json"
export RECORD_PATH=/home/<USER>/Desktop/Evaluation/Bench2Drive/record
export RESUME=
export TEAM_CONFIG=human
export SCENARIO_RUNNER_ROOT=scenario_runner

export current_t=$(date "+%Y%m%d%H%M%S")

echo "Running leaderboard with --no-other-vehicles flag..."

python3 ${LEADERBOARD_ROOT}/leaderboard/leaderboard_evaluator.py \
--routes=${ROUTES} \
--routes-subset=${ROUTES_SUBSET} \
--repetitions=${REPETITIONS} \
--track=${CHALLENGE_TRACK_CODENAME} \
--checkpoint=${CHECKPOINT_ENDPOINT} \
--agent=${TEAM_AGENT} \
--agent-config=${TEAM_CONFIG} \
--debug=${DEBUG_CHALLENGE} \
--record=${RECORD_PATH} \
--resume=${RESUME} \
--person=test_no_vehicles \
--time=${current_t} \
--no-other-vehicles \
"$@"
