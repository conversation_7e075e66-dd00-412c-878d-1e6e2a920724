#!/usr/bin/env python3

"""
Test script to verify BackgroundBehavior modifications
"""

import os
import sys

# Add paths
sys.path.append('scenario_runner')
sys.path.append('leaderboard')

def test_background_behavior_import():
    """Test if BackgroundBehavior can be imported and respects no_other_vehicles"""
    
    print("=== Testing BackgroundBehavior Modifications ===")
    
    try:
        # Test without NO_OTHER_VEHICLES
        print("1. Testing normal mode (vehicles enabled)...")
        os.environ.pop('NO_OTHER_VEHICLES', None)  # Remove if exists
        
        from scenario_runner.srunner.scenarios.background_activity import BackgroundBehavior
        
        # Mock objects for testing
        class MockEgoActor:
            pass
        
        class MockRoute:
            pass
        
        ego_actor = MockEgoActor()
        route = MockRoute()
        
        # This should work normally (but might fail due to missing CARLA dependencies)
        try:
            bg_behavior = BackgroundBehavior(ego_actor, route, debug=True)
            print("   ✅ Normal mode: Background<PERSON>ehavior created successfully")
            has_no_other_vehicles = getattr(bg_behavior, '_no_other_vehicles', False)
            print(f"   _no_other_vehicles = {has_no_other_vehicles}")
        except Exception as e:
            print(f"   ⚠️  Normal mode failed (expected due to missing CARLA): {e}")
        
        # Test with NO_OTHER_VEHICLES
        print("\n2. Testing no_other_vehicles mode...")
        os.environ['NO_OTHER_VEHICLES'] = 'true'
        
        # Reload the module to pick up the environment variable
        import importlib
        import scenario_runner.srunner.scenarios.background_activity
        importlib.reload(scenario_runner.srunner.scenarios.background_activity)
        
        from scenario_runner.srunner.scenarios.background_activity import BackgroundBehavior
        
        try:
            bg_behavior = BackgroundBehavior(ego_actor, route, debug=True)
            print("   ✅ No-other-vehicles mode: BackgroundBehavior created successfully")
            has_no_other_vehicles = getattr(bg_behavior, '_no_other_vehicles', False)
            print(f"   _no_other_vehicles = {has_no_other_vehicles}")
            
            if has_no_other_vehicles:
                print("   ✅ SUCCESS: no_other_vehicles mode is properly detected!")
            else:
                print("   ❌ FAILURE: no_other_vehicles mode not detected!")
                
        except Exception as e:
            print(f"   ❌ No-other-vehicles mode failed: {e}")
        
        print("\n=== Test Complete ===")
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        print("Make sure you're running this from the correct directory with proper PYTHONPATH")
        return False
    
    return True

if __name__ == '__main__':
    success = test_background_behavior_import()
    sys.exit(0 if success else 1)
