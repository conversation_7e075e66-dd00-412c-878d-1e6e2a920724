import xml.etree.ElementTree as ET
from collections import defaultdict
import math

def split_routes_by_town(xml_file, train_output="train.xml", test_output="test.xml"):
    # 解析XML文件
    tree = ET.parse(xml_file)
    root = tree.getroot()
    
    # 统计每个town的route数量
    town_routes = defaultdict(list)
    for route in root.findall('route'):
        town = route.get('town')
        town_routes[town].append(route)
    
    # 创建train和test的根元素
    train_root = ET.Element('routes')
    test_root = ET.Element('routes')
    
    # 对每个town的route列表进行对半分割
    for town, routes in town_routes.items():
        split_index = math.ceil(len(routes) / 2)
        
        # 前一半放入train
        for route in routes[:split_index]:
            train_root.append(route)
        
        # 后一半放入test
        for route in routes[split_index:]:
            test_root.append(route)
    
    # 写入train文件
    train_tree = ET.ElementTree(train_root)
    train_tree.write(train_output, encoding='utf-8', xml_declaration=True)
    
    # 写入test文件
    test_tree = ET.ElementTree(test_root)
    test_tree.write(test_output, encoding='utf-8', xml_declaration=True)
    
    # 打印统计信息
    print("Route distribution by town:")
    for town, routes in town_routes.items():
        total = len(routes)
        train_count = math.ceil(total / 2)
        test_count = total - train_count
        print(f"Town {town}: Total routes={total}, Train={train_count}, Test={test_count}")

# 使用示例
if __name__ == "__main__":
    input_file = "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/bench2drive220.xml"  # 替换为您的XML文件路径
    split_routes_by_town(input_file, "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/train_routes.xml", "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/test_routes.xml")