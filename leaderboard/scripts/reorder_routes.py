import xml.etree.ElementTree as ET

def reindex_train_routes(input_file="train.xml", output_file="train_reindexed.xml"):
    # 解析XML文件
    tree = ET.parse(input_file)
    root = tree.getroot()
    
    # 获取所有route元素并按原始顺序排列
    routes = root.findall('route')
    
    # 重新索引route ID
    for index, route in enumerate(routes, start=1):
        route.set('id', str(index))
    
    # 保存修改后的XML
    tree.write(output_file, encoding='utf-8', xml_declaration=True)
    print(f"Successfully reindexed {len(routes)} routes. Output saved to {output_file}")

# 使用示例
if __name__ == "__main__":
    input_f = "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/ours_test.xml"
    output_f = "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/ours_test.xml"
    reindex_train_routes(input_file=input_f, output_file=output_f)