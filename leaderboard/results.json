{"_checkpoint": {"global_record": {"index": -1, "route_id": -1, "status": "Completed", "infractions": {"collisions_layout": 0.0, "collisions_pedestrian": 0.0, "collisions_vehicle": 0.0, "red_light": 0.0, "stop_infraction": 0.0, "outside_route_lanes": 0.0, "min_speed_infractions": 176.294, "yield_emergency_vehicle_infractions": 0.0, "scenario_timeouts": 0.0, "route_dev": 0.0, "vehicle_blocked": 0.0, "route_timeout": 0.0}, "scores_mean": {"score_composed": 100.0, "score_route": 100.0, "score_penalty": 1.0}, "scores_std_dev": {"score_composed": 0, "score_route": 0, "score_penalty": 0}, "meta": {"total_length": 119.119, "duration_game": 17.9, "duration_system": 20.032, "exceptions": []}}, "progress": [1, 1], "records": [{"index": 0, "route_id": "RouteScenario_1790_rep0", "scenario_name": "HazardAtSideLane_1", "weather_id": "8", "save_name": "RouteScenario_1790_rep0_Town12_HazardAtSideLane_1_8_04_24_21_11_49", "status": "Completed", "num_infractions": 21, "infractions": {"collisions_layout": [], "collisions_pedestrian": [], "collisions_vehicle": [], "red_light": [], "stop_infraction": [], "outside_route_lanes": [], "min_speed_infractions": ["Average speed is 38.84% of the surrounding traffic's one", "Average speed is 175.2% of the surrounding traffic's one", "Average speed is 175.94% of the surrounding traffic's one", "Average speed is 98.63% of the surrounding traffic's one", "Average speed is 61.3% of the surrounding traffic's one", "Average speed is 104.21% of the surrounding traffic's one", "Average speed is 129.38% of the surrounding traffic's one", "Average speed is 148.48% of the surrounding traffic's one", "Average speed is 145.62% of the surrounding traffic's one", "Average speed is 124.2% of the surrounding traffic's one", "Average speed is 90.91% of the surrounding traffic's one", "Average speed is 113.39% of the surrounding traffic's one", "Average speed is 119.15% of the surrounding traffic's one", "Average speed is 124.58% of the surrounding traffic's one", "Average speed is 117.47% of the surrounding traffic's one", "Average speed is 72.88% of the surrounding traffic's one", "Average speed is 79.9% of the surrounding traffic's one", "Average speed is 101.8% of the surrounding traffic's one", "Average speed is 107.16% of the surrounding traffic's one", "Average speed is 62.56% of the surrounding traffic's one", "Average speed is 62.56% of the surrounding traffic's one"], "yield_emergency_vehicle_infractions": [], "scenario_timeouts": [], "route_dev": [], "vehicle_blocked": [], "route_timeout": []}, "scores": {"score_route": 100, "score_penalty": 1.0, "score_composed": 100.0}, "meta": {"route_length": 119.119, "duration_game": 17.9, "duration_system": 20.032}, "town_name": "Town12"}]}, "entry_status": "Finished", "eligible": true, "sensors": ["carla_camera"], "values": ["100.0", "100.0", "1.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "176.294"], "labels": ["Avg. driving score", "Avg. route completion", "Avg. infraction penalty", "Collisions with pedestrians", "Collisions with vehicles", "Collisions with layout", "Red lights infractions", "Stop sign infractions", "Off-road infractions", "Route deviations", "Route timeouts", "Agent blocked", "Yield emergency vehicles infractions", "Sc<PERSON>rio timeouts", "Min speed infractions"]}