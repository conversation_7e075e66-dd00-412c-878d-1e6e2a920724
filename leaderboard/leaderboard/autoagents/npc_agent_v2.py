#!/usr/bin/env python

# This work is licensed under the terms of the MIT license.
# For a copy, see <https://opensource.org/licenses/MIT>.

"""
This module provides an NPC agent to control the ego vehicle with metric collection
"""

from __future__ import print_function

import carla
import math
from agents.navigation.basic_agent import BasicAgent
from srunner.scenariomanager.carla_data_provider import CarlaDataProvider

from leaderboard.autoagents.autonomous_agent import AutonomousAgent, Track

def get_entry_point():
    return 'NpcAgent'

class NpcAgent(AutonomousAgent):

    """
    NPC autonomous agent to control the ego vehicle with metric collection
    """

    def __init__(self, carla_host, carla_port, debug=False):
        super().__init__(carla_host, carla_port, debug)
        self.metric_info = {}  # 存储指标数据
        self.step_count = 0  # 记录步数
        self.distance_traveled = 0.0  # 行驶距离
        self.previous_location = None  # 上一步位置
        self.start_location = None  # 起始位置
        self.total_route_distance = None  # 总路线长度

    _agent = None
    _route_assigned = False

    def setup(self, path_to_conf_file):
        """
        Setup the agent parameters
        """
        self.track = Track.SENSORS
        self._agent = None

    def sensors(self):
        """
        Define the sensor suite required by the agent
        """
        sensors = [
            {'type': 'sensor.camera.rgb', 'x': 0.7, 'y': -0.4, 'z': 1.60, 'roll': 0.0, 'pitch': 0.0, 'yaw': 0.0,
             'width': 300, 'height': 200, 'fov': 100, 'id': 'Left'},
        ]
        return sensors

    def run_step(self, input_data, timestamp, draw_target_waypoints=None, target_road_options=None):
        """
        Execute one step of navigation while collecting metrics
        """
        self.step_count += 1
        
        # 执行原有逻辑
        if not self._agent:
            # 搜索主角车辆
            hero_actor = None
            for actor in CarlaDataProvider.get_world().get_actors():
                if 'role_name' in actor.attributes and actor.attributes['role_name'] == 'hero':
                    hero_actor = actor
                    break

            if not hero_actor:
                return carla.VehicleControl()

            # 创建基本代理并设置路线
            self._agent = BasicAgent(hero_actor, 30)

            plan = []
            prev_wp = None
            for transform, _ in self._global_plan_world_coord:
                wp = CarlaDataProvider.get_map().get_waypoint(transform.location)
                if prev_wp:
                    plan.extend(self._agent.trace_route(prev_wp, wp))
                prev_wp = wp

            self._agent.set_global_plan(plan)
            
            # 初始化位置和路线信息
            self.start_location = hero_actor.get_location()
            self.previous_location = self.start_location
            self.total_route_distance = self._calculate_total_route_distance()
            
            # 首次调用返回空控制
            return carla.VehicleControl()
        else:
            # 获取控制指令
            control = self._agent.run_step()
            
            # 获取当前车辆状态
            vehicle = self._agent._vehicle
            location = vehicle.get_location()
            velocity = vehicle.get_velocity()
            acceleration = vehicle.get_acceleration()
            transform = vehicle.get_transform()
            
            # 计算速度 (km/h)
            speed = 3.6 * math.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)
            
            # 更新行驶距离
            if self.previous_location:
                self.distance_traveled += location.distance(self.previous_location)
            self.previous_location = location
            
            # 计算路线完成度
            route_completion = self._calculate_route_completion(location)
            
            # 更新指标数据
            self.metric_info = {
                'step': self.step_count,
                'timestamp': timestamp,
                'location': (location.x, location.y, location.z),
                'orientation': (transform.rotation.pitch, transform.rotation.yaw, transform.rotation.roll),
                'speed': speed,
                'acceleration': (acceleration.x, acceleration.y, acceleration.z),
                'control': {
                    'throttle': control.throttle,
                    'steer': control.steer,
                    'brake': control.brake,
                    'hand_brake': control.hand_brake,
                    'reverse': control.reverse
                },
                'distance_traveled': self.distance_traveled,
                'route_completion': route_completion,
                'remaining_distance': self.total_route_distance - self.distance_traveled if self.total_route_distance else None
            }
            
            return control

    def _calculate_total_route_distance(self):
        """计算总路线长度"""
        if not hasattr(self, '_global_plan_world_coord') or len(self._global_plan_world_coord) < 2:
            return 0.0
            
        total_distance = 0.0
        for i in range(len(self._global_plan_world_coord) - 1):
            p1 = self._global_plan_world_coord[i][0].location
            p2 = self._global_plan_world_coord[i+1][0].location
            total_distance += p1.distance(p2)
            
        return total_distance

    def _calculate_route_completion(self, current_location):
        """计算路线完成度百分比"""
        if not self.total_route_distance or self.total_route_distance <= 0:
            return 0.0
            
        # 简化计算：假设当前位置到终点的距离与总距离的比例
        if len(self._global_plan_world_coord) > 0:
            end_location = self._global_plan_world_coord[-1][0].location
            remaining_distance = current_location.distance(end_location)
            
            # 确保完成度在0-100%之间
            completion = max(0.0, min(100.0, 100.0 * (1 - remaining_distance / self.total_route_distance)))
            return completion
            
        return 0.0

    def get_metric_info(self):
        """返回当前收集的指标数据"""
        return self.metric_info
