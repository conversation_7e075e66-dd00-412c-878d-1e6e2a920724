#!/usr/bin/env python

# This work is licensed under the terms of the MIT license.
# For a copy, see <https://opensource.org/licenses/MIT>.

"""
This module provides a human agent to control the ego vehicle via keyboard
"""

import numpy as np
import json
import datetime
import os

try:
    import pygame
    from pygame.locals import K_DOWN
    from pygame.locals import K_LEFT
    from pygame.locals import K_RIGHT
    from pygame.locals import K_SPACE
    from pygame.locals import K_UP
    from pygame.locals import K_a
    from pygame.locals import K_d
    from pygame.locals import K_s
    from pygame.locals import K_w
    from pygame.locals import K_q
except ImportError:
    raise RuntimeError('cannot import pygame, make sure pygame package is installed')

import carla

from leaderboard.autoagents.autonomous_agent import AutonomousAgent, Track
from srunner.scenariomanager.carla_data_provider import CarlaDataProvider


def get_entry_point():
    return 'HumanAgent'

def get_actor_display_name(actor, truncate=250):
    name = ' '.join(actor.type_id.replace('_', '.').title().split('.')[1:])
    return (name[:truncate - 1] + u'\u2026') if len(name) > truncate else name

def location2xyz(location):
    return [location.x, location.y, location.z]

def rotation2rpy(rotation):
    return [rotation.roll, rotation.pitch, rotation.yaw]

# ==============================================================================
# -- FadingText ----------------------------------------------------------------
# ==============================================================================


class FadingText(object):
    def __init__(self, font, dim, pos):
        self.font = font
        self.dim = dim
        self.pos = pos
        self.seconds_left = 0
        self.surface = pygame.Surface(self.dim)

    def set_text(self, text, color=(255, 255, 255), seconds=2.0):
        text_texture = self.font.render(text, True, color)
        self.surface = pygame.Surface(self.dim)
        self.seconds_left = seconds
        self.surface.fill((0, 0, 0, 0))
        self.surface.blit(text_texture, (10, 11))

    def tick(self, _, clock):
        delta_seconds = 1e-3 * clock.get_time()
        self.seconds_left = max(0.0, self.seconds_left - delta_seconds)
        self.surface.set_alpha(500.0 * self.seconds_left)

    def render(self, display):
        display.blit(self.surface, self.pos)


# ==============================================================================
# -- HelpText ------------------------------------------------------------------
# ==============================================================================


class HelpText(object):
    def __init__(self, font, width, height):
        lines = __doc__.split('\n')
        self.font = font
        self.dim = (680, len(lines) * 22 + 12)
        self.pos = (0.5 * width - 0.5 * self.dim[0], 0.5 * height - 0.5 * self.dim[1])
        self.seconds_left = 0
        self.surface = pygame.Surface(self.dim)
        self.surface.fill((0, 0, 0, 0))
        for n, line in enumerate(lines):
            text_texture = self.font.render(line, True, (255, 255, 255))
            self.surface.blit(text_texture, (22, n * 22))
            self._render = False
        self.surface.set_alpha(220)

    def toggle(self):
        self._render = not self._render

    def render(self, display):
        if self._render:
            display.blit(self.surface, self.pos)

class HumanInterface(object):

    """
    Class to control a vehicle manually for debugging purposes
    """

    def __init__(self, width, height, side_scale, left_mirror=False, right_mirror=False):
        self._width = width
        self._height = height
        self._scale = side_scale
        self._surface = None

        self._left_mirror = left_mirror
        self._right_mirror = right_mirror

        pygame.init()
        pygame.font.init()
        self.dim = (1280 + 400, 720)

        # for left side info render
        font = pygame.font.Font(pygame.font.get_default_font(), 20)
        font_name = 'courier' if os.name == 'nt' else 'mono'
        fonts = [x for x in pygame.font.get_fonts() if font_name in x]
        default_font = 'ubuntumono'
        mono = default_font if default_font in fonts else fonts[0]
        mono = pygame.font.match_font(mono)
        self._font_mono = pygame.font.Font(mono, 12 if os.name == 'nt' else 14)
        self._notifications = FadingText(font, (width, 40), (0, height - 40))
        self.help = HelpText(pygame.font.Font(mono, 24), width, height)
        self._info_text = []
        self._show_info = True
        self._notifications = FadingText(font, (width, 40), (0, height - 40))


        self._clock = pygame.time.Clock()
        self._display = pygame.display.set_mode((self._width + 400, self._height), pygame.HWSURFACE | pygame.DOUBLEBUF)
        pygame.display.set_caption("Human Agent")

        # 添加导航显示区域参数
        self.nav_rect = pygame.Rect(width, 0, 400, height)  # 右侧400px宽的区域
        self.vehicle_pos = (0, 0)
        self.vehicle_yaw = 0
        self.nav_scale = 5.0

    def notification(self, text, seconds=2.0):
        self._notifications.set_text(text, seconds=seconds)

    def render_info(self, control, draw_target_waypoints, target_road_options):
        if not self._show_info:
            return
        player = CarlaDataProvider._ego_vehicle
        world = CarlaDataProvider.get_world()
        self._notifications.tick(world, self._clock)
        t = player.get_transform()
        v = player.get_velocity()
        # print("velocity:", v)
        c = control
        heading = 'N' if abs(t.rotation.yaw) < 89.5 else ''
        heading += 'S' if abs(t.rotation.yaw) > 90.5 else ''
        heading += 'E' if 179.5 > t.rotation.yaw > 0.5 else ''
        heading += 'W' if -0.5 > t.rotation.yaw > -179.5 else ''
        # max_col = max(1.0, max(collision))
        # collision = [x / max_col for x in collision]
        vehicles = world.get_actors().filter('vehicle.*')
        self._info_text = [
            # 'Server:  % 16.0f FPS' % self.server_fps,
            # 'Client:  % 16.0f FPS' % self._clock.get_fps(),
            # '',
            'Vehicle: % 20s' % get_actor_display_name(player, truncate=20),
            'Map:     % 20s' % world.get_map().name.split('/')[-1],
            # 'Simulation time: % 12s' % datetime.timedelta(seconds=int(self.simulation_time)),
            '',
            'Speed:   % 15.0f km/h' % (3.6 * math.sqrt(v.x**2 + v.y**2 + v.z**2)),
            u'Heading:% 16.0f\N{DEGREE SIGN} % 2s' % (t.rotation.yaw, heading),
            'Location:% 20s' % ('(% 5.1f, % 5.1f)' % (t.location.x, t.location.y)),
            # 'GNSS:% 24s' % ('(% 2.6f, % 3.6f)' % (world.gnss_sensor.lat, world.gnss_sensor.lon)),
            'Height:  % 18.0f m' % t.location.z,
            '']
        if isinstance(c, carla.VehicleControl):
            self._info_text += [
                ('Throttle:', c.throttle, 0.0, 1.0),
                ('Steer:', c.steer, -1.0, 1.0),
                ('Brake:', c.brake, 0.0, 1.0),
                ('Reverse:', c.reverse),
                ('Hand brake:', c.hand_brake),
                ('Manual:', c.manual_gear_shift),
                'Gear:        %s' % {-1: 'R', 0: 'N'}.get(c.gear, c.gear)]
        elif isinstance(c, carla.WalkerControl):
            self._info_text += [
                ('Speed:', c.speed, 0.0, 5.556),
                ('Jump:', c.jump)]
        self._info_text += [
            '',
            'Number of vehicles: % 8d' % len(vehicles)]
        
        if self._show_info:
            info_surface = pygame.Surface((220, self.dim[1]))
            info_surface.set_alpha(100)
            self._display.blit(info_surface, (0, 0))
            v_offset = 4
            bar_h_offset = 100
            bar_width = 106
            for item in self._info_text:
                if v_offset + 18 > self.dim[1]:
                    break
                if isinstance(item, list):
                    if len(item) > 1:
                        points = [(x + 8, v_offset + 8 + (1.0 - y) * 30) for x, y in enumerate(item)]
                        pygame.draw.lines(self._display, (255, 136, 0), False, points, 2)
                    item = None
                    v_offset += 18
                elif isinstance(item, tuple):
                    if isinstance(item[1], bool):
                        rect = pygame.Rect((bar_h_offset, v_offset + 8), (6, 6))
                        pygame.draw.rect(self._display, (255, 255, 255), rect, 0 if item[1] else 1)
                    else:
                        rect_border = pygame.Rect((bar_h_offset, v_offset + 8), (bar_width, 6))
                        pygame.draw.rect(self._display, (255, 255, 255), rect_border, 1)
                        f = (item[1] - item[2]) / (item[3] - item[2])
                        if item[2] < 0.0:
                            rect = pygame.Rect((bar_h_offset + f * (bar_width - 6), v_offset + 8), (6, 6))
                        else:
                            rect = pygame.Rect((bar_h_offset, v_offset + 8), (f * bar_width, 6))
                        pygame.draw.rect(self._display, (255, 255, 255), rect)
                    item = item[0]
                if item:  # At this point has to be a str.
                    surface = self._font_mono.render(item, True, (255, 255, 255))
                    self._display.blit(surface, (8, v_offset))
                v_offset += 18
            self._notifications.render(self._display)
            self.render_navigation(self._display, player, draw_target_waypoints, target_road_options)
            self.help.render(self._display)
            

    def render_navigation(self, display, player, draw_target_waypoints, draw_target_road_options):
        """在右侧区域渲染导航地图（车辆朝上，路径点相对车辆方向显示）"""

        # filtered_options = list(dict.fromkeys(draw_target_road_options))
        
        nav_surface = pygame.Surface((self.nav_rect.width, self.nav_rect.height))
        nav_surface.fill((50, 50, 50))  # 深灰色背景
        
        # 计算中心位置(相对于导航区域)
        center_x, center_y = self.nav_rect.width // 2, self.nav_rect.height // 2
        
        # 获取车辆当前的位置和朝向
        vehicle_transform = player.get_transform()
        vehicle_location = vehicle_transform.location
        # print("vehicle_location:", vehicle_location)
        vehicle_yaw = math.radians(vehicle_transform.rotation.yaw)
        
        # 绘制网格（以车辆为中心的正交网格）
        self._draw_orthogonal_grid(nav_surface, center_x, center_y)
        
        # 绘制路径点（相对于车辆坐标系）
        if draw_target_waypoints:
            points = []
            for wp in draw_target_waypoints:
                # 计算相对于车辆的位置（世界坐标系）
                delta_x = wp.location.x - vehicle_location.x
                delta_y = wp.location.y - vehicle_location.y

                # 将世界坐标转换为车辆坐标系
                rotated_x = -delta_y * math.cos(vehicle_yaw) + delta_x * math.sin(vehicle_yaw)
                rotated_y = -delta_y * math.sin(vehicle_yaw) - delta_x * math.cos(vehicle_yaw)
                
                # 转换为屏幕坐标
                screen_x = center_x - rotated_x * self.nav_scale
                screen_y = center_y + rotated_y * self.nav_scale
                points.append((screen_x, screen_y))
            
            # 绘制连接线
            if len(points) > 1:
                pygame.draw.lines(nav_surface, (0, 200, 255), False, points, 2)
            
            # 绘制路径点
            for x, y in points:
                pygame.draw.circle(nav_surface, (0, 255, 0), (int(x), int(y)), 5)
        
        # 绘制车辆方向
        pygame.draw.circle(nav_surface, (255, 0, 0), (center_x, center_y), 8)
        pygame.draw.line(nav_surface, (255, 0, 0),
                    (center_x, center_y), (center_x, center_y - 20), 8)
        
        # 显示比例尺
        font = pygame.font.SysFont('Arial', 16)
        scale_text = f"Scale: 1px = {1/self.nav_scale:.2f}m"
        scale_surface = font.render(scale_text, True, (255, 255, 255))
        nav_surface.blit(scale_surface, (10, 40))  # 左上角(10,40)，在命令下方

        # # 显示driving command
        # if filtered_options:
        #     cmd_text = "Command: " + ", ".join(filtered_options)
        #     cmd_surface = font.render(cmd_text, True, (255, 255, 255))
        #     nav_surface.blit(cmd_surface, (10, 10))  # 左上角(10,10)
        
        display.blit(nav_surface, self.nav_rect)

    def _draw_orthogonal_grid(self, surface, center_x, center_y):
        """绘制正交网格（车辆坐标系）"""
        grid_color = (100, 100, 100)
        major_grid_color = (150, 150, 150)
        grid_size = 50 * self.nav_scale  # 网格大小（像素）
        
        # 绘制主要轴线
        pygame.draw.line(surface, major_grid_color, 
                        (center_x, 0), (center_x, surface.get_height()), 2)  # 垂直轴（车辆前方）
        pygame.draw.line(surface, major_grid_color, 
                        (0, center_y), (surface.get_width(), center_y), 2)    # 水平轴（车辆右侧）
        
        # 绘制网格线
        width, height = surface.get_width(), surface.get_height()
        
        # 水平网格线（车辆前后方向）
        for i in range(1, int((width - center_x) / grid_size) + 1):
            x = center_x + i * grid_size
            pygame.draw.line(surface, grid_color, (x, 0), (x, height), 1)
        for i in range(1, int(center_x / grid_size) + 1):
            x = center_x - i * grid_size
            pygame.draw.line(surface, grid_color, (x, 0), (x, height), 1)
        
        # 垂直网格线（车辆左右方向）
        for i in range(1, int((height - center_y) / grid_size) + 1):
            y = center_y + i * grid_size
            pygame.draw.line(surface, grid_color, (0, y), (width, y), 1)
        for i in range(1, int(center_y / grid_size) + 1):
            y = center_y - i * grid_size
            pygame.draw.line(surface, grid_color, (0, y), (width, y), 1)

    def run_interface(self, input_data, control, draw_target_waypoints, target_road_options):
        """
        Run the GUI
        """

        # Process sensor data
        image_center = input_data['Center'][1][:, :, -2::-1]
        self._surface = pygame.surfarray.make_surface(image_center.swapaxes(0, 1))

        # Add the left mirror
        if self._left_mirror:
            image_left = input_data['Left'][1][:, :, -2::-1]
            left_surface = pygame.surfarray.make_surface(image_left.swapaxes(0, 1))
            self._surface.blit(left_surface, (0, (1 - self._scale) * self._height))

        # Add the right mirror
        if self._right_mirror:
            image_right = input_data['Right'][1][:, :, -2::-1]
            right_surface = pygame.surfarray.make_surface(image_right.swapaxes(0, 1))
            self._surface.blit(right_surface, ((1 - self._scale) * self._width, (1 - self._scale) * self._height))

        # Display image
        if self._surface is not None:
            self._display.blit(self._surface, (0, 0))

        self.render_info(control, draw_target_waypoints, target_road_options)


        pygame.display.flip()

    def set_black_screen(self):
        """Set the surface to black"""
        black_array = np.zeros([self._width, self._height])
        self._surface = pygame.surfarray.make_surface(black_array)
        if self._surface is not None:
            self._display.blit(self._surface, (0, 0))
        pygame.display.flip()

    def _quit(self):
        pygame.quit()


class HumanAgent(AutonomousAgent):

    """
    Human agent to control the ego vehicle via keyboard
    """

    current_control = None
    agent_engaged = False

    def setup(self, path_to_conf_file):
        """
        Setup the agent parameters
        """
        self.track = Track.SENSORS

        self.agent_engaged = False
        self.camera_width = 1280
        self.camera_height = 720
        self._side_scale = 0.3
        self._left_mirror = True
        self._right_mirror = True

        self._hic = HumanInterface(
            self.camera_width,
            self.camera_height,
            self._side_scale,
            self._left_mirror,
            self._right_mirror
        )
        # self._controller = KeyboardControl(path_to_conf_file)
        self._controller = DualControl(path_to_conf_file)
        self._prev_timestamp = 0

        self._clock = pygame.time.Clock()

        self.step = -1
        self.metric_info = {}

    # def read_metric_info

    def sensors(self):
        """
        Define the sensor suite required by the agent

        :return: a list containing the required sensors in the following format:

        [
            {'type': 'sensor.camera.rgb', 'x': 0.7, 'y': -0.4, 'z': 1.60, 'roll': 0.0, 'pitch': 0.0, 'yaw': 0.0,
                      'width': 300, 'height': 200, 'fov': 100, 'id': 'Left'},

            {'type': 'sensor.camera.rgb', 'x': 0.7, 'y': 0.4, 'z': 1.60, 'roll': 0.0, 'pitch': 0.0, 'yaw': 0.0,
                      'width': 300, 'height': 200, 'fov': 100, 'id': 'Right'},

            {'type': 'sensor.lidar.ray_cast', 'x': 0.7, 'y': 0.0, 'z': 1.60, 'yaw': 0.0, 'pitch': 0.0, 'roll': 0.0,
             'id': 'LIDAR'}
        ]
        """

        # sensors = [
        #     {'type': 'sensor.camera.rgb', 'x': 0.7, 'y': 0.0, 'z': 1.60, 'roll': 0.0, 'pitch': 0.0, 'yaw': 0.0,
        #      'width': self.camera_width, 'height': self.camera_height, 'fov': 100, 'id': 'Center'},
        # ]
        sensors = [
            {'type': 'sensor.camera.rgb', 'x': -5.5, 'y': 0.0, 'z': 2.8, 'roll': 0.0, 'pitch': -15.0, 'yaw': 0.0,
             'width': self.camera_width, 'height': self.camera_height, 'fov': 100, 'id': 'Center'},
        ]

        if self._left_mirror:
            sensors.append(
                {'type': 'sensor.camera.rgb', 'x': 0.7, 'y': -1.0, 'z': 1, 'roll': 0.0, 'pitch': 0.0, 'yaw': 210.0,
                 'width': self.camera_width * self._side_scale, 'height': self.camera_height * self._side_scale,
                 'fov': 100, 'id': 'Left'})

        if self._right_mirror:
            sensors.append(
                {'type': 'sensor.camera.rgb', 'x': 0.7, 'y': 1.0, 'z': 1, 'roll': 0.0, 'pitch': 0.0, 'yaw': 150.0,
                 'width': self.camera_width * self._side_scale, 'height': self.camera_height * self._side_scale,
                 'fov': 100, 'id': 'Right'})

        return sensors

    def run_step(self, input_data, timestamp, draw_target_waypoints, target_road_options):
        """
        Execute one step of navigation.
        """

        # print("run step**************")
        self.step += 1
        self._clock.tick_busy_loop(20)
        self.agent_engaged = True
        control = self._controller.parse_events(timestamp - self._prev_timestamp)
        self._hic.run_interface(input_data, control, draw_target_waypoints, target_road_options)

        # process the saved nav info
        nav_transform_list = []
        nav_location_list = []
        for wp in draw_target_waypoints:
            nav_transform_list.append(rotation2rpy(wp.rotation))
            nav_location_list.append(location2xyz(wp.location))
        nav_command_list = [option.name for option in target_road_options]

        # get ego_speed
        player = CarlaDataProvider._ego_vehicle
        v = player.get_velocity()
        ego_speed = 3.6 * math.sqrt(v.x**2 + v.y**2 + v.z**2)

        frame_result = {
            "nav_transform": nav_transform_list,
            "nav_location": nav_location_list,
            "nav_command": nav_command_list,
            "near_rotation": [], # rotation2rpy(near_command_xy[0].transform.rotation),
            "near_location": [], # location2xyz(near_command_xy[0].transform.location),
            "near_command": [], #near_command_xy[1].name,
            "far_rotation": [], # rotation2rpy(far_command_xy[0].transform.rotation),
            "far_location": [], #location2xyz(far_command_xy[0].transform.location),
            "far_command": [], #far_command_xy[1].name,
            "ego_speed": ego_speed
        }

        # control = self._controller.parse_events(timestamp - self._prev_timestamp)
        self._prev_timestamp = timestamp

        metric_info = self.get_metric_info()
        # metric_info.update(frame_result)
        self.metric_info[self.step] = metric_info

        return control

    def destroy(self):
        """
        Cleanup
        """
        self._hic.set_black_screen()
        self._hic._quit = True


class KeyboardControl(object):

    """
    Keyboard control for the human agent
    """

    def __init__(self, path_to_conf_file):
        """
        Init
        """
        self._control = carla.VehicleControl()
        self._steer_cache = 0.0
        self._clock = pygame.time.Clock()

        # Get the mode
        if "human" not in path_to_conf_file:

            with (open(path_to_conf_file, "r")) as f:
                lines = f.read().split("\n")
                self._mode = lines[0].split(" ")[1]
                self._endpoint = lines[1].split(" ")[1]

            # Get the needed vars
            if self._mode == "log":
                self._log_data = {'records': []}

            elif self._mode == "playback":
                self._index = 0
                self._control_list = []

                with open(self._endpoint) as fd:
                    try:
                        self._records = json.load(fd)
                        self._json_to_control()
                    except json.JSONDecodeError:
                        pass
        else:
            self._mode = "normal"
            self._endpoint = None

    def _json_to_control(self):

        # transform strs into VehicleControl commands
        for entry in self._records['records']:
            control = carla.VehicleControl(throttle=entry['control']['throttle'],
                                           steer=entry['control']['steer'],
                                           brake=entry['control']['brake'],
                                           hand_brake=entry['control']['hand_brake'],
                                           reverse=entry['control']['reverse'],
                                           manual_gear_shift=entry['control']['manual_gear_shift'],
                                           gear=entry['control']['gear'])
            self._control_list.append(control)

    def parse_events(self, timestamp):
        """
        Parse the keyboard events and set the vehicle controls accordingly
        """
        # Move the vehicle
        if self._mode == "playback":
            self._parse_json_control()
        else:
            self._parse_vehicle_keys(pygame.key.get_pressed(), timestamp*1000)

        # Record the control
        if self._mode == "log":
            self._record_control()

        return self._control

    def _parse_vehicle_keys(self, keys, milliseconds):
        """
        Calculate new vehicle controls based on input keys
        """

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return 
            elif event.type == pygame.KEYUP:
                if event.key == K_q:
                    self._control.gear = 1 if self._control.reverse else -1
                    self._control.reverse = self._control.gear < 0

        if keys[K_UP] or keys[K_w]:
            self._control.throttle = 0.8
        else:
            self._control.throttle = 0.0

        steer_increment = 3e-4 * milliseconds
        if keys[K_LEFT] or keys[K_a]:
            self._steer_cache -= steer_increment
        elif keys[K_RIGHT] or keys[K_d]:
            self._steer_cache += steer_increment
        else:
            self._steer_cache = 0.0

        self._control.steer = round(self._steer_cache, 1)
        self._control.brake = 1.0 if keys[K_DOWN] or keys[K_s] else 0.0
        self._control.hand_brake = keys[K_SPACE]

    def _parse_json_control(self):

        if self._index < len(self._control_list):
            self._control = self._control_list[self._index]
            self._index += 1
        else:
            print("JSON file has no more entries")

    def _record_control(self):
        new_record = {
            'control': {
                'throttle': self._control.throttle,
                'steer': self._control.steer,
                'brake': self._control.brake,
                'hand_brake': self._control.hand_brake,
                'reverse': self._control.reverse,
                'manual_gear_shift': self._control.manual_gear_shift,
                'gear': self._control.gear
            }
        }

        self._log_data['records'].append(new_record)

    def __del__(self):
        # Get ready to log user commands
        if self._mode == "log" and self._log_data:
            with open(self._endpoint, 'w') as fd:
                json.dump(self._log_data, fd, indent=4, sort_keys=True)


import math
import json
import pygame
from pygame.locals import *
from configparser import ConfigParser

class DualControl(object):
    """
    Combined keyboard and steering wheel control for the human agent
    Supports logging and playback modes
    """

    def __init__(self, path_to_conf_file):
        """
        Init
        """
        self._control = carla.VehicleControl()
        self._steer_cache = 0.0
        self._clock = pygame.time.Clock()
        
        # Initialize steering wheel
        pygame.joystick.init()
        joystick_count = pygame.joystick.get_count()
        if joystick_count > 0:
            self._joystick = pygame.joystick.Joystick(0)
            self._joystick.init()
            
            # Load wheel configuration
            self._parser = ConfigParser()
            self._parser.read('wheel_config.ini')
            self._steer_idx = int(self._parser.get('G29 Racing Wheel', 'steering_wheel'))
            self._throttle_idx = int(self._parser.get('G29 Racing Wheel', 'throttle'))
            self._brake_idx = int(self._parser.get('G29 Racing Wheel', 'brake'))
            self._reverse_idx = int(self._parser.get('G29 Racing Wheel', 'reverse'))
            self._handbrake_idx = int(self._parser.get('G29 Racing Wheel', 'handbrake'))
        else:
            self._joystick = None

        # Get the mode
        if "human" not in path_to_conf_file:
            with (open(path_to_conf_file, "r")) as f:
                lines = f.read().split("\n")
                self._mode = lines[0].split(" ")[1]
                self._endpoint = lines[1].split(" ")[1]

            # Get the needed vars
            if self._mode == "log":
                self._log_data = {'records': []}
            elif self._mode == "playback":
                self._index = 0
                self._control_list = []
                with open(self._endpoint) as fd:
                    try:
                        self._records = json.load(fd)
                        self._json_to_control()
                    except json.JSONDecodeError:
                        pass
        else:
            self._mode = "normal"
            self._endpoint = None

    def _json_to_control(self):
        # transform strs into VehicleControl commands
        for entry in self._records['records']:
            control = carla.VehicleControl(throttle=entry['control']['throttle'],
                                         steer=entry['control']['steer'],
                                         brake=entry['control']['brake'],
                                         hand_brake=entry['control']['hand_brake'],
                                         reverse=entry['control']['reverse'],
                                         manual_gear_shift=entry['control']['manual_gear_shift'],
                                         gear=entry['control']['gear'])
            self._control_list.append(control)

    def parse_events(self, timestamp):
        """
        Parse the keyboard and wheel events and set the vehicle controls accordingly
        """
        # Move the vehicle
        if self._mode == "playback":
            self._parse_json_control()
        else:
            self._parse_combined_controls(pygame.key.get_pressed(), timestamp*1000)

        # Record the control
        if self._mode == "log":
            self._record_control()

        return self._control

    def _parse_combined_controls(self, keys, milliseconds):
        """
        Calculate new vehicle controls based on input keys and wheel
        """
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return 
            elif event.type == pygame.KEYUP:
                if event.key == K_q:
                    self._control.gear = 1 if self._control.reverse else -1
                    self._control.reverse = self._control.gear < 0
            elif event.type == pygame.JOYBUTTONDOWN and self._joystick:
                if event.button == self._reverse_idx:
                    self._control.gear = 1 if self._control.reverse else -1
                    self._control.reverse = self._control.gear < 0

        # Use wheel controls if available, otherwise fall back to keyboard
        if self._joystick:
            self._parse_vehicle_wheel()
        else:
            self._parse_vehicle_keys(keys, milliseconds)

    def _parse_vehicle_keys(self, keys, milliseconds):
        if keys[K_UP] or keys[K_w]:
            self._control.throttle = 0.8
        else:
            self._control.throttle = 0.0

        steer_increment = 3e-4 * milliseconds
        if keys[K_LEFT] or keys[K_a]:
            self._steer_cache -= steer_increment
        elif keys[K_RIGHT] or keys[K_d]:
            self._steer_cache += steer_increment
        else:
            self._steer_cache = 0.0

        self._control.steer = round(self._steer_cache, 1)
        self._control.brake = 1.0 if keys[K_DOWN] or keys[K_s] else 0.0
        self._control.hand_brake = keys[K_SPACE]

    def _parse_vehicle_wheel(self):
        numAxes = self._joystick.get_numaxes()
        jsInputs = [float(self._joystick.get_axis(i)) for i in range(numAxes)]
        jsButtons = [float(self._joystick.get_button(i)) for i in range(self._joystick.get_numbuttons())]

        # Steering calculation
        K1 = 1.0
        steerCmd = K1 * math.tan(1.1 * jsInputs[self._steer_idx])
        self._control.steer = steerCmd

        # Throttle calculation
        K2 = 1.6
        throttleCmd = K2 + (2.05 * math.log10(-0.7 * jsInputs[self._throttle_idx] + 1.4) - 1.2) / 0.92
        self._control.throttle = max(0, min(1, throttleCmd))

        # Brake calculation
        brakeCmd = 1.6 + (2.05 * math.log10(-0.7 * jsInputs[self._brake_idx] + 1.4) - 1.2) / 0.92
        self._control.brake = max(0, min(1, brakeCmd))

        # Handbrake
        self._control.hand_brake = bool(jsButtons[self._handbrake_idx])

    def _parse_json_control(self):
        if self._index < len(self._control_list):
            self._control = self._control_list[self._index]
            self._index += 1
        else:
            print("JSON file has no more entries")

    def _record_control(self):
        new_record = {
            'control': {
                'throttle': self._control.throttle,
                'steer': self._control.steer,
                'brake': self._control.brake,
                'hand_brake': self._control.hand_brake,
                'reverse': self._control.reverse,
                'manual_gear_shift': self._control.manual_gear_shift,
                'gear': self._control.gear
            }
        }
        self._log_data['records'].append(new_record)

    def __del__(self):
        # Get ready to log user commands
        if self._mode == "log" and self._log_data:
            with open(self._endpoint, 'w') as fd:
                json.dump(self._log_data, fd, indent=4, sort_keys=True)
