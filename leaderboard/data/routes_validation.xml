<routes>
   <route id="0" town="Town13">
      <!-- Urbanization route -->
      <weathers>
         <!-- From slightly cloudy with a gentle breeze and a low-hanging fog to mild rain -->
         <weather route_percentage="0"
            cloudiness="5.0" precipitation="0.0" precipitation_deposits="50.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="15.0" fog_density="10.0"/>
         <weather route_percentage="100"
            cloudiness="5.0" precipitation="15.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="90.0" fog_density="2.0"/>
      </weathers>
      <waypoints>
         <position x="6090.4" y="4311.7" z="164.9"/>
         <position x="6092.0" y="4161.8" z="163.5"/>
         <position x="6509.8" y="4257.1" z="171.4"/>
         <position x="6622.8" y="4057.9" z="171.3"/>
         <position x="6850.0" y="3976.4" z="174.2"/>
         <position x="6906.5" y="3651.1" z="172.1"/>
         <position x="7058.9" y="3515.7" z="172.7"/>
         <position x="6958.8" y="2602.0" z="167.1"/>
         <position x="6819.8" y="2560.7" z="166.1"/>
         <position x="5800.4" y="1737.3" z="147.3"/>
         <position x="4615.8" y="831.6" z="163.0"/>
         <position x="3827.6" y="1022.8" z="180.4"/>
         <position x="3616.3" y="995.3" z="178.3"/>
         <position x="3360.5" y="879.6" z="174.5"/>
         <position x="3496.1" y="701.5" z="176.1"/>
         <position x="3622.4" y="655.4" z="177.7"/>
         <position x="2977.1" y="399.5" z="170.1"/>
         <position x="2714.3" y="1017.6" z="166.2"/>
      </waypoints>
      <scenarios>
         <scenario name="ParkingExit_1" type="ParkingExit">
            <trigger_point x="6090.4" y="4311.7" z="164.9" yaw="270.6"/>
            <direction value="right"/>
            <front_vehicle_distance value="9"/>
            <behind_vehicle_distance value="9"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="6091.8" y="4183.2" z="163.7" yaw="270.6"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="6290.5" y="4189.5" z="167.1" yaw="42.3"/>
            <distance value="100"/>
            <speed value="50"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="6456.6" y="4256.0" z="170.5" yaw="1.2"/>
            <distance value="100"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="6777.4" y="4305.7" z="176.2" yaw="90.5"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="6775.6" y="4513.3" z="178.1" yaw="90.5"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="6675.4" y="4250.9" z="174.1" yaw="-178.8"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="6623.2" y="4008.6" z="170.8" yaw="270.5"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="6541.3" y="2560.3" z="162.3" yaw="180.1"/>
         </scenario>
         <scenario name="BlockedIntersection_5" type="BlockedIntersection">
            <trigger_point x="3723.3" y="1026.5" z="179.6" yaw="178.0"/>
         </scenario>
         <scenario name="VehicleTurningRoute_5" type="VehicleTurningRoute">
            <trigger_point x="3622.9" y="628.7" z="177.6" yaw="271.0"/>
         </scenario>
         <scenario name="BlockedIntersection_7" type="BlockedIntersection">
            <trigger_point x="2787.7" y="1059.3" z="167.2" yaw="178.0"/>
         </scenario>
         <scenario name="HazardAtSideLane_1" type="HazardAtSideLane">
            <trigger_point x="7059.2" y="3493.1" z="172.5" yaw="-89.2"/>
            <distance value="55"/>
            <bicycle_drive_distance value="80"/>
            <bicycle_speed value="9"/>
         </scenario>
         <scenario name="PriorityAtJunction_2" type="PriorityAtJunction">
            <trigger_point x="6814.6" y="2560.7" z="166.0" yaw="180.1"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_2" type="OppositeVehicleRunningRedLight">
            <trigger_point x="6791.9" y="2560.7" z="165.8" yaw="180.1"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="6003.1" y="2175.9" z="150.8" yaw="297.7"/>
            <distance value="60"/>
            <frequency from="35" to="120"/>
         </scenario>
         <scenario name="ConstructionObstacle_3" type="ConstructionObstacle">
            <trigger_point x="6082.4" y="1921.8" z="150.4" yaw="211.2"/>
            <distance value="80"/>
            <speed value="50"/>
         </scenario>
         <scenario name="StaticCutIn_5" type="StaticCutIn">
            <trigger_point x="5060.9" y="1177.3" z="145.9" yaw="-136.4"/>
            <distance value="120"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_6" type="StaticCutIn">
            <trigger_point x="4822.8" y="951.6" z="154.1" yaw="-136.6"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="3945.7" y="1018.7" z="180.6" yaw="178.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3399.1" y="879.8" z="175.0" yaw="180.3"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3346.1" y="738.7" z="174.1" yaw="270.1"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3574.7" y="701.6" z="177.2" yaw="0.1"/>
            <flow_speed value="8"/>
            <source_dist_interval from="25" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3622.5" y="647.8" z="177.7" yaw="271.0"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="40"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3026.0" y="353.9" z="170.8" yaw="180.4"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="7040.8" y="4510.4" z="181.9" yaw="270.9"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="6698.7" y="2560.5" z="164.7" yaw="180.1"/>
            <distance value="60"/>
            <frequency from="45" to="120"/>
         </scenario>
         <scenario name="PedestrianCrossing_9" type="PedestrianCrossing">
            <trigger_point x="2968.90" y="637.11" z="169.53" yaw="93.64"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="6090.7" y="4289.8" z="164.7" yaw="270.6"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_1" type="ParkingCrossingPedestrian">
            <trigger_point x="6776.8" y="4376.9" z="176.9" yaw="90.5"/>
            <distance value="50"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_2" type="ParkingCrossingPedestrian">
            <trigger_point x="6825.8" y="4557.5" z="179.2" yaw="0.1"/>
            <distance value="50"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="7042.30" y="4421.61" z="181.28" yaw="270.94"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="7044.3" y="4299.9" z="180.3" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_1" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6621.2" y="4229.9" z="172.9" yaw="270.5"/>
            <distance value="65"/>
            <frequency from="35" to="100"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_3" type="ParkingCrossingPedestrian">
            <trigger_point x="6656.6" y="3977.4" z="171.1" yaw="-0.3"/>
            <distance value="50"/>
         </scenario>
         <scenario name="ParkingCutIn_3" type="ParkingCutIn">
            <trigger_point x="6753.2" y="3976.9" z="172.7" yaw="-0.3"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6846.1" y="3976.4" z="174.1" yaw="-0.3"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_2" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6904.2" y="3947.7" z="174.8" yaw="270.4"/>
            <distance value="70"/>
            <frequency from="35" to="100"/>
         </scenario>
         <scenario name="PedestrianCrossing_2" type="PedestrianCrossing">
            <trigger_point x="6904.92" y="3851.22" z="173.87" yaw="270.45"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_4" type="ParkingCrossingPedestrian">
            <trigger_point x="6905.7" y="3747.7" z="172.9" yaw="270.4"/>
            <distance value="60"/>
            <direction value="right"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="ParkingCutIn_4" type="ParkingCutIn">
            <trigger_point x="6928.8" y="3543.0" z="171.4" yaw="-0.6"/>
         </scenario>
         <scenario name="PedestrianCrossing_3" type="PedestrianCrossing">
            <trigger_point x="6989.0" y="3542.4" z="172.1" yaw="-0.6"/>
         </scenario>
         <scenario name="PedestrianCrossing_4" type="PedestrianCrossing">
            <trigger_point x="7060.45" y="3398.09" z="171.54" yaw="-89.25"/>
         </scenario>
         <scenario name="PriorityAtJunction_1" type="PriorityAtJunction">
            <trigger_point x="7060.5" y="3396.0" z="171.5" yaw="-89.2"/>
         </scenario>
         <scenario name="PedestrianCrossing_6" type="PedestrianCrossing">
            <trigger_point x="6324.7" y="2371.5" z="157.6" yaw="-151.3"/>
         </scenario>
         <scenario name="ParkingCutIn_5" type="ParkingCutIn">
            <trigger_point x="3616.0" y="1012.9" z="178.4" yaw="271.0"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_5" type="ParkingCrossingPedestrian">
            <trigger_point x="3557.6" y="880.5" z="177.3" yaw="180.3"/>
            <distance value="50"/>
         </scenario>
         <scenario name="ParkingCutIn_6" type="ParkingCutIn">
            <trigger_point x="3345.8" y="856.4" z="174.2" yaw="270.1"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_6" type="ParkingCrossingPedestrian">
            <trigger_point x="3378.2" y="701.2" z="174.5" yaw="0.1"/>
            <distance value="50"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_5" type="VehicleOpensDoorTwoWays">
            <trigger_point x="3572.2" y="604.2" z="177.0" yaw="180.7"/>
            <distance value="75"/>
            <frequency from="40" to="100"/>
         </scenario>
         <scenario name="PedestrianCrossing_8" type="PedestrianCrossing">
            <trigger_point x="3407.9" y="602.2" z="174.9" yaw="180.7"/>
         </scenario>
         <scenario name="ParkingCutIn_8" type="ParkingCutIn">
            <trigger_point x="3148.7" y="472.5" z="171.9" yaw="270.9"/>
         </scenario>
         <scenario name="BlockedIntersection_6" type="BlockedIntersection">
            <trigger_point x="3149.8" y="402.1" z="172.1" yaw="270.9"/>
         </scenario>
         <scenario name="PedestrianCrossing_5" type="PedestrianCrossing">
            <trigger_point x="7056.75" y="3155.51" z="169.25" yaw="-90.19"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="7057.3" y="3308.3" z="170.7" yaw="-90.2"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="7056.4" y="3046.2" z="168.4" yaw="269.8"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_3" type="StaticCutIn">
            <trigger_point x="5778.4" y="1725.8" z="147.1" yaw="215.4"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="PedestrianCrossing_7" type="PedestrianCrossing">
            <trigger_point x="4266.89" y="1010.63" z="175.11" yaw="177.99"/>
         </scenario>
         <scenario name="Accident_2" type="Accident">
            <trigger_point x="6634.7" y="4259.8" z="173.4" yaw="1.2"/>
            <distance value="50"/>
         </scenario>
         <scenario name="Accident_3" type="Accident">
            <trigger_point x="4635.0" y="807.6" z="162.6" yaw="129.9"/>
            <distance value="50"/>
            <speed value="50"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="5920.7" y="1819.3" z="148.2" yaw="213.4"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="5162.0" y="1268.0" z="144.4" yaw="219.7"/>
         </scenario>
         <scenario name="ConstructionObstacle_4" type="ConstructionObstacle">
            <trigger_point x="4181.0" y="1010.4" z="177.5" yaw="178.0"/>
            <distance value="50"/>
            <speed value="50"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="4531.4" y="981.6" z="164.8" yaw="111.6"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_9" type="ParkingCrossingPedestrian">
            <trigger_point x="2967.0" y="900.9" z="169.3" yaw="89.1"/>
            <distance value="40"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="2949.3" y="1053.6" z="169.3" yaw="178.0"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="2941.1" y="1053.9" z="169.2" yaw="178.0"/>
         </scenario>
         <scenario name="StaticCutIn_4" type="StaticCutIn">
            <trigger_point x="5357.8" y="1416.0" z="144.1" yaw="216.5"/>
            <distance value="120"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCutIn_2" type="ParkingCutIn">
            <trigger_point x="6879.2" y="4557.5" z="180.0" yaw="0.1"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ConstructionObstacle_2" type="ConstructionObstacle">
            <trigger_point x="7007.9" y="4258.0" z="179.4" yaw="-178.8"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_7" type="ParkingCrossingPedestrian">
            <trigger_point x="3346.5" y="590.7" z="174.1" yaw="270.1"/>
            <distance value="30"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3346.6" y="557.4" z="174.1" yaw="270.1"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_6" type="VehicleOpensDoorTwoWays">
            <trigger_point x="3316.75" y="513.01" z="173.85" yaw="180.59"/>
            <distance value="80"/>
            <frequency from="45" to="80"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3200.5" y="511.8" z="172.4" yaw="180.6"/>
            <flow_speed value="14"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_7" type="VehicleOpensDoorTwoWays">
            <trigger_point x="2965.6" y="710.4" z="169.4" yaw="91.5"/>
            <distance value="75"/>
            <frequency from="25" to="65"/>
         </scenario>
         <scenario name="ParkedObstacle_1" type="ParkedObstacle">
            <trigger_point x="6745.11" y="4252.39" z="175.22" yaw="-178.78"/>
            <distance value="66"/>
            <speed value="60"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="6735.1" y="4261.9" z="175.1" yaw="1.2"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="6859.0" y="4254.8" z="177.1" yaw="-178.8"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="6906.9" y="3599.6" z="171.6" yaw="270.4"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="7040.5" y="2848.5" z="167.4" yaw="259.5"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_2" type="SignalizedJunctionLeftTurn">
            <trigger_point x="6962.6" y="2609.8" z="167.1" yaw="244.3"/>
            <flow_speed value="12"/>
            <source_dist_interval from="20" to="45"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="6908.6" y="2560.8" z="166.9" yaw="180.1"/>
            <distance value="70"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_3" type="SignalizedJunctionLeftTurn">
            <trigger_point x="6515.2" y="2515.9" z="161.8" yaw="-72.3"/>
            <flow_speed value="14"/>
            <source_dist_interval from="12" to="55"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_3" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6505.9" y="2470.8" z="161.6" yaw="-151.3"/>
            <distance value="70"/>
            <frequency from="35" to="65"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_4" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6191.3" y="2298.4" z="154.5" yaw="-151.3"/>
            <distance value="65"/>
            <frequency from="40" to="70"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="6041.4" y="2216.2" z="151.5" yaw="-151.3"/>
            <flow_speed value="9"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="4731.3" y="864.5" z="158.5" yaw="-136.0"/>
            <flow_speed value="15"/>
            <source_dist_interval from="20" to="55"/>
         </scenario>
         <scenario name="Accident_4" type="Accident">
            <trigger_point x="3851.8" y="1021.9" z="180.5" yaw="178.0"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="ParkingCutIn_7" type="ParkingCutIn">
            <trigger_point x="3471.8" y="701.4" z="175.8" yaw="0.1"/>
         </scenario>
         <scenario name="ParkingCutIn_9" type="ParkingCutIn">
            <trigger_point x="2976.9" y="378.7" z="170.2" yaw="89.4"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_8" type="ParkingCrossingPedestrian">
            <trigger_point x="2977.1" y="482.5" z="169.9" yaw="91.0"/>
            <distance value="70"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2968.3" y="988.1" z="169.4" yaw="89.1"/>
            <flow_speed value="15"/>
            <source_dist_interval from="18" to="65"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="7010.8" y="2730.5" z="167.1" yaw="252.2"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="7009.5" y="2726.5" z="167.1" yaw="251.9"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="5540.1" y="1550.7" z="145.5" yaw="216.5"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="5535.8" y="1547.5" z="145.4" yaw="216.5"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="4483.3" y="1026.6" z="166.6" yaw="195.6"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="4481.1" y="1025.9" z="166.7" yaw="195.3"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3617.7" y="921.6" z="178.2" yaw="271.0"/>
         </scenario>
      </scenarios>
   </route>
   <route id="1" town="Town13">
      <!-- Urbanization route -->
      <!-- From a lightly cloudy day with a strong breeze and mild rain to a clear day with a gentle breeze -->
      <weathers>
         <weather route_percentage="0"
            cloudiness="5.0" precipitation="10.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="50.0" sun_azimuth_angle="-1.0" sun_altitude_angle="90.0" fog_density="2.0"/>
         <weather route_percentage="100"
            cloudiness="5.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="15.0" fog_density="10.0"/>
      </weathers>
      <waypoints>
         <position x="6770.8" y="5443.4" z="178.7"/>
         <position x="6978.1" y="5225.7" z="182.8"/>
         <position x="6795.8" y="5076.4" z="180.4"/>
         <position x="6779.4" y="4423.5" z="177.3"/>
         <position x="6593.3" y="4249.2" z="172.8"/>
         <position x="5788.6" y="4198.0" z="160.0"/>
         <position x="6086.5" y="4404.0" z="165.8"/>
         <position x="5597.4" y="5066.2" z="159.0"/>
         <position x="5516.2" y="4743.4" z="157.9"/>
         <position x="6273.6" y="4510.3" z="170.1"/>
         <position x="6500.9" y="4902.1" z="176.0"/>
         <position x="6920.7" y="4359.8" z="178.9"/>
      </waypoints>
      <scenarios>
         <scenario name="ParkingExit_1" type="ParkingExit">
            <trigger_point x="6770.8" y="5443.4" z="178.7" yaw="-89.5"/>
            <direction value="right"/>
            <front_vehicle_distance value="9"/>
            <behind_vehicle_distance value="9"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="6772.3" y="5263.0" z="179.4" yaw="-89.5"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="7036.6" y="4771.9" z="183.2" yaw="270.9"/>
            <flow_speed value="15"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_2" type="SignalizedJunctionLeftTurn">
            <trigger_point x="6772.5" y="4888.2" z="180.1" yaw="90.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="14" to="60"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_2" type="SignalizedJunctionRightTurn">
            <trigger_point x="6970.5" y="4931.4" z="182.8" yaw="-0.1"/>
            <flow_speed value="15"/>
            <source_dist_interval from="17" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_3" type="SignalizedJunctionRightTurn">
            <trigger_point x="6829.9" y="5076.7" z="180.9" yaw="180.6"/>
            <flow_speed value="12"/>
            <source_dist_interval from="18" to="50"/>
         </scenario>
         <scenario name="PriorityAtJunction_1" type="PriorityAtJunction">
            <trigger_point x="6776.5" y="4769.9" z="179.8" yaw="-89.5"/>
         </scenario>
         <scenario name="PriorityAtJunction_2" type="PriorityAtJunction">
            <trigger_point x="6777.9" y="4603.8" z="178.8" yaw="-89.5"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="6780.4" y="4302.5" z="176.2" yaw="-89.5"/>
            <flow_speed value="14"/>
            <source_dist_interval from="20" to="57"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_3" type="SignalizedJunctionLeftTurn">
            <trigger_point x="7048.7" y="4026.7" z="177.8" yaw="-89.1"/>
            <flow_speed value="15"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="6618.4" y="4213.6" z="172.8" yaw="90.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="PriorityAtJunction_3" type="PriorityAtJunction">
            <trigger_point x="6164.9" y="4123.3" z="164.4" yaw="-174.3"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="6160.9" y="4123.0" z="164.3" yaw="-175.3"/>
            <direction value="left"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_4" type="SignalizedJunctionLeftTurn">
            <trigger_point x="5853.2" y="4128.3" z="160.2" yaw="179.4"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_4" type="SignalizedJunctionRightTurn">
            <trigger_point x="6026.5" y="4359.1" z="164.3" yaw="0.0"/>
            <flow_speed value="8"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="PriorityAtJunction_4" type="PriorityAtJunction">
            <trigger_point x="5776.5" y="5014.3" z="162.4" yaw="90.9"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_6" type="SignalizedJunctionRightTurn">
            <trigger_point x="5774.4" y="5152.9" z="162.2" yaw="90.9"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_7" type="SignalizedJunctionRightTurn">
            <trigger_point x="5638.5" y="5217.8" z="159.4" yaw="180.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_5" type="SignalizedJunctionLeftTurn">
            <trigger_point x="5714.9" y="5067.7" z="161.2" yaw="0.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="20" to="45"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_2" type="OppositeVehicleRunningRedLight">
            <trigger_point x="5716.3" y="4509.7" z="160.5" yaw="0.1"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_3" type="OppositeVehicleRunningRedLight">
            <trigger_point x="6027.0" y="4510.0" z="165.5" yaw="0.1"/>
            <direction value="left"/>
         </scenario>
         <scenario name="PriorityAtJunction_5" type="PriorityAtJunction">
            <trigger_point x="6034.1" y="4510.0" z="165.7" yaw="0.1"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_9" type="SignalizedJunctionRightTurn">
            <trigger_point x="6272.2" y="4510.3" z="170.1" yaw="0.1"/>
            <flow_speed value="9"/>
            <source_dist_interval from="20" to="50"/>
         </scenario>
         <scenario name="PriorityAtJunction_6" type="PriorityAtJunction">
            <trigger_point x="6320.2" y="4706.6" z="172.4" yaw="90.9"/>
         </scenario>
         <scenario name="PriorityAtJunction_7" type="PriorityAtJunction">
            <trigger_point x="6315.4" y="5010.0" z="172.6" yaw="90.9"/>
         </scenario>
         <scenario name="PriorityAtJunction_8" type="PriorityAtJunction">
            <trigger_point x="6497.2" y="5140.1" z="175.3" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="7036.4" y="4782.6" z="183.3" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="6772.5" y="4880.5" z="180.1" yaw="90.5"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="7025.9" y="5024.4" z="183.6" yaw="90.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="7048.6" y="4036.0" z="177.9" yaw="-89.1"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6665.3" y="3974.3" z="171.3" yaw="179.7"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="5575.9" y="5107.7" z="158.4" yaw="256.7"/>
         </scenario>
         <scenario name="VehicleTurningRoute_5" type="VehicleTurningRoute">
            <trigger_point x="5782.5" y="4811.7" z="162.4" yaw="270.9"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="5509.0" y="4581.2" z="158.1" yaw="261.0"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_4" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6313.0" y="5165.0" z="172.0" yaw="90.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_6" type="VehicleTurningRoute">
            <trigger_point x="6505.5" y="4605.9" z="174.8" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_6" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6735.3" y="4557.4" z="177.9" yaw="0.1"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_4" type="ParkingCrossingPedestrian">
            <trigger_point x="6999.0" y="5078.5" z="183.2" yaw="180.6"/>
            <distance value="45"/>
            <direction value="right"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="Accident_4" type="Accident">
            <trigger_point x="5516.3" y="4737.5" z="157.9" yaw="271.2"/>
            <distance value="70"/>
            <direction value="right"/>
            <speed value="55"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="6798.8" y="5225.0" z="180.0" yaw="0.2"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="7032.1" y="5045.6" z="183.6" yaw="270.9"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="6778.5" y="4527.1" z="178.3" yaw="-89.5"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="5775.4" y="5088.5" z="162.3" yaw="90.9"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="6313.9" y="5110.0" z="172.2" yaw="90.9"/>
         </scenario>
         <scenario name="HardBreakRoute_10" type="HardBreakRoute">
            <trigger_point x="6818.1" y="4359.8" z="177.4" yaw="0.0"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_1" type="ParkingCrossingPedestrian">
            <trigger_point x="6794.7" y="5225.0" z="180.0" yaw="0.2"/>
            <distance value="55"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="7030.4" y="5150.8" z="183.6" yaw="270.9"/>
         </scenario>
         <scenario name="PedestrianCrossing_2" type="PedestrianCrossing">
            <trigger_point x="7032.9" y="4996.3" z="183.6" yaw="270.9"/>
         </scenario>
         <scenario name="ParkingCutIn_3" type="ParkingCutIn">
            <trigger_point x="6773.7" y="4745.7" z="179.7" yaw="90.5"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCutIn_4" type="ParkingCutIn">
            <trigger_point x="6773.1" y="4815.8" z="179.9" yaw="90.5"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_3" type="ParkingCrossingPedestrian">
            <trigger_point x="6792.5" y="4931.7" z="180.4" yaw="-0.1"/>
            <distance value="60"/>
            <direction value="right"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_2" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6774.0" y="5060.9" z="180.1" yaw="-89.5"/>
            <distance value="70"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_3" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6775.3" y="4910.8" z="180.1" yaw="-89.5"/>
            <distance value="60"/>
            <frequency from="40" to="115"/>
         </scenario>
         <scenario name="PedestrianCrossing_3" type="PedestrianCrossing">
            <trigger_point x="6777.77" y="4616.99" z="178.93" yaw="-89.51"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_6" type="ParkingCrossingPedestrian">
            <trigger_point x="7025.8" y="3972.5" z="176.9" yaw="179.7"/>
            <distance value="60"/>
            <direction value="right"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="ParkingCutIn_7" type="ParkingCutIn">
            <trigger_point x="6880.3" y="3973.2" z="174.7" yaw="179.7"/>
            <direction value="right"/>
         </scenario>
         <scenario name="PedestrianCrossing_4" type="PedestrianCrossing">
            <trigger_point x="6698.25" y="3974.15" z="171.81" yaw="179.71"/>
         </scenario>
         <scenario name="ParkingCutIn_8" type="ParkingCutIn">
            <trigger_point x="6620.3" y="3995.5" z="170.7" yaw="90.5"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_4" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5789.4" y="4144.5" z="159.7" yaw="90.9"/>
            <distance value="70"/>
            <frequency from="35" to="110"/>
         </scenario>
         <scenario name="PedestrianCrossing_5" type="PedestrianCrossing">
            <trigger_point x="5787.21" y="4292.51" z="160.40" yaw="90.85"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_8" type="ParkingCrossingPedestrian">
            <trigger_point x="5810.5" y="4359.0" z="161.0" yaw="0.0"/>
            <distance value="70"/>
            <direction value="right"/>
            <crossing_angle value="4"/>
         </scenario>
         <scenario name="ParkingCutIn_9" type="ParkingCutIn">
            <trigger_point x="6086.7" y="4382.7" z="165.6" yaw="90.6"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_5" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5779.9" y="4785.0" z="162.3" yaw="90.9"/>
            <distance value="70"/>
            <frequency from="35" to="100"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_11" type="ParkingCrossingPedestrian">
            <trigger_point x="5777.5" y="4941.4" z="162.5" yaw="90.9"/>
            <distance value="50"/>
            <direction value="right"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="PedestrianCrossing_8" type="PedestrianCrossing">
            <trigger_point x="5776.8" y="4989.7" z="162.5" yaw="90.9"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_12" type="ParkingCrossingPedestrian">
            <trigger_point x="5775.3" y="5094.0" z="162.3" yaw="90.9"/>
            <distance value="50"/>
            <direction value="right"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_13" type="ParkingCrossingPedestrian">
            <trigger_point x="5751.3" y="5218.2" z="161.5" yaw="180.2"/>
            <distance value="45"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCutIn_11" type="ParkingCutIn">
            <trigger_point x="5593.2" y="5066.1" z="158.9" yaw="0.7"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCutIn_13" type="ParkingCutIn">
            <trigger_point x="5763.6" y="4759.3" z="162.0" yaw="180.4"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_15" type="ParkingCrossingPedestrian">
            <trigger_point x="5512.3" y="4509.4" z="158.3" yaw="0.1"/>
            <distance value="45"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_16" type="ParkingCrossingPedestrian">
            <trigger_point x="5596.5" y="4509.5" z="159.1" yaw="0.1"/>
            <distance value="50"/>
            <direction value="right"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_17" type="ParkingCrossingPedestrian">
            <trigger_point x="6113.7" y="4510.1" z="167.2" yaw="0.1"/>
            <distance value="70"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_18" type="ParkingCrossingPedestrian">
            <trigger_point x="6323.0" y="4531.7" z="171.2" yaw="90.9"/>
            <distance value="70"/>
            <direction value="right"/>
         </scenario>
         <scenario name="PedestrianCrossing_10" type="PedestrianCrossing">
            <trigger_point x="6320.31" y="4699.91" z="172.37" yaw="90.90"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_7" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6318.9" y="4789.1" z="172.7" yaw="90.9"/>
            <distance value="75"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="PedestrianCrossing_11" type="PedestrianCrossing">
            <trigger_point x="6315.52" y="5004.84" z="172.65" yaw="90.90"/>
         </scenario>
         <scenario name="ParkingCutIn_14" type="ParkingCutIn">
            <trigger_point x="6331.6" y="5223.3" z="172.0" yaw="0.2"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_5" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6429.8" y="5223.6" z="173.7" yaw="0.2"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_19" type="ParkingCrossingPedestrian">
            <trigger_point x="6496.4" y="5197.8" z="175.0" yaw="270.9"/>
            <distance value="55"/>
            <direction value="right"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="PedestrianCrossing_12" type="PedestrianCrossing">
            <trigger_point x="6497.39" y="5130.45" z="175.35" yaw="270.88"/>
         </scenario>
         <scenario name="ParkingCutIn_15" type="ParkingCutIn">
            <trigger_point x="6498.5" y="5056.1" z="175.7" yaw="270.9"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_20" type="ParkingCrossingPedestrian">
            <trigger_point x="6499.5" y="4996.8" z="175.9" yaw="270.9"/>
            <distance value="65"/>
            <direction value="right"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="PedestrianCrossing_13" type="PedestrianCrossing">
            <trigger_point x="6501.97" y="4833.18" z="175.90" yaw="270.88"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_9" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6503.3" y="4744.8" z="175.6" yaw="270.9"/>
            <distance value="55"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_10" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6524.9" y="4557.1" z="174.7" yaw="0.1"/>
            <distance value="60"/>
            <frequency from="45" to="100"/>
         </scenario>
         <scenario name="ParkingCutIn_6" type="ParkingCutIn">
            <trigger_point x="6778.5" y="4536.2" z="178.3" yaw="-89.5"/>
         </scenario>
         <scenario name="VehicleTurningRoute_4" type="VehicleTurningRoute">
            <trigger_point x="5857.6" y="4128.2" z="160.2" yaw="179.4"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="5787.0" y="4304.7" z="160.5" yaw="90.9"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_10" type="ParkingCrossingPedestrian">
            <trigger_point x="6045.3" y="4507.0" z="165.9" yaw="180.1"/>
            <distance value="70"/>
         </scenario>
         <scenario name="PedestrianCrossing_6" type="PedestrianCrossing">
            <trigger_point x="5829.00" y="4506.80" z="162.09" yaw="180.06"/>
         </scenario>
         <scenario name="ParkingCutIn_10" type="ParkingCutIn">
            <trigger_point x="5783.0" y="4574.7" z="161.7" yaw="90.9"/>
         </scenario>
         <scenario name="PedestrianCrossing_7" type="PedestrianCrossing">
            <trigger_point x="5781.12" y="4701.33" z="162.14" yaw="90.85"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_5" type="ParkingCrossingPedestrian">
            <trigger_point x="6778.8" y="4490.5" z="177.9" yaw="-89.5"/>
            <distance value="60"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="6591.4" y="4249.1" z="172.7" yaw="-178.8"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="6414.6" y="4242.3" z="169.8" yaw="-169.5"/>
            <distance value="100"/>
            <speed value="45"/>
         </scenario>
         <scenario name="Accident_2" type="Accident">
            <trigger_point x="6246.9" y="4147.5" z="165.9" yaw="-152.9"/>
            <distance value="70"/>
            <speed value="47"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_1" type="VehicleOpensDoorTwoWays">
            <trigger_point x="7012.2" y="4725.1" z="182.8" yaw="180.1"/>
            <distance value="70"/>
            <frequency from="40" to="80"/>
         </scenario>
         <scenario name="ParkingCutIn_12" type="ParkingCutIn">
            <trigger_point x="5779.0" y="5044.5" z="162.4" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_6" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5780.5" y="4947.4" z="162.5" yaw="270.9"/>
            <distance value="70"/>
            <frequency from="45" to="90"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="6770.9" y="5424.9" z="178.8" yaw="-89.5"/>
         </scenario>
         <scenario name="ParkingCutIn_2" type="ParkingCutIn">
            <trigger_point x="6886.0" y="5225.3" z="181.4" yaw="0.2"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_2" type="ParkingCrossingPedestrian">
            <trigger_point x="6922.6" y="4724.9" z="181.6" yaw="180.1"/>
            <distance value="60"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6809.2" y="4724.6" z="180.1" yaw="180.1"/>
         </scenario>
         <scenario name="ParkingCutIn_5" type="ParkingCutIn">
            <trigger_point x="6905.3" y="4931.5" z="181.9" yaw="-0.1"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="7026.8" y="4974.2" z="183.6" yaw="90.9"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="6874.1" y="5077.2" z="181.5" yaw="180.6"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="6960.4" y="4263.5" z="178.7" yaw="1.2"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="6971.6" y="3972.8" z="176.1" yaw="179.7"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_7" type="ParkingCrossingPedestrian">
            <trigger_point x="6619.4" y="4098.4" z="171.6" yaw="90.5"/>
            <distance value="75"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_5" type="SignalizedJunctionRightTurn">
            <trigger_point x="6086.1" y="4440.1" z="166.1" yaw="90.6"/>
            <flow_speed value="13"/>
            <source_dist_interval from="19" to="50"/>
         </scenario>
         <scenario name="Accident_3" type="Accident">
            <trigger_point x="5593.9" y="5201.4" z="158.5" yaw="261.6"/>
            <distance value="65"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_14" type="ParkingCrossingPedestrian">
            <trigger_point x="5640.1" y="5066.7" z="159.8" yaw="0.7"/>
            <distance value="50"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_8" type="SignalizedJunctionRightTurn">
            <trigger_point x="5577.4" y="4758.1" z="158.8" yaw="180.4"/>
            <flow_speed value="12"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="6226.1" y="4510.2" z="169.2" yaw="0.1"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_8" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6317.0" y="4909.0" z="172.8" yaw="90.9"/>
            <distance value="60"/>
            <frequency from="45" to="65"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="6503.5" y="4730.8" z="175.6" yaw="270.9"/>
         </scenario>
         <scenario name="ParkingCutIn_16" type="ParkingCutIn">
            <trigger_point x="6638.1" y="4557.2" z="176.4" yaw="0.1"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="7045.6" y="4222.2" z="179.6" yaw="-89.1"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="7034.3" y="4910.9" z="183.5" yaw="270.9"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="7034.4" y="4907.6" z="183.5" yaw="270.9"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="6820.6" y="4260.5" z="176.4" yaw="1.2"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="6824.5" y="4260.6" z="176.5" yaw="1.2"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="6059.8" y="4126.2" z="162.7" yaw="179.4"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="6027.2" y="4126.6" z="162.3" yaw="179.4"/>
         </scenario>
         <scenario name="HardBreakRoute_11" type="HardBreakRoute">
            <trigger_point x="5971.9" y="4359.1" z="163.3" yaw="0.0"/>
         </scenario>
      </scenarios>
   </route>
   <route id="2" town="Town13">
      <!-- Urbanization + residential -->
      <weathers>
         <!-- From a clear day with a gentle breeze and light fog to a slightly cloudy dusk with low wind and moderately dense fog -->
         <weather route_percentage="0"
            cloudiness="0.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="90.0" fog_density="2.0"/>
         <weather route_percentage="100"
            cloudiness="10.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="0.0" fog_density="15.0"/>
      </weathers>
      <waypoints>
         <position x="2536.1" y="2284.9" z="172.5"/>
         <position x="2990.8" y="2385.8" z="180.1"/>
         <position x="2905.5" y="2574.9" z="180.4"/>
         <position x="2898.5" y="2643.0" z="180.5"/>
         <position x="2959.3" y="2861.5" z="182.3"/>
         <position x="2862.8" y="2895.8" z="180.5"/>
         <position x="2848.2" y="2904.4" z="180.1"/>
         <position x="2642.0" y="3003.3" z="174.6"/>
         <position x="2654.6" y="3295.6" z="174.4"/>
         <position x="2922.3" y="3227.3" z="181.2"/>
         <position x="3152.7" y="3234.2" z="182.4"/>
         <position x="3268.1" y="3146.8" z="181.3"/>
         <position x="3412.6" y="3218.4" z="175.5"/>
         <position x="3038.1" y="3304.4" z="182.1"/>
         <position x="2971.1" y="3537.5" z="180.7"/>
         <position x="2810.6" y="3571.4" z="178.1"/>
         <position x="2767.1" y="3530.9" z="177.0"/>
         <position x="2632.7" y="3472.7" z="173.4"/>
         <position x="2797.3" y="3574.0" z="177.8"/>
         <position x="2933.2" y="3639.0" z="180.3"/>
         <position x="2959.1" y="3681.2" z="180.6"/>
         <position x="2920.1" y="3878.9" z="181.4"/>
         <position x="2880.1" y="3971.0" z="181.5"/>
         <position x="3065.0" y="4003.9" z="183.7"/>
         <position x="3170.9" y="4067.7" z="184.0"/>
         <position x="3513.3" y="4079.0" z="172.6"/>
         <position x="3373.3" y="4320.7" z="182.6"/>
         <position x="3095.6" y="4279.4" z="186.4"/>
         <position x="3057.4" y="4372.3" z="186.6"/>
         <position x="2926.2" y="4441.1" z="184.7"/>
         <position x="2640.4" y="4524.8" z="175.8"/>
         <position x="2005.5" y="5144.1" z="149.6"/>
         <position x="2119.5" y="5431.1" z="150.2"/>
         <position x="2282.4" y="5606.1" z="151.9"/>
         <position x="2632.3" y="5636.8" z="157.6"/>
         <position x="2792.9" y="5584.2" z="161.3"/>
      </waypoints>
      <scenarios>
         <scenario name="ParkingExit_1" type="ParkingExit">
            <trigger_point x="2536.1" y="2284.9" z="172.5" yaw="0.5"/>
            <front_vehicle_distance value="9"/>
            <behind_vehicle_distance value="9"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="2821.1" y="2287.4" z="177.8" yaw="0.5"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2974.7" y="2790.5" z="182.3" yaw="62.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="2691.2" y="2960.3" z="176.1" yaw="160.4"/>
            <flow_speed value="12"/>
            <source_dist_interval from="17" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="2637.1" y="3251.5" z="174.0" yaw="91.1"/>
            <flow_speed value="13"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_2" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="2764.3" y="3298.9" z="177.5" yaw="1.7"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="2940.7" y="3227.8" z="181.5" yaw="1.7"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3199.3" y="3187.2" z="182.2" yaw="270.8"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_2" type="SignalizedJunctionRightTurn">
            <trigger_point x="3398.4" y="3176.6" z="176.8" yaw="27.6"/>
            <flow_speed value="12"/>
            <source_dist_interval from="15" to="65"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3027.6" y="3304.1" z="182.1" yaw="181.7"/>
            <flow_speed value="9"/>
            <source_dist_interval from="17" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2971.5" y="3535.6" z="180.7" yaw="103.4"/>
            <flow_speed value="8"/>
            <source_dist_interval from="20" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2910.6" y="3575.7" z="179.9" yaw="0.9"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_2" type="SignalizedJunctionLeftTurn">
            <trigger_point x="2899.1" y="3912.6" z="181.4" yaw="126.7"/>
            <flow_speed value="12"/>
            <source_dist_interval from="17" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3064.4" y="4032.7" z="184.0" yaw="91.2"/>
            <flow_speed value="6"/>
            <source_dist_interval from="12" to="50"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="3270.6" y="4069.1" z="182.2" yaw="0.8"/>
            <direction value="left"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_6" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3471.8" y="4054.0" z="174.4" yaw="-13.6"/>
            <flow_speed value="6"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3105.1" y="4279.7" z="186.4" yaw="181.3"/>
            <flow_speed value="8"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_7" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3056.6" y="4409.7" z="186.6" yaw="91.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="20" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_6" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="2837.8" y="4439.9" z="182.7" yaw="180.7"/>
            <flow_speed value="11"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_8" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2810.5" y="4565.2" z="181.0" yaw="91.7"/>
            <flow_speed value="6"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_9" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2679.1" y="4588.1" z="176.5" yaw="181.4"/>
            <flow_speed value="10"/>
            <source_dist_interval from="17" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_7" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="1984.6" y="5181.5" z="149.2" yaw="123.5"/>
            <flow_speed value="13"/>
            <source_dist_interval from="18" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_10" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2282.0" y="5558.2" z="152.0" yaw="-90.4"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_11" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2590.1" y="5529.2" z="157.9" yaw="0.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="18" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_8" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="2997.7" y="5577.8" z="165.4" yaw="-16.8"/>
            <flow_speed value="12"/>
            <source_dist_interval from="13" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_12" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3058.2" y="5454.9" z="169.0" yaw="-89.3"/>
            <flow_speed value="9"/>
            <source_dist_interval from="20" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_9" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3204.9" y="5416.1" z="172.1" yaw="-5.5"/>
            <flow_speed value="8"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_10" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3091.6" y="5414.6" z="170.5" yaw="180.3"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_13" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3054.8" y="5510.7" z="167.7" yaw="90.7"/>
            <flow_speed value="12"/>
            <source_dist_interval from="13" to="50"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="2584.8" y="2285.4" z="173.7" yaw="0.5"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="3015.2" y="2385.8" z="180.3" yaw="-0.0"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="2901.8" y="2579.2" z="180.3" yaw="131.5"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="3341.8" y="3313.3" z="177.3" yaw="126.7"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="2729.5" y="3677.0" z="176.3" yaw="1.0"/>
         </scenario>
         <scenario name="BlockedIntersection_5" type="BlockedIntersection">
            <trigger_point x="2764.7" y="3609.7" z="177.0" yaw="272.8"/>
         </scenario>
         <scenario name="BlockedIntersection_6" type="BlockedIntersection">
            <trigger_point x="3024.7" y="3974.2" z="183.2" yaw="1.3"/>
         </scenario>
         <scenario name="BlockedIntersection_7" type="BlockedIntersection">
            <trigger_point x="3422.2" y="4327.4" z="180.8" yaw="142.6"/>
         </scenario>
         <scenario name="VehicleTurningRoute_5" type="VehicleTurningRoute">
            <trigger_point x="2640.6" y="4482.5" z="176.2" yaw="270.3"/>
         </scenario>
         <scenario name="BlockedIntersection_8" type="BlockedIntersection">
            <trigger_point x="2060.5" y="5228.1" z="150.0" yaw="0.6"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="2120.6" y="5654.0" z="149.8" yaw="89.7"/>
         </scenario>s
         <scenario name="VehicleTurningRoute_6" type="VehicleTurningRoute">
            <trigger_point x="2991.2" y="5579.8" z="165.2" yaw="-17.2"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3234.8" y="5320.0" z="174.7" yaw="270.6"/>
         </scenario>
         <scenario name="VehicleTurningRoute_8" type="VehicleTurningRoute">
            <trigger_point x="3193.9" y="5267.2" z="175.5" yaw="181.3"/>
         </scenario>
         <scenario name="VehicleTurningRoute_9" type="VehicleTurningRoute">
            <trigger_point x="2834.5" y="5637.7" z="161.3" yaw="152.3"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_1" type="ParkingCrossingPedestrian">
            <trigger_point x="2671.5" y="2286.1" z="175.6" yaw="0.5"/>
            <distance value="50"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_2" type="ParkingCrossingPedestrian">
            <trigger_point x="2881.24" y="2385.84" z="179.04" yaw="-0.00"/>
            <distance value="50"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="3035.2" y="2423.6" z="180.8" yaw="130.6"/>
            <distance value="55"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="2884.3" y="2615.4" z="180.2" yaw="62.7"/>
            <distance value="66"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="2642.1" y="2999.4" z="174.6" yaw="91.1"/>
            <distance value="50"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="6"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="3000.7" y="3229.6" z="182.3" yaw="1.7"/>
            <distance value="50"/>
            <blocker_model value="static.prop.busstoplb"/>
            <crossing_angle value="4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_6" type="DynamicObjectCrossing">
            <trigger_point x="2984.5" y="3354.7" z="181.5" yaw="90.4"/>
            <distance value="55"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="6"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_7" type="DynamicObjectCrossing">
            <trigger_point x="2779.6" y="3573.7" z="177.4" yaw="0.9"/>
            <distance value="60"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_8" type="DynamicObjectCrossing">
            <trigger_point x="2887.9" y="3971.2" z="181.7" yaw="1.3"/>
            <distance value="60"/>
            <blocker_model value="static.prop.busstoplb"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_9" type="DynamicObjectCrossing">
            <trigger_point x="3291.9" y="4286.3" z="184.6" yaw="190.1"/>
            <distance value="50"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_10" type="DynamicObjectCrossing">
            <trigger_point x="3046.8" y="4442.6" z="186.4" yaw="180.7"/>
            <distance value="45"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_11" type="DynamicObjectCrossing">
            <trigger_point x="2813.7" y="4454.6" z="182.1" yaw="91.7"/>
            <distance value="50"/>
            <blocker_model value="static.prop.busstoplb"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_12" type="DynamicObjectCrossing">
            <trigger_point x="2119.0" y="5324.1" z="150.5" yaw="89.7"/>
            <distance value="50"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_13" type="DynamicObjectCrossing">
            <trigger_point x="2307.00" y="5525.60" z="152.54" yaw="0.7"/>
            <distance value="70"/>
            <blocker_model value="static.prop.container"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_14" type="DynamicObjectCrossing">
            <trigger_point x="2632.8" y="5541.5" z="158.6" yaw="90.3"/>
            <distance value="45"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_15" type="DynamicObjectCrossing">
            <trigger_point x="3234.0" y="5397.8" z="172.8" yaw="270.6"/>
            <distance value="45"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="6"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="2816.8" y="3224.1" z="179.0" yaw="1.7"/>
            <distance value="65"/>
            <frequency from="35" to="120"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="3170.66" y="3308.37" z="181.61" yaw="181.73"/>
            <distance value="65"/>
            <frequency from="50" to="115"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="2927.56" y="3573.23" z="180.16" yaw="180.88"/>
            <distance value="70"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="3077.7" y="4066.5" z="184.4" yaw="0.8"/>
            <distance value="70"/>
            <frequency from="50" to="110"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="3325.4" y="4069.7" z="180.7" yaw="-0.1"/>
            <distance value="70"/>
            <frequency from="55" to="120"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="3032.06" y="5561.50" z="166.19" yaw="165.54"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="3227.7" y="3146.0" z="182.1" yaw="1.1"/>
            <distance value="70"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="2767.1" y="3561.2" z="177.0" yaw="272.8"/>
            <distance value="70"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="2631.1" y="3551.8" z="173.3" yaw="91.1"/>
            <distance value="70"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="3506.2" y="4055.1" z="172.6" yaw="70.6"/>
            <distance value="60"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="2146.0" y="5698.7" z="150.0" yaw="1.0"/>
            <distance value="75"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="2539.7" y="2285.0" z="172.6" yaw="0.5"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="2817.0" y="5654.5" z="160.8" yaw="-29.2"/>
            <distance value="113"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="3006.2" y="2457.5" z="180.9" yaw="130.6"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="2640.1" y="3099.3" z="174.4" yaw="91.1"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="2639.21" y="3143.18" z="174.27" yaw="91.13"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="3265.3" y="3322.0" z="179.7" yaw="199.6"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="3039.5" y="3718.7" z="181.3" yaw="126.7"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="3476.2" y="4272.2" z="177.6" yaw="125.1"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="3347.4" y="4304.9" z="183.3" yaw="207.0"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="2971.6" y="4441.7" z="185.5" yaw="180.7"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="2964.9" y="4441.6" z="185.4" yaw="180.7"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="2895.4" y="3917.5" z="181.3" yaw="126.7"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="3064.6" y="4025.0" z="183.9" yaw="91.2"/>
         </scenario>
         <scenario name="VehicleTurningRoute_4" type="VehicleTurningRoute">
            <trigger_point x="3111.0" y="4279.8" z="186.4" yaw="181.3"/>
         </scenario>
         <scenario name="VehicleTurningRoute_7" type="VehicleTurningRoute">
            <trigger_point x="3208.6" y="5415.7" z="172.2" yaw="-6.1"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="2859.3" y="2300.9" z="178.3" yaw="90.2"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="2493.3" y="4451.4" z="170.7" yaw="126.7"/>
            <distance value="75"/>
            <speed value="50"/>
         </scenario>
         <scenario name="ConstructionObstacle_2" type="ConstructionObstacle">
            <trigger_point x="2363.7" y="4627.9" z="163.3" yaw="126.0"/>
            <distance value="75"/>
            <direction value="right"/>
            <speed value="55"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="2218.1" y="4828.7" z="155.8" yaw="125.6"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2990.3" y="3681.8" z="180.9" yaw="1.1"/>
            <flow_speed value="14"/>
            <source_dist_interval from="15" to="65"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_3" type="ParkingCrossingPedestrian">
            <trigger_point x="2968.36" y="2385.84" z="179.93" yaw="-0.00"/>
            <distance value="52"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="2995.7" y="2469.8" z="180.9" yaw="130.6"/>
            <distance value="80"/>
            <frequency from="45" to="95"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="2936.3" y="2716.1" z="181.5" yaw="62.7"/>
            <distance value="90"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="3156.2" y="3234.3" z="182.4" yaw="1.7"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="3420.9" y="3207.2" z="175.3" yaw="126.7"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="2796.1" y="4591.0" z="180.2" yaw="181.4"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="2792.4" y="4590.9" z="180.1" yaw="181.4"/>
         </scenario>
         <scenario name="HardBreakRoute_12" type="HardBreakRoute">
            <trigger_point x="2209.4" y="4840.9" z="155.4" yaw="125.4"/>
         </scenario>
         <scenario name="BlockedIntersection_9" type="BlockedIntersection">
            <trigger_point x="3144.9" y="5354.6" z="172.8" yaw="91.2"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="2847.2" y="2904.8" z="180.1" yaw="160.4"/>
            <distance value="112"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="2656.2" y="5742.2" z="157.0" yaw="-22.4"/>
         </scenario>
         <scenario name="HardBreakRoute_13" type="HardBreakRoute">
            <trigger_point x="2677.5" y="5732.8" z="157.4" yaw="-25.1"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="2990.3" y="2850.5" z="182.8" yaw="160.4"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="2983.4" y="2852.9" z="182.7" yaw="160.4"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="2800.6" y="3255.8" z="178.5" yaw="270.4"/>
         </scenario>
         <scenario name="HardBreakRoute_10" type="HardBreakRoute">
            <trigger_point x="2640.2" y="4569.2" z="175.3" yaw="270.3"/>
         </scenario>
         <scenario name="HardBreakRoute_11" type="HardBreakRoute">
            <trigger_point x="2618.1" y="4437.1" z="175.7" yaw="180.7"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="2624.7" y="4437.2" z="176.0" yaw="180.7"/>
         </scenario>
      </scenarios>
   </route>
   <route id="3" town="Town13">
      <!-- Urbanization + highway -->
      <weathers>
         <!-- From a partly cloudy day with light rain, a soft breeze, and low fog to a mostly cloudy day with dry conditions -->
         <weather route_percentage="0"
            cloudiness="15.0" precipitation="20.0" precipitation_deposits="60.0" wetness="0.0"
            wind_intensity="5.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="3.0"/>
         <weather route_percentage="100"
            cloudiness="80.0" precipitation="0.0" precipitation_deposits="30.0" wetness="20.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="2.0"/>
      </weathers>
      <waypoints>
         <position x="249.8" y="1319.6" z="148.1"/>
         <position x="1051.9" y="1127.1" z="156.6"/>
         <position x="1156.7" y="1158.8" z="157.0"/>
         <position x="1171.88" y="1186.19" z="157.00"/>
         <position x="1211.61" y="1220.18" z="157.00"/>
         <position x="1238.82" y="1230.10" z="157.00"/>
         <position x="1271.54" y="1232.66" z="157.00"/>
         <position x="1313.3" y="1221.7" z="157.0"/>
         <position x="1332.30" y="1210.21" z="157.00"/>
         <position x="1348.13" y="1195.90" z="157.00"/>
         <position x="1360.14" y="1180.28" z="157.00"/>
         <position x="1374.4" y="1149.5" z="157.0"/>
         <position x="1732.3" y="1103.0" z="151.7"/>
         <position x="1785.1" y="1104.1" z="151.8"/>
         <position x="2010.1" y="1087.2" z="154.4"/>
         <position x="1965.7" y="1091.4" z="153.7"/>
         <position x="1444.0" y="1110.9" z="157.0"/>
         <position x="1371.5" y="1079.2" z="157.0"/>
         <position x="1363.53" y="1062.94" z="157.00"/>
         <position x="1352.28" y="1046.45" z="157.00"/>
         <position x="1339.12" y="1032.86" z="157.00"/>
         <position x="1326.02" y="1022.98" z="157.00"/>
         <position x="1308.7" y="1013.8" z="157.0"/>
         <position x="1279.57" y="1005.72" z="157.00"/>
         <position x="1249.25" y="1005.60" z="157.00"/>
         <position x="1221.62" y="1012.81" z="157.00"/>
         <position x="1203.81" y="1021.80" z="157.00"/>
         <position x="1193.1" y="1029.3" z="157.0"/>
         <position x="1166.28" y="1059.80" z="157.00"/>
         <position x="1150.9" y="1103.6" z="157.0"/>
         <position x="1149.86" y="1119.44" z="157.00"/>
         <position x="1151.16" y="1135.99" z="157.00"/>
         <position x="1162.19" y="1170.38" z="157.00"/>
         <position x="1173.9" y="1189.1" z="157.0"/>
         <position x="1253.4" y="1401.8" z="148.7"/>
         <position x="1255.9" y="1465.1" z="148.7"/>
         <position x="1258.9" y="1489.8" z="148.7"/>
         <position x="1256.4" y="1632.2" z="148.7"/>
         <position x="1259.6" y="1651.5" z="148.7"/>
         <position x="1257.1" y="1791.9" z="148.6"/>
         <position x="1260.3" y="1810.8" z="148.6"/>
         <position x="2043.4" y="3620.8" z="169.7"/>
         <position x="2053.7" y="3642.3" z="170.0"/>
         <position x="2063.4" y="3663.3" z="170.3"/>
         <position x="2342.3" y="4240.1" z="180.4"/>
         <position x="2355.7" y="4274.9" z="180.8"/>
         <position x="2403.2" y="5140.1" z="172.8"/>
         <position x="2398.9" y="5167.8" z="172.5"/>
         <position x="2368.4" y="6122.1" z="166.1"/>
         <position x="1999.9" y="6451.8" z="153.5"/>
         <position x="1962.3" y="6455.2" z="152.5"/>
         <position x="1944.1" y="6458.6" z="152.0"/>
         <position x="1383.7" y="6432.5" z="148.3"/>
         <position x="1361.7" y="6425.0" z="148.4"/>
         <position x="1348.3" y="6419.0" z="148.6"/>
         <position x="962.5" y="6296.4" z="156.5"/>
         <position x="210.2" y="6088.2" z="164.3"/>
         <position x="66.1" y="6087.9" z="162.5"/>
         <position x="-134.3" y="6084.4" z="159.3"/>
         <position x="-528.6" y="5921.3" z="151.9"/>
         <position x="-671.8" y="5892.0" z="149.7"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="256.8" y="1204.0" z="148.2" yaw="-86.5"/>
            <flow_speed value="8"/>
            <source_dist_interval from="13" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="1978.3" y="1094.3" z="153.9" yaw="-1.2"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2174.7" y="949.0" z="157.2" yaw="-14.2"/>
            <flow_speed value="7"/>
            <source_dist_interval from="10" to="70"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2232.7" y="1028.2" z="158.1" yaw="90.5"/>
            <flow_speed value="9"/>
            <source_dist_interval from="18" to="56"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="2111.0" y="1083.5" z="155.9" yaw="177.8"/>
         </scenario>
         <scenario name="PriorityAtJunction_1" type="PriorityAtJunction">
            <trigger_point x="2085.3" y="1084.5" z="155.5" yaw="177.8"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="2092.51" y="962.68" z="155.84" yaw="-4.70"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="2040.8" y="1004.4" z="154.9" yaw="270.5"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="2233.5" y="947.5" z="158.3" yaw="90.5"/>
         </scenario>
         <scenario name="MergerIntoSlowTraffic_1" type="MergerIntoSlowTraffic">
            <trigger_point x="1129.0" y="1128.4" z="157.0" yaw="20.9"/>
            <start_actor_flow x="1150.7" y="1104.8" z="157.0"/>
            <end_actor_flow x="1330.9" y="1211.2" z="157.0"/>
            <flow_speed value="10"/>
            <source_dist_interval from="25" to="55"/>
         </scenario>
         <scenario name="MergerIntoSlowTrafficV2_1" type="MergerIntoSlowTrafficV2">
            <trigger_point x="1317.35" y="1219.67" z="157.00" yaw="-27.88"/>
            <start_actor_flow x="1339.1" y="1245.6" z="156.6"/>
            <end_actor_flow x="1402.6" y="1118.2" z="157.0"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="30"/>
         </scenario>
         <scenario name="MergerIntoSlowTrafficV2_2" type="MergerIntoSlowTrafficV2">
            <trigger_point x="1218.7" y="1014.0" z="157.0" yaw="156.6"/>
            <start_actor_flow x="1194.1" y="988.8" z="155.9"/>
            <end_actor_flow x="1208.0" y="1270.9" z="155.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="17" to="38"/>
         </scenario>
         <scenario name="EnterActorFlow_1" type="EnterActorFlow">
            <trigger_point x="1195.3" y="1233.4" z="157.0" yaw="70.6"/>
            <start_actor_flow x="1260.0" y="1258.4" z="148.8"/>
            <end_actor_flow x="1256.6" y="1620.2" z="148.7"/>
            <flow_speed value="17"/>
            <source_dist_interval from="20" to="75"/>
         </scenario>
         <scenario name="MergerIntoSlowTraffic_2" type="MergerIntoSlowTraffic">
            <trigger_point x="-377.2" y="5990.9" z="154.6" yaw="-144.6"/>
            <start_actor_flow x="-373.2" y="5946.7" z="154.6"/>
            <end_actor_flow x="-687.3" y="5892.9" z="149.4"/>
            <flow_speed value="10"/>
            <source_dist_interval from="20" to="35"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_1" type="YieldToEmergencyVehicle">
            <trigger_point x="1257.7" y="1955.9" z="148.6" yaw="91.0"/>
            <distance value="130"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_2" type="YieldToEmergencyVehicle">
            <trigger_point x="1483.3" y="2895.1" z="164.4" yaw="60.9"/>
            <distance value="150"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="2050.8" y="964.4" z="155.2" yaw="-0.3"/>
            <distance value="50"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="322.0" y="1138.2" z="148.1" yaw="3.6"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="1723.0" y="1100.1" z="151.9" yaw="177.7"/>
            <distance value="75"/>
            <frequency from="40" to="100"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="1870.8" y="3393.1" z="167.0" yaw="49.9"/>
            <distance value="80"/>
            <speed value="55"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="2160.4" y="3822.9" z="173.0" yaw="60.6"/>
            <distance value="80"/>
            <direction value="right"/>
            <speed value="60"/>
         </scenario>
         <scenario name="Accident_2" type="Accident">
            <trigger_point x="2381.8" y="5703.1" z="168.1" yaw="91.8"/>
            <distance value="75"/>
         </scenario>
         <scenario name="Accident_3" type="Accident">
            <trigger_point x="1949.4" y="6458.6" z="152.1" yaw="-179.8"/>
            <distance value="70"/>
            <speed value="45"/>
         </scenario>
         <scenario name="ConstructionObstacle_3" type="ConstructionObstacle">
            <trigger_point x="1086.6" y="6346.8" z="153.1" yaw="-160.1"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="1258.1" y="1734.3" z="148.6" yaw="91.0"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="1890.4" y="3416.6" z="167.2" yaw="50.5"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="2074.3" y="3680.1" z="170.5" yaw="57.2"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="2226.4" y="3947.4" z="175.5" yaw="63.6"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="2232.7" y="3960.2" z="175.7" yaw="63.8"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="2360.2" y="4291.7" z="181.0" yaw="75.4"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="2415.0" y="4771.7" z="178.7" yaw="91.5"/>
         </scenario>
         <scenario name="HardBreakRoute_13" type="HardBreakRoute">
            <trigger_point x="1500.3" y="6448.4" z="147.9" yaw="-174.1"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="1107.1" y="6354.1" z="152.5" yaw="-160.8"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="678.1" y="6171.0" z="163.7" yaw="-161.0"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="2348.0" y="4247.1" z="180.5" yaw="73.9"/>
            <distance value="100"/>
         </scenario>
         <scenario name="StaticCutIn_3" type="StaticCutIn">
            <trigger_point x="2379.6" y="4374.6" z="181.5" yaw="78.3"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_4" type="StaticCutIn">
            <trigger_point x="2410.6" y="4593.9" z="181.0" yaw="85.6"/>
            <distance value="110"/>
            <direction value="left"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="1984.5" y="1094.2" z="154.0" yaw="-1.2"/>
         </scenario>
         <scenario name="ParkedObstacle_1" type="ParkedObstacle">
            <trigger_point x="2215.1" y="1079.5" z="157.7" yaw="177.8"/>
            <distance value="55"/>
            <speed value="45"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="609.9" y="1137.2" z="147.2" yaw="-1.1"/>
            <distance value="55"/>
            <frequency value="75"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="13"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="941.8" y="1130.1" z="153.3" yaw="-1.4"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="1467.8" y="1113.3" z="156.9" yaw="357.8"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="1804.8" y="1100.1" z="152.0" yaw="-2.3"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="2162.1" y="952.0" z="157.0" yaw="-12.7"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="1258.4" y="1717.5" z="148.6" yaw="91.0"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="1994.9" y="3551.7" z="168.7" yaw="54.0"/>
         </scenario>
         <scenario name="ParkedObstacle_2" type="ParkedObstacle">
            <trigger_point x="2295.0" y="4101.4" z="178.4" yaw="68.7"/>
            <distance value="60"/>
            <speed value="60"/>
         </scenario>
         <scenario name="ConstructionObstacle_2" type="ConstructionObstacle">
            <trigger_point x="2392.6" y="5365.5" z="170.3" yaw="91.8"/>
            <distance value="90"/>
            <speed value="60"/>
         </scenario>
         <scenario name="HardBreakRoute_11" type="HardBreakRoute">
            <trigger_point x="2372.1" y="6008.3" z="166.6" yaw="91.8"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="2363.2" y="6225.5" z="165.7" yaw="99.0"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="378.7" y="6100.8" z="165.5" yaw="-172.6"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="1490.7" y="1112.4" z="156.5" yaw="357.8"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="1991.3" y="1090.8" z="154.1" yaw="178.8"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="2064.9" y="3665.7" z="170.3" yaw="56.8"/>
         </scenario>
         <scenario name="HardBreakRoute_10" type="HardBreakRoute">
            <trigger_point x="2411.5" y="4882.5" z="176.8" yaw="91.8"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="2399.2" y="5157.2" z="172.6" yaw="91.8"/>
         </scenario>
         <scenario name="HighwayCutIn_1" type="HighwayCutIn">
            <trigger_point x="340.4" y="6096.3" z="165.3" yaw="-174.0"/>
            <other_actor_location x="337.3" y="6062.3" z="165.2"/>
         </scenario>
         <scenario name="HardBreakRoute_12" type="HardBreakRoute">
            <trigger_point x="2302.1" y="6353.0" z="162.5" yaw="132.3"/>
         </scenario>
         <scenario name="Accident_4" type="Accident">
            <trigger_point x="613.3" y="6150.3" z="164.6" yaw="-163.6"/>
            <distance value="90"/>
         </scenario>
      </scenarios>
   </route>
   <route id="4" town="Town13">
      <!-- Interurban + highway -->
      <weathers>
      <!-- From a nearly clear day with a gentle breeze and light fog to a slightly cloudy day as the sun lowers -->
         <weather route_percentage="0"
            cloudiness="2.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="90.0" fog_density="2.0"/>
         <weather route_percentage="100"
            cloudiness="20.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="15.0" fog_density="2.0"/>
      </weathers>
      <waypoints>
         <position x="1835.7" y="5826.3" z="147.7"/>
         <position x="1806.8" y="6018.1" z="147.8"/>
         <position x="1556.8" y="6012.5" z="147.8"/>
         <position x="1523.0" y="6015.0" z="147.9"/>
         <position x="1116.1" y="6007.9" z="154.9"/>
         <position x="1088.6" y="5982.8" z="155.8"/>
         <position x="1111.6" y="4688.4" z="165.6"/>
         <position x="1110.1" y="4582.2" z="166.1"/>
         <position x="1064.4" y="4556.6" z="167.7"/>
         <position x="379.7" y="4864.5" z="163.8"/>
         <position x="62.3" y="4941.8" z="155.8"/>
         <position x="-371.9" y="5127.1" z="151.1"/>
         <position x="-785.5" y="4868.1" z="146.1"/>
         <position x="-951.0" y="4792.1" z="146.2"/>
         <position x="-990.0" y="4792.8" z="146.4"/>
         <position x="-1700.0" y="5411.8" z="153.9"/>
         <position x="-2096.3" y="5819.7" z="153.3"/>
         <position x="-2176.1" y="5901.0" z="153.4"/>
         <position x="-3024.5" y="4696.3" z="179.1"/>
         <position x="-2388.5" y="4626.9" z="174.5"/>
         <position x="-1715.7" y="5420.5" z="154.1"/>
         <position x="-1001.2" y="4764.1" z="146.4"/>
         <position x="-1417.71" y="4416.02" z="151.38"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="1833.9" y="5899.8" z="147.8" yaw="91.3"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3703.6" y="5213.6" z="165.4" yaw="270.8"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3699.1" y="4901.7" z="167.8" yaw="270.8"/>
            <flow_speed value="6"/>
            <source_dist_interval from="10" to="60"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3437.9" y="4764.6" z="173.0" yaw="-9.4"/>
            <direction value="left"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-3593.0" y="5895.3" z="168.8" yaw="-179.4"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="-2534.2" y="4615.2" z="176.5" yaw="-9.4"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-1783.0" y="5334.7" z="157.2" yaw="62.5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-3713.3" y="5875.2" z="170.5" yaw="270.8"/>
            <distance value="60"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="-3711.0" y="5720.5" z="168.8" yaw="270.8"/>
            <distance value="65"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="1807.17" y="6018.08" z="147.80" yaw="-178.72"/>
            <distance value="60"/>
            <speed value="45"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_5" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3186.8" y="4723.1" z="177.2" yaw="-9.4"/>
            <distance value="70"/>
            <frequency from="40" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_3" type="ParkedObstacleTwoWays">
            <trigger_point x="-3001.4" y="4692.5" z="179.2" yaw="-9.4"/>
            <distance value="65"/>
            <frequency from="50" to="100"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="-2346.1" y="4743.4" z="173.7" yaw="64.5"/>
            <distance value="70"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="-2148.5" y="4983.8" z="169.8" yaw="36.7"/>
            <distance value="80"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_6" type="InvadingTurn">
            <trigger_point x="-1644.1" y="5397.5" z="153.0" yaw="-17.8"/>
            <distance value="90"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_4" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1075.98" y="4728.33" z="146.95" yaw="205.57"/>
            <distance value="88"/>
            <frequency value="40"/>
            <bicycle_drive_distance value="95"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="AccidentTwoWays_5" type="AccidentTwoWays">
            <trigger_point x="-1210.0" y="5188.7" z="147.4" yaw="-47.5"/>
            <distance value="70"/>
            <frequency from="40" to="95"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="1426.2" y="6012.8" z="148.8" yaw="-178.7"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-3709.2" y="5594.9" z="167.5" yaw="270.8"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-3706.8" y="5429.4" z="166.1" yaw="270.8"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-3706.7" y="5422.2" z="166.1" yaw="270.8"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="-3655.9" y="4799.2" z="169.9" yaw="-7.6"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="-3660.8" y="4799.8" z="169.8" yaw="-7.5"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="-2790.9" y="4657.6" z="179.3" yaw="-9.4"/>
         </scenario>
         <scenario name="InterurbanAdvancedActorFlow_1" type="InterurbanAdvancedActorFlow">
            <trigger_point x="-1078.5" y="4972.1" z="146.8" yaw="-62.7"/>
            <start_actor_flow x="-1109.0" y="4716.4" z="147.2"/>
            <end_actor_flow x="-861.5" y="4835.0" z="146.1"/>
            <flow_speed value="16"/>
            <source_dist_interval from="17" to="65"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="1300.7" y="6010.9" z="150.6" yaw="180.6"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-3712.8" y="5841.1" z="170.2" yaw="270.8"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_4" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3705.2" y="5320.6" z="165.6" yaw="270.8"/>
            <distance value="80"/>
            <frequency from="20" to="70"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-3701.3" y="5054.4" z="166.1" yaw="270.8"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="1122.5" y="6008.0" z="154.7" yaw="-179.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-286.9" y="5116.4" z="152.2" yaw="141.2"/>
            <flow_speed value="12"/>
            <source_dist_interval from="10" to="65"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-2097.3" y="5831.1" z="153.2" yaw="94.2"/>
            <flow_speed value="15"/>
            <source_dist_interval from="13" to="80"/>
         </scenario>
         <scenario name="InterurbanActorFlow_1" type="InterurbanActorFlow">
            <trigger_point x="1111.5" y="4697.0" z="165.6" yaw="271.1"/>
            <start_actor_flow x="1110.6" y="4464.2" z="166.4"/>
            <end_actor_flow x="1107.9" y="4703.1" z="165.5"/>
            <flow_speed value="17"/>
            <source_dist_interval from="25" to="95"/>
         </scenario>
         <scenario name="InterurbanActorFlow_2" type="InterurbanActorFlow">
            <trigger_point x="-861.0" y="4831.3" z="146.1" yaw="205.8"/>
            <start_actor_flow x="-1085.2" y="4727.8" z="147.0"/>
            <end_actor_flow x="-852.2" y="4839.5" z="146.1"/>
            <flow_speed value="18"/>
            <source_dist_interval from="30" to="100"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="1089.7" y="5914.5" z="156.1" yaw="270.9"/>
            <distance value="80"/>
            <frequency from="35" to="95"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="1093.3" y="5692.6" z="157.1" yaw="270.9"/>
            <distance value="88"/>
            <frequency from="25" to="89"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="1100.9" y="5270.1" z="160.3" yaw="271.1"/>
            <distance value="70"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="13"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="786.1" y="4569.2" z="173.2" yaw="163.5"/>
            <distance value="120"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="206.8" y="4909.4" z="158.9" yaw="167.6"/>
            <distance value="70"/>
            <frequency from="28" to="100"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-66.0" y="4984.9" z="153.9" yaw="157.2"/>
            <distance value="70"/>
            <frequency from="25" to="110"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-375.1" y="5124.0" z="151.0" yaw="224.3"/>
            <distance value="75"/>
            <frequency value="77"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="-577.2" y="4971.9" z="147.6" yaw="209.7"/>
            <distance value="75"/>
            <frequency from="20" to="95"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_3" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1017.2" y="4845.6" z="146.5" yaw="117.3"/>
            <distance value="75"/>
            <frequency value="88"/>
            <bicycle_drive_distance value="89"/>
            <bicycle_speed value="9"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-1209.5" y="5182.9" z="147.3" yaw="132.0"/>
            <distance value="110"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="AccidentTwoWays_4" type="AccidentTwoWays">
            <trigger_point x="-1474.0" y="5339.0" z="150.2" yaw="162.2"/>
            <distance value="76"/>
            <frequency from="25" to="98"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-1897.5" y="5492.1" z="156.0" yaw="146.8"/>
            <distance value="100"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="-2143.2" y="5897.8" z="153.1" yaw="173.3"/>
            <distance value="75"/>
            <frequency from="10" to="110"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-2606.9" y="5901.9" z="157.6" yaw="-179.7"/>
            <distance value="80"/>
            <frequency from="35" to="100"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="-2941.7" y="5900.0" z="161.2" yaw="-179.7"/>
            <distance value="75"/>
            <frequency from="25" to="99"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="443.9" y="4833.6" z="165.8" yaw="150.3"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="-1308.74" y="4566.17" z="149.97" yaw="226.34"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-1154.5" y="4688.1" z="147.7" yaw="210.3"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="1088.7" y="5974.9" z="155.8" yaw="270.9"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="1050.2" y="4556.4" z="168.2" yaw="180.9"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="247.7" y="4900.4" z="160.0" yaw="167.6"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-1203.7" y="5176.4" z="147.3" yaw="131.0"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-1777.8" y="5436.9" z="155.0" yaw="162.2"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-2924.2" y="5900.1" z="161.0" yaw="-179.7"/>
         </scenario>
      </scenarios>
   </route>
   <route id="5" town="Town13">
      <!-- Rural + highway -->
      <weathers>
         <!-- From a slightly cloudy day with a gentle breeze and dense fog to a nearly clear day with the same breeze and significantly reduced fog -->
         <weather route_percentage="0"
            cloudiness="5.0" precipitation="0.0" precipitation_deposits="50.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="15.0" fog_density="25.0"/>
         <weather route_percentage="100"
            cloudiness="5.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="90.0" fog_density="2.0"/>
      </weathers>
      <waypoints>
         <position x="-1720.9" y="-781.7" z="153.8"/>
         <position x="-2473.2" y="-712.4" z="165.2"/>
         <position x="-2152.0" y="-561.8" z="162.7"/>
         <position x="-2638.4" y="-1009.2" z="162.1"/>
         <position x="-4174.7" y="-1707.4" z="170.8"/>
         <position x="-3804.0" y="-2803.3" z="163.9"/>
         <position x="-3686.7" y="-2850.8" z="161.8"/>
         <position x="-3555.0" y="-2854.8" z="160.3"/>
         <position x="-3432.0" y="-2858.3" z="159.1"/>
         <position x="-2426.2" y="-2737.1" z="154.5"/>
         <position x="-2005.3" y="-2531.2" z="154.2"/>
         <position x="-1764.1" y="-2361.2" z="155.3"/>
         <position x="-1652.1" y="-2269.9" z="155.9"/>
         <position x="-1489.9" y="-2137.6" z="156.9"/>
         <position x="-1387.5" y="-2047.7" z="157.7"/>
         <position x="-618.9" y="-1544.6" z="162.6"/>
         <position x="-464.5" y="-1405.8" z="162.0"/>
         <position x="-154.8" y="-1473.0" z="168.0"/>
         <position x="992.3" y="-2310.6" z="154.6"/>
         <position x="899.7" y="-2439.1" z="155.5"/>
         <position x="719.1" y="-2549.2" z="157.3"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-1942.8" y="-731.2" z="157.0" yaw="89.9"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-2052.5" y="-574.8" z="160.7" yaw="89.4"/>
            <flow_speed value="12"/>
            <source_dist_interval from="17" to="60"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="-2101.5" y="-511.1" z="162.4" yaw="179.9"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-2269.9" y="-511.4" z="165.5" yaw="-179.8"/>
            <flow_speed value="9"/>
            <source_dist_interval from="12" to="48"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-2315.9" y="-717.2" z="163.0" yaw="-90.2"/>
         </scenario>
         <scenario name="InterurbanAdvancedActorFlow_1" type="InterurbanAdvancedActorFlow">
            <trigger_point x="-2151.7" y="-894.2" z="158.2" yaw="270.1"/>
            <start_actor_flow x="-2294.9" y="-1003.0" z="158.6"/>
            <end_actor_flow x="-1974.5" y="-1000.4" z="154.9"/>
            <flow_speed value="18"/>
            <source_dist_interval from="25" to="75"/>
         </scenario>
         <scenario name="EnterActorFlow_1" type="EnterActorFlow">
            <trigger_point x="-3808.4" y="-2796.7" z="164.1" yaw="301.3"/>
            <start_actor_flow x="-3820.2" y="-2851.2" z="163.8"/>
            <end_actor_flow x="-3472.5" y="-2851.3" z="159.5"/>
            <flow_speed value="18"/>
            <source_dist_interval from="15" to="70"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_1" type="YieldToEmergencyVehicle">
            <trigger_point x="-3212.1" y="-2858.4" z="157.6" yaw="-0.0"/>
            <distance value="150"/>
         </scenario>
         <scenario name="HighwayCutIn_1" type="HighwayCutIn">
            <trigger_point x="-1834.1" y="-2417.0" z="154.9" yaw="37.4"/>
            <other_actor_location x="-1804.1" y="-2452.4" z="155.4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-2452.3" y="-509.4" z="168.1" yaw="0.2"/>
            <distance value="60"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="-2298.4" y="-508.8" z="165.9" yaw="0.2"/>
            <distance value="60"/>
            <blocker_model value="static.prop.haybalelb"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-2315.2" y="-523.1" z="166.0" yaw="-90.2"/>
            <distance value="60"/>
            <frequency from="19" to="60"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="-2341.6" y="-778.2" z="162.4" yaw="179.6"/>
            <distance value="130"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="-2524.8" y="-1008.3" z="161.1" yaw="180.5"/>
            <distance value="80"/>
            <frequency from="10" to="55"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-2908.1" y="-1011.3" z="163.6" yaw="180.5"/>
            <distance value="90"/>
            <frequency from="15" to="50"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-3127.4" y="-1013.1" z="163.8" yaw="180.5"/>
            <distance value="80"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="120"/>
            <bicycle_speed value="13"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-3571.4" y="-1016.6" z="163.3" yaw="180.5"/>
            <distance value="80"/>
            <frequency value="75"/>
            <bicycle_drive_distance value="120"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_3" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-4272.8" y="-1172.7" z="164.8" yaw="269.6"/>
            <distance value="100"/>
            <frequency value="85"/>
            <bicycle_drive_distance value="95"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-3996.9" y="-1899.4" z="170.5" yaw="313.2"/>
            <distance value="170"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="-2152.1" y="-528.0" z="163.2" yaw="270.1"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="-2152.0" y="-561.8" z="162.7" yaw="270.1"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-2366.6" y="-1007.0" z="159.4" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-3656.3" y="-1017.3" z="163.4" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="-3508.7" y="-2858.3" z="159.8" yaw="-0.0"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-3450.69" y="-2858.31" z="159.28" yaw="-0.02"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-1949.1" y="-2499.2" z="154.3" yaw="33.7"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="-1600.8" y="-2228.0" z="156.3" yaw="39.2"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4150.8" y="-1021.1" z="164.6" yaw="180.4"/>
            <flow_speed value="12"/>
            <source_dist_interval from="13" to="55"/>
         </scenario>
         <scenario name="HighwayExit_1" type="HighwayExit">
            <trigger_point x="-642.5" y="-1559.6" z="162.7" yaw="32.5"/>
            <start_actor_flow x="-645.1" y="-1557.3" z="162.7"/>
            <end_actor_flow x="-492.0" y="-1443.0" z="160.6"/>
            <flow_speed value="13"/>
            <source_dist_interval from="10" to="78"/>
         </scenario>
         <scenario name="MergerIntoSlowTraffic_1" type="MergerIntoSlowTraffic">
            <trigger_point x="-426.5" y="-1367.2" z="165.6" yaw="44.0"/>
            <start_actor_flow x="-363.5" y="-1348.9" z="168.0"/>
            <end_actor_flow x="-314.4" y="-1230.5" z="168.0"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="48"/>
         </scenario>
         <scenario name="MergerIntoSlowTrafficV2_1" type="MergerIntoSlowTrafficV2">
            <trigger_point x="-149.4" y="-1297.7" z="168.0" yaw="-74.8"/>
            <start_actor_flow x="-113.1" y="-1300.8" z="167.8"/>
            <end_actor_flow x="-175.4" y="-1430.1" z="168.0"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="35"/>
         </scenario>
         <scenario name="InterurbanAdvancedActorFlow_2" type="InterurbanAdvancedActorFlow">
            <trigger_point x="969.8" y="-2271.7" z="154.6" yaw="-59.6"/>
            <start_actor_flow x="901.6" y="-2433.8" z="155.5"/>
            <end_actor_flow x="1117.6" y="-2304.3" z="154.0"/>
            <flow_speed value="18"/>
            <source_dist_interval from="23" to="88"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_2" type="YieldToEmergencyVehicle">
            <trigger_point x="-1461.0" y="-2105.6" z="157.2" yaw="38.4"/>
            <distance value="140"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="-1168.1" y="-1894.6" z="160.0" yaw="32.5"/>
            <distance value="80"/>
            <speed value="60"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="-973.8" y="-1770.7" z="162.1" yaw="32.5"/>
            <distance value="88"/>
            <speed value="57"/>
         </scenario>
         <scenario name="ParkedObstacle_1" type="ParkedObstacle">
            <trigger_point x="-812.8" y="-1668.1" z="163.0" yaw="32.5"/>
            <distance value="70"/>
            <speed value="49"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="20.6" y="-1718.5" z="168.8" yaw="-46.6"/>
            <distance value="77"/>
            <frequency from="10" to="99"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="134.3" y="-1818.1" z="170.4" yaw="-35.8"/>
            <distance value="100"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_4" type="HazardAtSideLaneTwoWays">
            <trigger_point x="263.3" y="-1893.7" z="169.9" yaw="-25.0"/>
            <distance value="75"/>
            <frequency value="82"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="InvadingTurn_6" type="InvadingTurn">
            <trigger_point x="567.3" y="-1974.9" z="162.2" yaw="-13.2"/>
            <distance value="100"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="782.5" y="-2070.3" z="156.8" yaw="-34.6"/>
            <distance value="70"/>
            <frequency from="30" to="105"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="-146.6" y="-1485.5" z="168.0" yaw="-56.8"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="-982.9" y="-1776.5" z="162.0" yaw="32.5"/>
         </scenario>
         <scenario name="ControlLoss_14" type="ControlLoss">
            <trigger_point x="-157.0" y="-1469.6" z="168.0" yaw="-56.8"/>
         </scenario>
         <scenario name="ControlLoss_15" type="ControlLoss">
            <trigger_point x="-70.4" y="-1602.2" z="168.3" yaw="-56.8"/>
         </scenario>
         <scenario name="ControlLoss_16" type="ControlLoss">
            <trigger_point x="955.6" y="-2404.9" z="155.1" yaw="211.4"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="-2367.6" y="-2711.8" z="154.3" yaw="21.1"/>
            <distance value="100"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="-1896.1" y="-781.9" z="155.9" yaw="180.1"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-2004.5" y="-680.0" z="158.6" yaw="179.7"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-2474.1" y="-580.2" z="167.3" yaw="89.8"/>
            <flow_speed value="13"/>
            <source_dist_interval from="10" to="88"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="-1738.7" y="-781.7" z="154.0" yaw="180.1"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="-2179.8" y="-511.1" z="163.9" yaw="-179.8"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="-2443.4" y="-509.3" z="168.0" yaw="0.2"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="-2224.9" y="-508.5" z="164.7" yaw="0.2"/>
            <flow_speed value="12"/>
            <source_dist_interval from="17" to="66"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="-2151.9" y="-698.9" z="160.8" yaw="270.1"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-4272.3" y="-1416.2" z="166.9" yaw="274.3"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="-4271.1" y="-1430.2" z="167.1" yaw="275.6"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-4256.9" y="-1514.6" z="168.3" yaw="283.5"/>
            <distance value="95"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="-4189.8" y="-1682.6" z="170.6" yaw="300.1"/>
            <distance value="85"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-3883.3" y="-2066.7" z="168.7" yaw="295.1"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-3872.5" y="-2091.1" z="168.4" yaw="292.7"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="-3842.9" y="-2179.3" z="167.6" yaw="284.4"/>
            <distance value="95"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3822.8" y="-2348.9" z="166.6" yaw="270.3"/>
            <distance value="90"/>
            <frequency from="45" to="120"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-3821.9" y="-2514.3" z="165.9" yaw="270.3"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-3821.3" y="-2614.0" z="165.3" yaw="270.3"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="-2138.9" y="-2607.9" z="154.0" yaw="27.8"/>
         </scenario>
      </scenarios>
   </route>
   <route id="6" town="Town13">
      <!-- Rural + interurban -->
      <weathers>
      <!-- From dry and partly cloudy with a moderate wind to clear and sunny with a light breeze -->
         <weather route_percentage="0"
            cloudiness="15.0" precipitation="0.0" precipitation_deposits="40.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="90.0"
            fog_density="2.0"/>
         <weather route_percentage="100"
            cloudiness="5.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="15.0"
            fog_density="2.0"/>
      </weathers>
      <waypoints>
         <position x="-1589.0" y="-10.5" z="156.8"/>
         <position x="-1506.8" y="-143.8" z="155.0"/>
         <position x="-1616.9" y="127.4" z="157.7"/>
         <position x="-1270.1" y="205.1" z="153.2"/>
         <position x="-1188.3" y="383.9" z="152.4"/>
         <position x="-1092.5" y="461.5" z="151.5"/>
         <position x="-1166.4" y="545.9" z="151.9"/>
         <position x="-1340.9" y="517.1" z="153.7"/>
         <position x="-1402.1" y="543.2" z="154.4"/>
         <position x="-1438.9" y="630.5" z="154.6"/>
         <position x="-1422.7" y="668.8" z="154.3"/>
         <position x="-1226.6" y="712.0" z="151.8"/>
         <position x="-1112.6" y="765.5" z="150.7"/>
         <position x="-1088.9" y="554.4" z="151.3"/>
         <position x="-784.6" y="644.3" z="150.1"/>
         <position x="-855.9" y="1189.5" z="148.4"/>
         <position x="-1613.1" y="1946.2" z="150.1"/>
         <position x="-814.8" y="2146.2" z="146.5"/>
         <position x="-530.3" y="1769.0" z="147.6"/>
         <position x="-687.0" y="1634.4" z="147.6"/>
         <position x="-1147.4" y="2084.6" z="147.1"/>
         <position x="-1033.3" y="2275.8" z="146.6"/>
         <position x="-1427.7" y="2628.9" z="148.0"/>
         <position x="-1503.9" y="2733.8" z="148.1"/>
         <position x="-1625.2" y="2834.0" z="148.6"/>
         <position x="-2284.2" y="2962.1" z="153.6"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1493.8" y="-10.5" z="155.4" yaw="0.0"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-1331.0" y="127.7" z="153.7" yaw="0.0"/>
            <flow_speed value="14"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1724.9" y="1942.3" z="151.4" yaw="185.3"/>
            <flow_speed value="11"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-1453.1" y="-111.6" z="154.5" yaw="269.2"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="-1526.4" y="127.5" z="156.3" yaw="0.0"/>
            <direction value="left"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="-457.7" y="863.3" z="149.8" yaw="32.2"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="-874.2" y="1207.8" z="148.3" yaw="132.5"/>
         </scenario>
         <scenario name="VehicleTurningRoute_4" type="VehicleTurningRoute">
            <trigger_point x="-1031.2" y="2272.4" z="146.6" yaw="-107.3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-1580.6" y="-10.5" z="156.6" yaw="0.0"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="-1606.2" y="127.4" z="157.6" yaw="0.0"/>
            <distance value="113"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="-1423.7" y="127.6" z="154.8" yaw="0.0"/>
            <distance value="105"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="-1523.0" y="-143.7" z="155.2" yaw="179.8"/>
            <distance value="120"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="-1274.1" y="141.5" z="153.2" yaw="84.0"/>
            <distance value="70"/>
            <frequency from="25" to="100"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-1226.5" y="695.3" z="151.8" yaw="90.1"/>
            <distance value="70"/>
            <frequency from="25" to="75"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="-1090.2" y="554.3" z="151.3" yaw="5.7"/>
            <distance value="75"/>
            <frequency from="20" to="88"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="-836.0" y="620.0" z="150.2" yaw="23.3"/>
            <distance value="90"/>
            <frequency from="19" to="90"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-420.6" y="937.2" z="149.7" yaw="125.1"/>
            <distance value="80"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="AccidentTwoWays_4" type="AccidentTwoWays">
            <trigger_point x="-743.0" y="1123.8" z="148.8" yaw="162.2"/>
            <distance value="70"/>
            <frequency from="15" to="85"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-950.6" y="1241.2" z="148.3" yaw="192.8"/>
            <distance value="70"/>
            <frequency value="75"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="11"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-1194.8" y="1185.8" z="149.3" yaw="192.8"/>
            <distance value="180"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="-1490.4" y="1607.5" z="149.8" yaw="62.4"/>
            <distance value="180"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="-1799.4" y="2101.0" z="151.8" yaw="95.2"/>
            <distance value="150"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="AccidentTwoWays_5" type="AccidentTwoWays">
            <trigger_point x="-1702.4" y="2450.0" z="150.3" yaw="53.0"/>
            <distance value="75"/>
            <frequency from="25" to="88"/>
         </scenario>
         <scenario name="InvadingTurn_6" type="InvadingTurn">
            <trigger_point x="-1420.7" y="2633.7" z="147.9" yaw="13.2"/>
            <distance value="170"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_3" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1110.6" y="2569.7" z="146.4" yaw="-42.9"/>
            <distance value="65"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="9"/>
         </scenario>
         <scenario name="InvadingTurn_7" type="InvadingTurn">
            <trigger_point x="-1034.2" y="2206.1" z="146.6" yaw="-33.2"/>
            <distance value="180"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_8" type="InvadingTurn">
            <trigger_point x="-667.0" y="2269.7" z="146.4" yaw="65.9"/>
            <distance value="230"/>
            <offset value="0.2"/>
         </scenario>
         <scenario name="InvadingTurn_9" type="InvadingTurn">
            <trigger_point x="-288.5" y="2316.9" z="146.6" yaw="-86.2"/>
            <distance value="100"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="-1452.3" y="-31.1" z="154.8" yaw="270.3"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="-1649.4" y="0.6" z="157.8" yaw="95.0"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-1673.2" y="1947.1" z="150.8" yaw="185.3"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="-1515.3" y="-143.8" z="155.1" yaw="179.8"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-787.8" y="642.6" z="150.1" yaw="26.9"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-647.1" y="1100.9" z="149.0" yaw="163.2"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-1235.5" y="1176.6" z="149.6" yaw="192.8"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-1534.3" y="1290.6" z="151.9" yaw="118.6"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-1444.0" y="1761.3" z="148.9" yaw="94.1"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="-1797.3" y="2078.5" z="151.9" yaw="95.2"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-1240.2" y="2639.0" z="146.8" yaw="-13.4"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="-516.7" y="1978.2" z="147.1" yaw="-110.6"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-1138.4" y="390.7" z="152.0" yaw="7.0"/>
            <flow_speed value="9"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="-1293.2" y="678.9" z="152.6" yaw="0.5"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-1227.1" y="908.0" z="150.8" yaw="90.1"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_6" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1405.6" y="2634.0" z="147.8" yaw="191.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="18" to="45"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="-1212.72" y="380.14" z="152.61" yaw="9.27"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="-1073.6" y="412.6" z="151.5" yaw="105.7"/>
            <distance value="98"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_6" type="DynamicObjectCrossing">
            <trigger_point x="-1395.3" y="529.0" z="154.4" yaw="115.6"/>
            <distance value="50"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="-1137.7" y="547.4" z="151.6" yaw="182.5"/>
            <distance value="70"/>
            <frequency from="45" to="90"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-1206.9" y="953.7" z="150.4" yaw="0.8"/>
            <distance value="140"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-524.0" y="1698.1" z="147.8" yaw="201.3"/>
            <distance value="80"/>
            <frequency from="45" to="100"/>
         </scenario>
         <scenario name="InvadingTurn_10" type="InvadingTurn">
            <trigger_point x="-1002.5" y="1522.4" z="147.6" yaw="185.3"/>
            <distance value="130"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_4" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1280.6" y="1705.9" z="148.1" yaw="107.9"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="InvadingTurn_11" type="InvadingTurn">
            <trigger_point x="-1046.3" y="2241.0" z="146.6" yaw="65.8"/>
            <distance value="150"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="-1072.5" y="2521.3" z="146.3" yaw="124.8"/>
            <distance value="75"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="InvadingTurn_12" type="InvadingTurn">
            <trigger_point x="-1599.3" y="2822.7" z="148.4" yaw="152.9"/>
            <distance value="160"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_5" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1812.5" y="2776.5" z="150.1" yaw="238.4"/>
            <distance value="75"/>
            <frequency value="85"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="8"/>
         </scenario>
         <scenario name="InvadingTurn_13" type="InvadingTurn">
            <trigger_point x="-1932.3" y="2420.1" z="152.1" yaw="233.9"/>
            <distance value="160"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="InvadingTurn_14" type="InvadingTurn">
            <trigger_point x="-2180.9" y="2440.0" z="153.6" yaw="111.7"/>
            <distance value="130"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-1146.2" y="2086.4" z="147.1" yaw="55.8"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-1469.0" y="2643.0" z="148.2" yaw="106.4"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="-1284.2" y="366.7" z="153.3" yaw="12.0"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="-1423.4" y="668.7" z="154.3" yaw="14.1"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="-1112.2" y="665.8" z="151.1" yaw="-89.8"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="-867.1" y="1564.1" z="147.5" yaw="201.3"/>
         </scenario>
         <scenario name="ControlLoss_14" type="ControlLoss">
            <trigger_point x="-1151.9" y="2078.0" z="147.1" yaw="55.8"/>
         </scenario>
         <scenario name="ControlLoss_15" type="ControlLoss">
            <trigger_point x="-1468.0" y="2639.6" z="148.2" yaw="106.4"/>
         </scenario>
         <scenario name="ControlLoss_16" type="ControlLoss">
            <trigger_point x="-2126.0" y="2868.7" z="152.1" yaw="125.1"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1298.4" y="304.6" z="153.5" yaw="108.1"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="85"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_2" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1658.7" y="75.2" z="158.3" yaw="97.5"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="78"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1429.0" y="602.6" z="154.6" yaw="111.3"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="69"/>
         </scenario>
      </scenarios>
   </route>
   <route id="7" town="Town13">
      <!-- Rural + interurban -->
      <weathers>
         <!-- From partly cloudy with strong winds and light rain to cloudy and dry with stronger winds -->
         <weather route_percentage="0"
            cloudiness="20.0" precipitation="20.0" precipitation_deposits="50.0" wetness="0.0"
            wind_intensity="30.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="3.0"/>
         <weather route_percentage="100"
            cloudiness="60.0" precipitation="0.0" precipitation_deposits="60.0" wetness="0.0"
            wind_intensity="60.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="3.0"/>
      </weathers>
      <waypoints>
         <position x="-4228.0" y="4329.5" z="174.5"/>
         <position x="-4341.9" y="4042.7" z="178.7"/>
         <position x="-4165.2" y="3829.3" z="184.7"/>
         <position x="-3919.1" y="3821.4" z="185.8"/>
         <position x="-3857.0" y="4047.4" z="183.4"/>
         <position x="-4101.4" y="4014.4" z="182.7"/>
         <position x="-3941.4" y="3932.9" z="184.9"/>
         <position x="-3833.8" y="3933.5" z="184.7"/>
         <position x="-3694.8" y="4008.4" z="183.2"/>
         <position x="-3653.2" y="4158.1" z="181.6"/>
         <position x="-3257.0" y="4041.7" z="180.2"/>
         <position x="-3291.7" y="3932.7" z="179.5"/>
         <position x="-3386.6" y="3620.7" z="176.5"/>
         <position x="-3343.6" y="3789.7" z="178.4"/>
         <position x="-3469.7" y="3788.5" z="180.1"/>
         <position x="-3541.8" y="3717.1" z="180.7"/>
         <position x="-3644.0" y="3614.7" z="181.8"/>
         <position x="-3700.3" y="3510.0" z="181.9"/>
         <position x="-3761.7" y="3105.6" z="177.5"/>
         <position x="-3787.5" y="2169.0" z="158.5"/>
         <position x="-4281.0" y="269.3" z="172.0"/>
         <position x="-4221.4" y="113.8" z="172.7"/>
         <position x="-4133.9" y="16.2" z="173.2"/>
         <position x="-4061.1" y="-220.4" z="171.8"/>
         <position x="-4776.2" y="-351.3" z="164.2"/>
         <position x="-4772.5" y="-41.2" z="163.8"/>
         <position x="-4605.6" y="-77.9" z="166.2"/>
         <position x="-4548.0" y="-213.3" z="166.9"/>
         <position x="-4456.86" y="-157.66" z="168.41"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4262.5" y="4152.0" z="177.8" yaw="180.3"/>
            <flow_speed value="11"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="-4365.1" y="3972.5" z="179.7" yaw="255.1"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4173.1" y="3931.7" z="183.3" yaw="0.3"/>
            <flow_speed value="9"/>
            <source_dist_interval from="13" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4176.4" y="3675.0" z="185.9" yaw="275.3"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4001.8" y="3613.5" z="186.6" yaw="0.6"/>
            <flow_speed value="12"/>
            <source_dist_interval from="20" to="60"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3874.1" y="3884.5" z="185.3" yaw="60.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3858.3" y="4102.4" z="182.6" yaw="91.4"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="-4039.3" y="4153.2" z="180.6" yaw="180.3"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4105.3" y="3998.8" z="182.9" yaw="255.2"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="53"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="-3764.7" y="4333.2" z="178.6" yaw="0.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="-3602.1" y="4158.4" z="181.5" yaw="0.3"/>
            <direction value="left"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-3321.4" y="4159.8" z="180.9" yaw="0.3"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_6" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3256.2" y="3983.2" z="179.8" yaw="270.8"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_6" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3376.1" y="3932.5" z="180.2" yaw="180.2"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="48"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3418.7" y="3859.7" z="180.1" yaw="270.7"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="-3416.6" y="3685.3" z="178.0" yaw="270.6"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="-3255.4" y="3750.4" z="176.8" yaw="91.4"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_7" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3492.2" y="3788.3" z="180.5" yaw="180.5"/>
            <flow_speed value="7"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-3541.3" y="3673.7" z="180.2" yaw="270.7"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="-3639.0" y="3614.8" z="181.7" yaw="180.7"/>
            <flow_speed value="14"/>
            <source_dist_interval from="20" to="66"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_8" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4133.9" y="25.1" z="173.2" yaw="270.0"/>
            <flow_speed value="8"/>
            <source_dist_interval from="14" to="48"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_9" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3995.7" y="-134.7" z="172.7" yaw="271.1"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_3" type="OppositeVehicleTakingPriority">
            <trigger_point x="-4178.6" y="-255.3" z="171.0" yaw="179.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="-4736.9" y="26.9" z="164.2" yaw="76.7"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_11" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4626.8" y="113.1" z="165.8" yaw="0.1"/>
            <flow_speed value="10"/>
            <source_dist_interval from="18" to="53"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_10" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4619.9" y="-112.9" z="166.0" yaw="245.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="47"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_11" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4564.7" y="-208.3" z="166.7" yaw="-17.0"/>
            <flow_speed value="7"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-4221.44" y="4329.60" z="174.63" yaw="0.45"/>
            <distance value="58"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="-3855.0" y="3968.4" z="184.4" yaw="91.4"/>
            <distance value="75"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="-4088.9" y="4111.0" z="181.0" yaw="268.2"/>
            <distance value="75"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="-3834.5" y="3933.5" z="184.7" yaw="0.3"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_6" type="DynamicObjectCrossing">
            <trigger_point x="-3860.2" y="4179.4" z="181.2" yaw="91.4"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_7" type="DynamicObjectCrossing">
            <trigger_point x="-3671.4" y="4158.0" z="181.7" yaw="0.3"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_8" type="DynamicObjectCrossing">
            <trigger_point x="-3531.31" y="4158.72" z="181.28" yaw="0.30"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_9" type="DynamicObjectCrossing">
            <trigger_point x="-3419.48" y="3916.49" z="180.52" yaw="270.74"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_10" type="DynamicObjectCrossing">
            <trigger_point x="-3272.6" y="3790.4" z="177.6" yaw="180.5"/>
            <distance value="44"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_11" type="DynamicObjectCrossing">
            <trigger_point x="-3445.36" y="3788.75" z="179.75" yaw="180.54"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_12" type="DynamicObjectCrossing">
            <trigger_point x="-4133.9" y="86.5" z="173.6" yaw="270.0"/>
            <distance value="68"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_13" type="DynamicObjectCrossing">
            <trigger_point x="-3996.7" y="-77.5" z="173.2" yaw="271.1"/>
            <distance value="64"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_15" type="DynamicObjectCrossing">
            <trigger_point x="-4573.3" y="84.8" z="166.7" yaw="267.8"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-4114.76" y="4152.79" z="179.83" yaw="180.33"/>
            <distance value="66"/>
            <frequency from="38" to="58"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="-4312.7" y="4134.1" z="177.4" yaw="256.9"/>
            <distance value="115"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-4139.2" y="3906.6" z="184.0" yaw="246.5"/>
            <distance value="125"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="-4142.48" y="3612.10" z="186.44" yaw="0.59"/>
            <distance value="75"/>
            <frequency from="40" to="83"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-3934.6" y="3758.8" z="186.2" yaw="88.6"/>
            <distance value="110"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="-3891.96" y="4154.07" z="181.6" yaw="180.33"/>
            <distance value="70"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="-4096.69" y="3932.08" z="184.09" yaw="0.31"/>
            <distance value="75"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="-3694.4" y="3963.6" z="183.5" yaw="90.5"/>
            <distance value="70"/>
            <frequency from="40" to="100"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="-3258.18" y="4134.73" z="180.77" yaw="270.76"/>
            <distance value="70"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="-3373.4" y="3620.9" z="176.3" yaw="0.7"/>
            <distance value="120"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3705.9" y="3451.0" z="181.4" yaw="264.0"/>
            <distance value="75"/>
            <frequency from="45" to="110"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-3763.5" y="3096.7" z="177.4" yaw="258.6"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-3786.1" y="2178.2" z="158.6" yaw="261.6"/>
            <distance value="70"/>
            <frequency value="78"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3890.8" y="1903.2" z="158.5" yaw="244.7"/>
            <distance value="75"/>
            <frequency from="50" to="100"/>
         </scenario>
         <scenario name="AccidentTwoWays_4" type="AccidentTwoWays">
            <trigger_point x="-4270.5" y="1100.0" z="166.8" yaw="246.3"/>
            <distance value="75"/>
            <frequency from="40" to="90"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_3" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-4339.1" y="707.3" z="169.1" yaw="273.9"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="11"/>
         </scenario>
         <scenario name="AccidentTwoWays_5" type="AccidentTwoWays">
            <trigger_point x="-4293.7" y="400.6" z="171.6" yaw="276.8"/>
            <distance value="70"/>
            <frequency from="45" to="99"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="-4022.0" y="-202.4" z="172.0" yaw="192.3"/>
            <distance value="120"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_6" type="InvadingTurn">
            <trigger_point x="-4322.3" y="-253.8" z="169.7" yaw="177.1"/>
            <distance value="100"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="AccidentTwoWays_6" type="AccidentTwoWays">
            <trigger_point x="-4861.1" y="-323.9" z="163.6" yaw="79.5"/>
            <distance value="70"/>
            <frequency from="35" to="88"/>
         </scenario>
         <scenario name="InvadingTurn_8" type="InvadingTurn">
            <trigger_point x="-4800.0" y="-75.4" z="163.6" yaw="57.3"/>
            <distance value="115"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="-3933.2" y="3635.7" z="186.3" yaw="90.7"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="-3733.9" y="4155.0" z="181.8" yaw="180.3"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-3834.1" y="4332.7" z="178.2" yaw="0.5"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-3396.49" y="4159.42" z="180.97" yaw="0.30"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-3275.1" y="3932.8" z="179.4" yaw="180.2"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="-3417.5" y="3764.7" z="179.1" yaw="270.7"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="-3694.8" y="3586.9" z="182.6" yaw="266.6"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="-3828.4" y="2522.2" z="165.1" yaw="278.7"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="-4037.9" y="1592.7" z="161.9" yaw="244.7"/>
         </scenario>
         <scenario name="HardBreakRoute_10" type="HardBreakRoute">
            <trigger_point x="-4243.6" y="113.7" z="172.4" yaw="0.1"/>
         </scenario>
         <scenario name="HardBreakRoute_11" type="HardBreakRoute">
            <trigger_point x="-4112.2" y="-59.4" z="172.9" yaw="0.1"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="-4169.4" y="4330.0" z="175.3" yaw="0.5"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="-4340.3" y="3930.8" z="180.9" yaw="0.3"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="-3933.4" y="3651.4" z="186.3" yaw="90.7"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="-4115.5" y="3931.9" z="183.9" yaw="0.3"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-3741.3" y="4154.9" z="181.8" yaw="180.3"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-3825.6" y="4332.7" z="178.3" yaw="0.5"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-3693.5" y="4280.7" z="179.8" yaw="270.5"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-3508.3" y="4158.8" z="181.2" yaw="0.3"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-3284.8" y="3932.7" z="179.5" yaw="180.2"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-3440.6" y="3788.8" z="179.7" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="-3565.7" y="3615.7" z="180.1" yaw="180.7"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="-3830.6" y="2536.9" z="165.5" yaw="277.7"/>
         </scenario>
         <scenario name="ControlLoss_14" type="ControlLoss">
            <trigger_point x="-3781.5" y="2291.2" z="159.9" yaw="273.8"/>
         </scenario>
         <scenario name="ControlLoss_15" type="ControlLoss">
            <trigger_point x="-4033.7" y="1601.5" z="161.7" yaw="244.7"/>
         </scenario>
         <scenario name="ControlLoss_16" type="ControlLoss">
            <trigger_point x="-4261.1" y="1120.7" z="166.8" yaw="244.8"/>
         </scenario>
         <scenario name="ControlLoss_17" type="ControlLoss">
            <trigger_point x="-4339.0" y="704.7" z="169.2" yaw="274.1"/>
         </scenario>
         <scenario name="ControlLoss_18" type="ControlLoss">
            <trigger_point x="-4245.7" y="113.7" z="172.3" yaw="0.1"/>
         </scenario>
         <scenario name="ControlLoss_19" type="ControlLoss">
            <trigger_point x="-4104.9" y="-59.4" z="172.9" yaw="0.1"/>
         </scenario>
         <scenario name="ControlLoss_20" type="ControlLoss">
            <trigger_point x="-4016.7" y="-201.4" z="172.0" yaw="189.2"/>
         </scenario>
         <scenario name="ControlLoss_21" type="ControlLoss">
            <trigger_point x="-4724.1" y="-350.9" z="164.6" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_22" type="ControlLoss">
            <trigger_point x="-4706.34" y="112.92" z="164.45" yaw="0.11"/>
         </scenario>
         <scenario name="ControlLoss_23" type="ControlLoss">
            <trigger_point x="-4637.4" y="-185.2" z="165.7" yaw="-17.7"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_9" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4399.3" y="-246.8" z="168.8" yaw="172.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="60"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_14" type="DynamicObjectCrossing">
            <trigger_point x="-4475.9" y="-254.0" z="167.8" yaw="252.1"/>
            <distance value="75"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="InvadingTurn_7" type="InvadingTurn">
            <trigger_point x="-4545.7" y="-397.7" z="166.3" yaw="158.5"/>
            <distance value="100"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4145.4" y="4330.2" z="175.6" yaw="0.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="12" to="73"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_2" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4089.9" y="4208.2" z="178.9" yaw="270.9"/>
            <flow_speed value="9"/>
            <source_dist_interval from="17" to="62"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3803.5" y="4154.6" z="181.8" yaw="180.3"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3862.3" y="4261.6" z="179.6" yaw="91.4"/>
            <flow_speed value="8"/>
            <source_dist_interval from="12" to="62"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_7" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4200.5" y="113.8" z="172.9" yaw="0.1"/>
            <flow_speed value="12"/>
            <source_dist_interval from="15" to="78"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_8" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4082.3" y="-59.3" z="173.0" yaw="0.1"/>
            <flow_speed value="13"/>
            <source_dist_interval from="16" to="80"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_10" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4775.9" y="-351.3" z="164.2" yaw="180.5"/>
            <flow_speed value="9"/>
            <source_dist_interval from="12" to="79"/>
         </scenario>
      </scenarios>
   </route>
   <route id="8" town="Town13">
      <!-- Rural + Highway -->
      <weathers>
         <!-- From clear and sunny with a light breeze to partly cloudy -->
         <weather route_percentage="0"
            cloudiness="5.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="90.0" fog_density="2.0"/>
         <weather route_percentage="100"
            cloudiness="75.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="15.0" fog_density="2.0"/>
      </weathers>
      <waypoints>
         <position x="-2077.8" y="5729.8" z="154.2"/>
         <position x="-2161.9" y="5899.8" z="153.3"/>
         <position x="-2180.6" y="5904.9" z="153.5"/>
         <position x="-2618.9" y="5931.2" z="157.7"/>
         <position x="-2861.1" y="6006.9" z="161.2"/>
         <position x="-2924.79" y="6006.16" z="162.01"/>
         <position x="-3098.68" y="6004.32" z="164.00"/>
         <position x="-3316.7" y="5957.7" z="165.8"/>
         <position x="-3564.8" y="5899.1" z="168.3"/>
         <position x="-3682.4" y="5894.4" z="170.2"/>
         <position x="-3713.1" y="5861.8" z="170.4"/>
         <position x="-3758.9" y="5740.1" z="169.8"/>
         <position x="-3867.2" y="5621.0" z="170.0"/>
         <position x="-4163.6" y="5441.6" z="170.9"/>
         <position x="-4596.9" y="5882.1" z="182.3"/>
         <position x="-5401.9" y="5828.6" z="189.5"/>
         <position x="-5555.3" y="5719.8" z="191.4"/>
         <position x="-5603.3" y="5667.1" z="191.8"/>
         <position x="-5736.3" y="5388.5" z="192.4"/>
         <position x="-5753.6" y="5264.0" z="191.9"/>
         <position x="-5756.08" y="5167.66" z="191.20"/>
         <position x="-5750.3" y="5037.4" z="190.2"/>
         <position x="-5478.4" y="4903.4" z="183.3"/>
         <position x="-4538.8" y="4891.4" z="167.3"/>
         <position x="-4412.1" y="5198.7" z="169.1"/>
         <position x="-4106.8" y="5326.5" z="168.6"/>
         <position x="-4095.3" y="5153.0" z="166.6"/>
         <position x="-4314.4" y="4954.7" z="166.5"/>
         <position x="-4373.4" y="4836.6" z="166.6"/>
         <position x="-5512.7" y="4899.9" z="183.9"/>
         <position x="-5742.6" y="4727.7" z="186.9"/>
         <position x="-5742.6" y="4550.2" z="184.3"/>
         <position x="-5744.1" y="4449.6" z="182.5"/>
         <position x="-5781.5" y="2978.0" z="173.1"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-2098.1" y="5844.7" z="153.1" yaw="92.5"/>
            <flow_speed value="15"/>
            <source_dist_interval from="18" to="66"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="-3652.1" y="5894.7" z="169.7" yaw="-179.4"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="-3707.9" y="5508.1" z="166.7" yaw="270.8"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3760.2" y="5444.8" z="166.7" yaw="180.5"/>
            <flow_speed value="11"/>
            <source_dist_interval from="18" to="58"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3806.1" y="5394.4" z="166.7" yaw="-90.2"/>
            <flow_speed value="11"/>
            <source_dist_interval from="13" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3763.3" y="5339.8" z="166.0" yaw="359.9"/>
            <flow_speed value="15"/>
            <source_dist_interval from="20" to="66"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="-3714.1" y="5692.4" z="168.5" yaw="90.8"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3849.6" y="5739.4" z="171.3" yaw="180.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_6" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3906.8" y="5669.8" z="171.3" yaw="269.8"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3805.7" y="5498.5" z="167.7" yaw="-90.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="16" to="54"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3851.5" y="5444.0" z="167.7" yaw="180.5"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3947.6" y="5443.1" z="168.7" yaw="180.5"/>
            <direction value="left"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_3" type="OppositeVehicleTakingPriority">
            <trigger_point x="-4039.4" y="5442.2" z="169.6" yaw="180.6"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_7" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4292.3" y="5546.7" z="173.9" yaw="106.0"/>
            <flow_speed value="7"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="-4357.6" y="5611.4" z="175.6" yaw="180.8"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="-4409.6" y="5679.1" z="177.3" yaw="90.6"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-4491.7" y="5734.0" z="179.0" yaw="180.5"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_8" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4555.2" y="5813.8" z="180.8" yaw="91.1"/>
            <flow_speed value="13"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="-4478.9" y="5079.6" z="168.2" yaw="0.5"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="-4341.1" y="5344.8" z="170.7" yaw="54.8"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="-4195.2" y="5340.3" z="169.4" yaw="359.9"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4095.3" y="5155.6" z="166.6" yaw="270.6"/>
            <flow_speed value="9"/>
            <source_dist_interval from="13" to="48"/>
         </scenario>
         <scenario name="BlockedIntersection_5" type="BlockedIntersection">
            <trigger_point x="-4228.4" y="5078.9" z="166.7" yaw="180.5"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4314.1" y="4943.5" z="166.5" yaw="271.6"/>
            <flow_speed value="15"/>
            <source_dist_interval from="18" to="65"/>
         </scenario>
         <scenario name="BlockedIntersection_6" type="BlockedIntersection">
            <trigger_point x="-4531.2" y="4614.3" z="167.5" yaw="271.0"/>
         </scenario>
         <scenario name="BlockedIntersection_7" type="BlockedIntersection">
            <trigger_point x="-4386.5" y="4558.8" z="168.7" yaw="0.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_9" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4310.3" y="4643.5" z="168.1" yaw="91.2"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="48"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-4464.4" y="4722.9" z="167.0" yaw="180.2"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_10" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4537.0" y="4790.9" z="167.0" yaw="91.0"/>
            <flow_speed value="16"/>
            <source_dist_interval from="20" to="65"/>
         </scenario>
         <scenario name="MergerIntoSlowTraffic_1" type="MergerIntoSlowTraffic">
            <trigger_point x="-3353.9" y="5936.9" z="166.0" yaw="-153.8"/>
            <start_actor_flow x="-3347.1" y="5901.2" z="165.5"/>
            <end_actor_flow x="-3603.2" y="5898.7" z="168.9"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="45"/>
         </scenario>
         <scenario name="EnterActorFlow_1" type="EnterActorFlow">
            <trigger_point x="-5362.0" y="5843.5" z="189.1" yaw="-162.2"/>
            <start_actor_flow x="-5375.3" y="5866.8" z="189.5"/>
            <end_actor_flow x="-5590.5" y="5678.0" z="191.7"/>
            <flow_speed value="17"/>
            <source_dist_interval from="15" to="65"/>
         </scenario>
         <scenario name="EnterActorFlow_2" type="EnterActorFlow">
            <trigger_point x="-5716.6" y="4856.6" z="187.6" yaw="240.2"/>
            <start_actor_flow x="-5749.4" y="4860.4" z="188.5"/>
            <end_actor_flow x="-5741.6" y="4502.3" z="183.4"/>
            <flow_speed value="18"/>
            <source_dist_interval from="15" to="70"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_1" type="YieldToEmergencyVehicle">
            <trigger_point x="-5745.3" y="4512.1" z="183.6" yaw="-88.9"/>
            <distance value="130"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-3806.0" y="5435.9" z="167.1" yaw="-92.6"/>
            <distance value="50"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="-3736.5" y="5740.3" z="169.4" yaw="180.5"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="-3906.61" y="5722.80" z="171.99" yaw="269.81"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="-4096.0" y="5215.1" z="167.2" yaw="270.6"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="-4308.9" y="4572.4" z="169.1" yaw="91.2"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3710.9" y="5713.8" z="168.7" yaw="270.8"/>
            <distance value="75"/>
            <frequency from="40" to="90"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="-3710.9" y="5474.6" z="166.4" yaw="90.8"/>
            <distance value="75"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="-3873.1" y="5620.9" z="170.1" yaw="0.8"/>
            <distance value="110"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="HazardAtSideLane_1" type="HazardAtSideLane">
            <trigger_point x="-4592.2" y="5882.1" z="182.2" yaw="-179.2"/>
            <distance value="75"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-5581.2" y="4903.5" z="185.3" yaw="-0.1"/>
            <distance value="75"/>
            <frequency value="75"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="11"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-5174.1" y="4895.3" z="177.1" yaw="-3.2"/>
            <distance value="80"/>
            <frequency from="35" to="99"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="-4875.5" y="4873.1" z="171.0" yaw="-4.5"/>
            <distance value="75"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-4538.5" y="4879.2" z="167.2" yaw="91.2"/>
            <distance value="75"/>
            <frequency from="35" to="85"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-4427.0" y="5100.8" z="168.1" yaw="87.9"/>
            <distance value="120"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-4287.5" y="5358.1" z="170.5" yaw="335.7"/>
            <distance value="110"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="-4118.7" y="5079.9" z="166.3" yaw="180.5"/>
            <distance value="70"/>
            <frequency from="35" to="85"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="-4317.3" y="5057.7" z="167.0" yaw="271.6"/>
            <distance value="75"/>
            <frequency from="25" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_3" type="ParkedObstacleTwoWays">
            <trigger_point x="-4532.9" y="4710.1" z="167.0" yaw="271.0"/>
            <distance value="75"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_4" type="ParkedObstacleTwoWays">
            <trigger_point x="-4333.7" y="4723.4" z="167.2" yaw="180.2"/>
            <distance value="75"/>
            <frequency from="35" to="95"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-4881.8" y="4870.1" z="171.1" yaw="175.5"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="15"/>
         </scenario>
         <scenario name="StaticCutIn_3" type="StaticCutIn">
            <trigger_point x="-5733.9" y="3310.5" z="170.4" yaw="-94.4"/>
            <distance value="120"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="-2188.3" y="5905.4" z="153.5" yaw="176.2"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="-3713.0" y="5854.8" z="170.3" yaw="270.8"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-3709.2" y="5355.0" z="165.7" yaw="90.8"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-5117.1" y="5871.7" z="186.8" yaw="-177.3"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-4534.9" y="4829.1" z="167.0" yaw="271.0"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="-4506.2" y="4558.8" z="168.0" yaw="0.0"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="-5745.6" y="4704.9" z="186.6" yaw="-88.9"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="-2183.9" y="5905.1" z="153.5" yaw="175.9"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="-3713.1" y="5864.6" z="170.4" yaw="270.8"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="-3747.9" y="5444.9" z="166.6" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="-3826.6" y="5444.2" z="167.4" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-4018.4" y="5442.4" z="169.4" yaw="-179.3"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-4125.1" y="5441.4" z="170.5" yaw="180.6"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-4610.5" y="5881.9" z="182.4" yaw="-179.2"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-5100.3" y="5872.4" z="186.7" yaw="-177.5"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-5668.2" y="5565.1" z="192.4" yaw="-118.0"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="-5622.9" y="4903.6" z="186.1" yaw="-360.1"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-4534.7" y="4816.9" z="167.0" yaw="271.0"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="-4863.7" y="4868.7" z="170.8" yaw="175.5"/>
         </scenario>
         <scenario name="ControlLoss_14" type="ControlLoss">
            <trigger_point x="-5745.7" y="4709.6" z="186.7" yaw="-88.9"/>
         </scenario>
         <scenario name="HighwayCutIn_1" type="HighwayCutIn">
            <trigger_point x="-2768.1" y="5999.7" z="160.0" yaw="167.3"/>
            <other_actor_location x="-2766.5" y="6011.6" z="160.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_2" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4351.2" y="5611.4" z="175.6" yaw="180.8"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="88"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="-5584.7" y="5690.9" z="191.6" yaw="-129.1"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="-5675.9" y="5550.4" z="192.4" yaw="-116.8"/>
            <distance value="102"/>
            <direction value="right"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="-5737.1" y="5370.1" z="192.4" yaw="-102.8"/>
            <distance value="72"/>
            <speed value="50"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="-5727.8" y="3613.7" z="169.8" yaw="-88.9"/>
         </scenario>
         <scenario name="ControlLoss_15" type="ControlLoss">
            <trigger_point x="-5728.6" y="3659.1" z="170.0" yaw="-88.9"/>
         </scenario>
         <scenario name="ControlLoss_16" type="ControlLoss">
            <trigger_point x="-5727.0" y="3447.9" z="169.8" yaw="-91.4"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="-4577.7" y="4847.0" z="167.3" yaw="176.4"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="-4585.0" y="4847.5" z="167.4" yaw="176.4"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4541.7" y="5031.9" z="168.3" yaw="91.0"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="66"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="-5223.6" y="4894.4" z="178.1" yaw="177.3"/>
            <distance value="88"/>
            <frequency from="15" to="80"/>
         </scenario>
      </scenarios>
   </route>
   <route id="9" town="Town13">
      <!-- Urban + residential -->
      <weathers>
         <!-- From partly cloudy with strong winds and no precipitation to heavy rain with strong winds and mostly cloudy skies -->
         <weather route_percentage="0"
            cloudiness="20.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="30.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="3.0"/>
         <weather route_percentage="100"
            cloudiness="80.0" precipitation="60.0" precipitation_deposits="60.0" wetness="0.0"
            wind_intensity="60.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="3.0"/>
      </weathers>
      <waypoints>
         <position x="5581.6" y="3556.7" z="158.0"/>
         <position x="5492.2" y="3788.6" z="159.0"/>
         <position x="5738.5" y="4126.5" z="159.3"/>
         <position x="5224.0" y="4342.8" z="157.8"/>
         <position x="5222.2" y="3689.5" z="160.4"/>
         <position x="5153.6" y="3561.0" z="159.7"/>
         <position x="5047.9" y="3333.4" z="156.6"/>
         <position x="5220.9" y="3195.3" z="155.7"/>
         <position x="5153.0" y="3073.4" z="153.8"/>
         <position x="4645.9" y="3090.0" z="149.3"/>
         <position x="4641.3" y="3135.1" z="149.7"/>
         <position x="4439.7" y="3845.4" z="150.6"/>
         <position x="4398.1" y="4407.9" z="148.0"/>
         <position x="4401.2" y="4439.1" z="147.9"/>
         <position x="3948.2" y="5479.6" z="156.2"/>
         <position x="3913.7" y="5488.6" z="157.3"/>
         <position x="3879.9" y="5468.0" z="158.8"/>
         <position x="3849.9" y="5274.5" z="161.6"/>
         <position x="3808.0" y="5225.9" z="163.8"/>
         <position x="3653.5" y="5254.9" z="170.3"/>
         <position x="3469.1" y="5373.5" z="172.9"/>
         <position x="3339.2" y="5378.4" z="173.8"/>
         <position x="3234.3" y="5372.6" z="173.4"/>
         <position x="3144.5" y="5374.4" z="172.3"/>
         <position x="2883.4" y="5513.2" z="164.4"/>
         <position x="2795.8" y="5409.8" z="164.2"/>
         <position x="2734.0" y="5232.6" z="165.8"/>
         <position x="2916.8" y="4976.5" z="176.5"/>
         <position x="3078.6" y="5151.5" z="176.3"/>
         <position x="3152.9" y="5115.1" z="178.3"/>
         <position x="3228.5" y="4982.9" z="181.9"/>
         <position x="3616.4" y="4990.8" z="175.0"/>
         <position x="3672.0" y="4971.1" z="172.5"/>
         <position x="3619.5" y="4905.4" z="175.2"/>
         <position x="3409.7" y="4861.4" z="182.7"/>
         <position x="3383.8" y="4809.0" z="183.5"/>
         <position x="3155.9" y="4602.5" z="186.4"/>
         <position x="3689.2" y="4643.2" z="170.4"/>
         <position x="3816.6" y="4720.6" z="163.4"/>
         <position x="3758.2" y="4991.0" z="167.8"/>
         <position x="3631.1" y="5104.3" z="173.2"/>
      </waypoints>
      <scenarios>
         <scenario name="ParkingExit_1" type="ParkingExit">
            <trigger_point x="5581.6" y="3556.7" z="158.0" yaw="179.4"/>
            <direction value="right"/>
            <front_vehicle_distance value="9"/>
            <behind_vehicle_distance value="9"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="5471.3" y="3557.8" z="158.5" yaw="179.4"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="5755.6" y="3786.1" z="158.2" yaw="359.4"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="5791.9" y="3913.6" z="158.8" yaw="90.6"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_2" type="SignalizedJunctionRightTurn">
            <trigger_point x="5790.0" y="4080.5" z="159.4" yaw="90.6"/>
            <flow_speed value="11"/>
            <source_dist_interval from="15" to="58"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="5285.1" y="4358.4" z="157.9" yaw="168.8"/>
         </scenario>
         <scenario name="PedestrianCrossing_2" type="PedestrianCrossing">
            <trigger_point x="5223.2" y="4050.5" z="160.0" yaw="269.8"/>
         </scenario>
         <scenario name="PriorityAtJunction_1" type="PriorityAtJunction">
            <trigger_point x="5223.2" y="4045.8" z="160.0" yaw="269.8"/>
         </scenario>
         <scenario name="PriorityAtJunction_2" type="PriorityAtJunction">
            <trigger_point x="5222.7" y="3854.5" z="160.7" yaw="269.8"/>
         </scenario>
         <scenario name="PedestrianCrossing_3" type="PedestrianCrossing">
            <trigger_point x="5222.7" y="3852.9" z="160.7" yaw="269.8"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_3" type="SignalizedJunctionRightTurn">
            <trigger_point x="5024.5" y="3562.3" z="159.3" yaw="179.4"/>
            <flow_speed value="11"/>
            <source_dist_interval from="16" to="56"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_4" type="SignalizedJunctionRightTurn">
            <trigger_point x="4969.9" y="3404.6" z="157.0" yaw="269.5"/>
            <flow_speed value="14"/>
            <source_dist_interval from="19" to="65"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="5164.2" y="3333.2" z="157.2" yaw="-0.1"/>
         </scenario>
         <scenario name="PedestrianCrossing_4" type="PedestrianCrossing">
            <trigger_point x="4625.0" y="3492.5" z="152.9" yaw="101.1"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="3937.5" y="5480.2" z="156.5" yaw="159.5"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_4" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3850.9" y="5288.6" z="161.4" yaw="265.1"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_5" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3720.6" y="5224.9" z="167.9" yaw="180.6"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="3472.5" y="5373.6" z="172.9" yaw="180.9"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="3287.2" y="5395.1" z="173.2" yaw="160.6"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_6" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3235.0" y="5304.1" z="175.1" yaw="270.6"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_7" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3180.8" y="5266.9" z="175.3" yaw="181.3"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3144.7" y="5364.1" z="172.5" yaw="91.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="PedestrianCrossing_6" type="PedestrianCrossing">
            <trigger_point x="3101.6" y="5414.6" z="170.6" yaw="180.3"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2855.8" y="5522.6" z="163.6" yaw="165.4"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="58"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_8" type="VehicleTurningRoutePedestrian">
            <trigger_point x="2797.9" y="5281.5" z="166.6" yaw="-89.1"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="2689.7" y="5232.1" z="164.6" yaw="180.6"/>
         </scenario>
         <scenario name="BlockedIntersection_5" type="BlockedIntersection">
            <trigger_point x="2638.0" y="5030.6" z="166.8" yaw="270.3"/>
         </scenario>
         <scenario name="BlockedIntersection_6" type="BlockedIntersection">
            <trigger_point x="2983.7" y="4977.8" z="178.1" yaw="1.2"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_9" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3039.7" y="5098.4" z="176.7" yaw="90.3"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_10" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3116.2" y="5152.3" z="177.0" yaw="1.2"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3154.3" y="5048.4" z="179.8" yaw="-88.8"/>
            <flow_speed value="11"/>
            <source_dist_interval from="14" to="59"/>
         </scenario>
         <scenario name="PedestrianCrossing_7" type="PedestrianCrossing">
            <trigger_point x="3351.4" y="4985.4" z="181.9" yaw="1.2"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_11" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3662.2" y="4944.3" z="173.1" yaw="-117.1"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3451.0" y="4902.2" z="181.4" yaw="181.1"/>
            <flow_speed value="11"/>
            <source_dist_interval from="10" to="60"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_12" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3409.9" y="4852.3" z="182.7" yaw="271.1"/>
         </scenario>
         <scenario name="BlockedIntersection_7" type="BlockedIntersection">
            <trigger_point x="3191.0" y="4806.4" z="184.4" yaw="180.8"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3137.3" y="4669.3" z="185.7" yaw="270.7"/>
            <flow_speed value="12"/>
            <source_dist_interval from="15" to="65"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="3434.6" y="4611.2" z="182.7" yaw="4.2"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_6" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3692.7" y="4643.3" z="170.2" yaw="0.8"/>
            <flow_speed value="12"/>
            <source_dist_interval from="15" to="62"/>
         </scenario>
         <scenario name="BlockedIntersection_8" type="BlockedIntersection">
            <trigger_point x="3869.7" y="4948.1" z="161.5" yaw="81.7"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_1" type="ParkingCrossingPedestrian">
            <trigger_point x="5568.9" y="3556.8" z="158.1" yaw="179.4"/>
            <distance value="73"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_2" type="ParkingCrossingPedestrian">
            <trigger_point x="5416.7" y="3584.6" z="158.9" yaw="90.3"/>
            <distance value="80"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_3" type="ParkingCrossingPedestrian">
            <trigger_point x="5432.9" y="3789.2" z="159.4" yaw="359.4"/>
            <distance value="75"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="5793.1" y="3803.2" z="158.4" yaw="90.6"/>
            <distance value="75"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="5790.9" y="3996.1" z="159.1" yaw="90.6"/>
            <distance value="75"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_4" type="ParkingCrossingPedestrian">
            <trigger_point x="5223.4" y="4121.5" z="159.6" yaw="269.8"/>
            <distance value="75"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_5" type="ParkingCrossingPedestrian">
            <trigger_point x="5222.4" y="3746.2" z="160.6" yaw="269.8"/>
            <distance value="78"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_6" type="ParkingCrossingPedestrian">
            <trigger_point x="4971.0" y="3543.0" z="158.7" yaw="269.5"/>
            <distance value="75"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_7" type="ParkingCrossingPedestrian">
            <trigger_point x="5220.9" y="3193.5" z="155.7" yaw="269.8"/>
            <distance value="78"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="3550.0" y="5366.7" z="171.5" yaw="162.3"/>
            <distance value="78"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="3308.9" y="5387.1" z="173.5" yaw="160.3"/>
            <distance value="50"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="-1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_6" type="DynamicObjectCrossing">
            <trigger_point x="2777.4" y="5233.1" z="167.1" yaw="180.6"/>
            <distance value="70"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_7" type="DynamicObjectCrossing">
            <trigger_point x="2658.2" y="4971.2" z="168.7" yaw="1.2"/>
            <distance value="65"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_8" type="DynamicObjectCrossing">
            <trigger_point x="3040.3" y="4994.8" z="179.0" yaw="90.3"/>
            <distance value="70"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_9" type="DynamicObjectCrossing">
            <trigger_point x="3054.7" y="5151.0" z="175.8" yaw="1.2"/>
            <distance value="65"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_10" type="DynamicObjectCrossing">
            <trigger_point x="3427.0" y="4986.9" z="181.0" yaw="1.2"/>
            <distance value="70"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_11" type="DynamicObjectCrossing">
            <trigger_point x="3149.4" y="4602.4" z="186.4" yaw="1.4"/>
            <distance value="50"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_1" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5537.6" y="3788.2" z="158.8" yaw="359.4"/>
            <distance value="75"/>
            <frequency from="20" to="75"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="5766.54" y="4125.88" z="159.48" yaw="179.44"/>
            <distance value="80"/>
            <speed value="49"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="5590.5" y="4165.9" z="158.9" yaw="153.3"/>
            <distance value="75"/>
            <speed value="50"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_2" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5224.0" y="4350.3" z="157.8" yaw="269.8"/>
            <distance value="75"/>
            <frequency from="15" to="85"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_3" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5194.6" y="3560.6" z="159.7" yaw="179.4"/>
            <distance value="77"/>
            <frequency from="25" to="90"/>
         </scenario>
         <scenario name="ParkedObstacle_1" type="ParkedObstacle">
            <trigger_point x="4642.6" y="3094.4" z="149.3" yaw="91.7"/>
            <distance value="70"/>
         </scenario>
         <scenario name="Accident_2" type="Accident">
            <trigger_point x="4634.9" y="3348.7" z="151.7" yaw="91.7"/>
            <distance value="70"/>
         </scenario>
         <scenario name="ConstructionObstacle_3" type="ConstructionObstacle">
            <trigger_point x="4400.33" y="4017.47" z="149.58" yaw="92.77"/>
            <distance value="80"/>
            <speed value="45"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="3882.2" y="5481.7" z="158.6" yaw="260.3"/>
            <distance value="75"/>
            <frequency from="25" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="3829.4" y="5226.1" z="162.8" yaw="180.6"/>
            <distance value="70"/>
            <frequency from="15" to="100"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="3655.3" y="5246.0" z="170.4" yaw="99.9"/>
            <distance value="100"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="3234.0" y="5396.0" z="172.8" yaw="270.6"/>
            <distance value="75"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="3038.5" y="5420.5" z="169.4" yaw="166.2"/>
            <distance value="110"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="2794.4" y="5492.2" z="162.8" yaw="-89.1"/>
            <distance value="75"/>
            <frequency from="25" to="110"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="2637.1" y="5206.4" z="163.5" yaw="270.3"/>
            <distance value="77"/>
            <frequency from="35" to="115"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_3" type="ParkedObstacleTwoWays">
            <trigger_point x="2822.1" y="4974.5" z="173.9" yaw="1.2"/>
            <distance value="78"/>
            <frequency from="18" to="99"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_4" type="ParkedObstacleTwoWays">
            <trigger_point x="3152.4" y="5137.3" z="177.8" yaw="-88.8"/>
            <distance value="74"/>
            <frequency from="15" to="95"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="3188.9" y="4982.1" z="181.5" yaw="1.2"/>
            <distance value="74"/>
            <frequency from="20" to="98"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="3623.3" y="4905.5" z="175.1" yaw="181.1"/>
            <distance value="78"/>
            <frequency from="20" to="105"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="3394.9" y="4809.1" z="183.3" yaw="180.8"/>
            <distance value="76"/>
            <frequency from="25" to="99"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="3506.8" y="4618.3" z="179.9" yaw="6.9"/>
            <distance value="110"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="3800.0" y="4652.1" z="163.7" yaw="76.4"/>
            <distance value="77"/>
            <frequency from="18" to="89"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_5" type="ParkedObstacleTwoWays">
            <trigger_point x="3861.3" y="4993.1" z="162.1" yaw="181.2"/>
            <distance value="75"/>
            <frequency from="18" to="90"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="5223.0" y="3962.6" z="160.4" yaw="269.8"/>
         </scenario>
         <scenario name="ParkingCutIn_2" type="ParkingCutIn">
            <trigger_point x="4970.3" y="3453.7" z="157.6" yaw="269.5"/>
         </scenario>
         <scenario name="ParkingCutIn_3" type="ParkingCutIn">
            <trigger_point x="5012.2" y="3333.5" z="156.3" yaw="-0.1"/>
         </scenario>
         <scenario name="ParkingCutIn_4" type="ParkingCutIn">
            <trigger_point x="5221.2" y="3303.3" z="157.1" yaw="269.8"/>
         </scenario>
         <scenario name="StaticCutIn_3" type="StaticCutIn">
            <trigger_point x="4401.2" y="4442.0" z="147.9" yaw="90.3"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_4" type="StaticCutIn">
            <trigger_point x="4400.5" y="4603.4" z="147.5" yaw="90.3"/>
            <distance value="109"/>
            <direction value="right"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="3381.7" y="5372.7" z="173.8" yaw="177.2"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="3575.1" y="4990.0" z="176.7" yaw="1.2"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="3136.1" y="4773.2" z="184.4" yaw="270.7"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="3229.4" y="4604.4" z="186.4" yaw="1.4"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="3314.4" y="4606.4" z="185.6" yaw="1.4"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="3842.6" y="4827.8" z="162.6" yaw="76.4"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="3671.7" y="5011.4" z="172.3" yaw="96.2"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="5416.7" y="3580.1" z="158.9" yaw="90.3"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="5613.3" y="4155.4" z="158.9" yaw="157.1"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="4597.8" y="3579.9" z="153.0" yaw="113.5"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="3373.9" y="5373.2" z="173.8" yaw="175.5"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="3146.4" y="5286.3" z="174.4" yaw="91.2"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="2794.1" y="5512.1" z="162.5" yaw="-89.1"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="2653.0" y="4971.1" z="168.5" yaw="1.2"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="3572.6" y="4989.9" z="176.8" yaw="1.2"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="3135.8" y="4790.9" z="184.1" yaw="270.7"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="3235.1" y="4604.5" z="186.4" yaw="1.4"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="3310.3" y="4606.3" z="185.6" yaw="1.4"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="3842.2" y="4826.2" z="162.6" yaw="76.4"/>
         </scenario>
         <scenario name="ConstructionObstacle_2" type="ConstructionObstacle">
            <trigger_point x="5377.6" y="4326.7" z="158.3" yaw="153.4"/>
            <distance value="78"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="5222.1" y="3630.8" z="160.1" yaw="269.8"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="5187.9" y="3073.5" z="154.0" yaw="180.3"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="4949.6" y="3072.4" z="152.0" yaw="180.3"/>
            <distance value="120"/>
            <direction value="right"/>
         </scenario>
         <scenario name="HazardAtSideLane_1" type="HazardAtSideLane">
            <trigger_point x="4540.6" y="3675.1" z="152.3" yaw="126.7"/>
            <distance value="70"/>
            <bicycle_drive_distance value="80"/>
            <bicycle_speed value="11"/>
         </scenario>
         <scenario name="PedestrianCrossing_5" type="PedestrianCrossing">
            <trigger_point x="4420.7" y="3910.2" z="150.1" yaw="105.3"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="4398.9" y="4237.4" z="148.7" yaw="90.3"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="4398.7" y="4853.6" z="147.3" yaw="92.8"/>
         </scenario>
         <scenario name="StaticCutIn_5" type="StaticCutIn">
            <trigger_point x="4345.0" y="5092.1" z="147.6" yaw="112.6"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_6" type="StaticCutIn">
            <trigger_point x="4220.8" y="5289.9" z="149.2" yaw="131.6"/>
            <distance value="110"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_13" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3727.2" y="4990.4" z="169.5" yaw="181.2"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="3632.4" y="4991.2" z="174.3" yaw="1.2"/>
         </scenario>
      </scenarios>
   </route>
   <route id="10" town="Town13">
      <!-- Urbanization route -->
      <weathers>
      <!-- From a heavily overcast and stormy downpour to a slightly less cloudy, windy, and rainy night -->
         <weather route_percentage="0"
            cloudiness="100.0" precipitation="100.0" precipitation_deposits="90.0" wetness="0.0"
            wind_intensity="100.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="4.0"/>
         <weather route_percentage="100"
            cloudiness="90.0" precipitation="50.0" precipitation_deposits="60.0" wetness="0.0"
            wind_intensity="100.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-90.0" fog_density="8.0"/>
      </weathers>
      <waypoints>
         <position x="6090.4" y="4311.7" z="164.9"/>
         <position x="6092.0" y="4161.8" z="163.5"/>
         <position x="6509.8" y="4257.1" z="171.4"/>
         <position x="6622.8" y="4057.9" z="171.3"/>
         <position x="6850.0" y="3976.4" z="174.2"/>
         <position x="6906.5" y="3651.1" z="172.1"/>
         <position x="7058.9" y="3515.7" z="172.7"/>
         <position x="6958.8" y="2602.0" z="167.1"/>
         <position x="6819.8" y="2560.7" z="166.1"/>
         <position x="5800.4" y="1737.3" z="147.3"/>
         <position x="4615.8" y="831.6" z="163.0"/>
         <position x="3827.6" y="1022.8" z="180.4"/>
         <position x="3616.3" y="995.3" z="178.3"/>
         <position x="3360.5" y="879.6" z="174.5"/>
         <position x="3496.1" y="701.5" z="176.1"/>
         <position x="3622.4" y="655.4" z="177.7"/>
         <position x="2977.1" y="399.5" z="170.1"/>
         <position x="2714.3" y="1017.6" z="166.2"/>
      </waypoints>
      <scenarios>
         <scenario name="ParkingExit_1" type="ParkingExit">
            <trigger_point x="6090.4" y="4311.7" z="164.9" yaw="270.6"/>
            <direction value="right"/>
            <front_vehicle_distance value="9"/>
            <behind_vehicle_distance value="9"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="6091.8" y="4183.2" z="163.7" yaw="270.6"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="6290.5" y="4189.5" z="167.1" yaw="42.3"/>
            <distance value="100"/>
            <speed value="50"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="6456.6" y="4256.0" z="170.5" yaw="1.2"/>
            <distance value="100"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="6777.4" y="4305.7" z="176.2" yaw="90.5"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="6775.6" y="4513.3" z="178.1" yaw="90.5"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="6675.4" y="4250.9" z="174.1" yaw="-178.8"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="6623.2" y="4008.6" z="170.8" yaw="270.5"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="6541.3" y="2560.3" z="162.3" yaw="180.1"/>
         </scenario>
         <scenario name="BlockedIntersection_5" type="BlockedIntersection">
            <trigger_point x="3723.3" y="1026.5" z="179.6" yaw="178.0"/>
         </scenario>
         <scenario name="VehicleTurningRoute_5" type="VehicleTurningRoute">
            <trigger_point x="3622.9" y="628.7" z="177.6" yaw="271.0"/>
         </scenario>
         <scenario name="BlockedIntersection_7" type="BlockedIntersection">
            <trigger_point x="2787.7" y="1059.3" z="167.2" yaw="178.0"/>
         </scenario>
         <scenario name="HazardAtSideLane_1" type="HazardAtSideLane">
            <trigger_point x="7059.2" y="3493.1" z="172.5" yaw="-89.2"/>
            <distance value="55"/>
            <bicycle_drive_distance value="80"/>
            <bicycle_speed value="9"/>
         </scenario>
         <scenario name="PriorityAtJunction_2" type="PriorityAtJunction">
            <trigger_point x="6814.6" y="2560.7" z="166.0" yaw="180.1"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_2" type="OppositeVehicleRunningRedLight">
            <trigger_point x="6791.9" y="2560.7" z="165.8" yaw="180.1"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="6003.1" y="2175.9" z="150.8" yaw="297.7"/>
            <distance value="60"/>
            <frequency from="35" to="120"/>
         </scenario>
         <scenario name="ConstructionObstacle_3" type="ConstructionObstacle">
            <trigger_point x="6082.4" y="1921.8" z="150.4" yaw="211.2"/>
            <distance value="80"/>
            <speed value="50"/>
         </scenario>
         <scenario name="StaticCutIn_5" type="StaticCutIn">
            <trigger_point x="5060.9" y="1177.3" z="145.9" yaw="-136.4"/>
            <distance value="120"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_6" type="StaticCutIn">
            <trigger_point x="4822.8" y="951.6" z="154.1" yaw="-136.6"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="3945.7" y="1018.7" z="180.6" yaw="178.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3399.1" y="879.8" z="175.0" yaw="180.3"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3346.1" y="738.7" z="174.1" yaw="270.1"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3574.7" y="701.6" z="177.2" yaw="0.1"/>
            <flow_speed value="8"/>
            <source_dist_interval from="25" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3622.5" y="647.8" z="177.7" yaw="271.0"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="40"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3026.0" y="353.9" z="170.8" yaw="180.4"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="7040.8" y="4510.4" z="181.9" yaw="270.9"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="6698.7" y="2560.5" z="164.7" yaw="180.1"/>
            <distance value="60"/>
            <frequency from="45" to="120"/>
         </scenario>
         <scenario name="PedestrianCrossing_9" type="PedestrianCrossing">
            <trigger_point x="2968.90" y="637.11" z="169.53" yaw="93.64"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="6090.7" y="4289.8" z="164.7" yaw="270.6"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_1" type="ParkingCrossingPedestrian">
            <trigger_point x="6776.8" y="4376.9" z="176.9" yaw="90.5"/>
            <distance value="50"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_2" type="ParkingCrossingPedestrian">
            <trigger_point x="6825.8" y="4557.5" z="179.2" yaw="0.1"/>
            <distance value="50"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="7042.30" y="4421.61" z="181.28" yaw="270.94"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="7044.3" y="4299.9" z="180.3" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_1" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6621.2" y="4229.9" z="172.9" yaw="270.5"/>
            <distance value="65"/>
            <frequency from="35" to="100"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_3" type="ParkingCrossingPedestrian">
            <trigger_point x="6656.6" y="3977.4" z="171.1" yaw="-0.3"/>
            <distance value="50"/>
         </scenario>
         <scenario name="ParkingCutIn_3" type="ParkingCutIn">
            <trigger_point x="6753.2" y="3976.9" z="172.7" yaw="-0.3"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6846.1" y="3976.4" z="174.1" yaw="-0.3"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_2" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6904.2" y="3947.7" z="174.8" yaw="270.4"/>
            <distance value="70"/>
            <frequency from="35" to="100"/>
         </scenario>
         <scenario name="PedestrianCrossing_2" type="PedestrianCrossing">
            <trigger_point x="6904.92" y="3851.22" z="173.87" yaw="270.45"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_4" type="ParkingCrossingPedestrian">
            <trigger_point x="6905.7" y="3747.7" z="172.9" yaw="270.4"/>
            <distance value="60"/>
            <direction value="right"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="ParkingCutIn_4" type="ParkingCutIn">
            <trigger_point x="6928.8" y="3543.0" z="171.4" yaw="-0.6"/>
         </scenario>
         <scenario name="PedestrianCrossing_3" type="PedestrianCrossing">
            <trigger_point x="6989.0" y="3542.4" z="172.1" yaw="-0.6"/>
         </scenario>
         <scenario name="PedestrianCrossing_4" type="PedestrianCrossing">
            <trigger_point x="7060.45" y="3398.09" z="171.54" yaw="-89.25"/>
         </scenario>
         <scenario name="PriorityAtJunction_1" type="PriorityAtJunction">
            <trigger_point x="7060.5" y="3396.0" z="171.5" yaw="-89.2"/>
         </scenario>
         <scenario name="PedestrianCrossing_6" type="PedestrianCrossing">
            <trigger_point x="6324.7" y="2371.5" z="157.6" yaw="-151.3"/>
         </scenario>
         <scenario name="ParkingCutIn_5" type="ParkingCutIn">
            <trigger_point x="3616.0" y="1012.9" z="178.4" yaw="271.0"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_5" type="ParkingCrossingPedestrian">
            <trigger_point x="3557.6" y="880.5" z="177.3" yaw="180.3"/>
            <distance value="50"/>
         </scenario>
         <scenario name="ParkingCutIn_6" type="ParkingCutIn">
            <trigger_point x="3345.8" y="856.4" z="174.2" yaw="270.1"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_6" type="ParkingCrossingPedestrian">
            <trigger_point x="3378.2" y="701.2" z="174.5" yaw="0.1"/>
            <distance value="50"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_5" type="VehicleOpensDoorTwoWays">
            <trigger_point x="3572.2" y="604.2" z="177.0" yaw="180.7"/>
            <distance value="75"/>
            <frequency from="40" to="100"/>
         </scenario>
         <scenario name="PedestrianCrossing_8" type="PedestrianCrossing">
            <trigger_point x="3407.9" y="602.2" z="174.9" yaw="180.7"/>
         </scenario>
         <scenario name="ParkingCutIn_8" type="ParkingCutIn">
            <trigger_point x="3148.7" y="472.5" z="171.9" yaw="270.9"/>
         </scenario>
         <scenario name="BlockedIntersection_6" type="BlockedIntersection">
            <trigger_point x="3149.8" y="402.1" z="172.1" yaw="270.9"/>
         </scenario>
         <scenario name="PedestrianCrossing_5" type="PedestrianCrossing">
            <trigger_point x="7056.75" y="3155.51" z="169.25" yaw="-90.19"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="7057.3" y="3308.3" z="170.7" yaw="-90.2"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="7056.4" y="3046.2" z="168.4" yaw="269.8"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_3" type="StaticCutIn">
            <trigger_point x="5778.4" y="1725.8" z="147.1" yaw="215.4"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="PedestrianCrossing_7" type="PedestrianCrossing">
            <trigger_point x="4266.89" y="1010.63" z="175.11" yaw="177.99"/>
         </scenario>
         <scenario name="Accident_2" type="Accident">
            <trigger_point x="6634.7" y="4259.8" z="173.4" yaw="1.2"/>
            <distance value="50"/>
         </scenario>
         <scenario name="Accident_3" type="Accident">
            <trigger_point x="4635.0" y="807.6" z="162.6" yaw="129.9"/>
            <distance value="50"/>
            <speed value="50"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="5920.7" y="1819.3" z="148.2" yaw="213.4"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="5162.0" y="1268.0" z="144.4" yaw="219.7"/>
         </scenario>
         <scenario name="ConstructionObstacle_4" type="ConstructionObstacle">
            <trigger_point x="4181.0" y="1010.4" z="177.5" yaw="178.0"/>
            <distance value="50"/>
            <speed value="50"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="4531.4" y="981.6" z="164.8" yaw="111.6"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_9" type="ParkingCrossingPedestrian">
            <trigger_point x="2967.0" y="900.9" z="169.3" yaw="89.1"/>
            <distance value="40"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="2949.3" y="1053.6" z="169.3" yaw="178.0"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="2941.1" y="1053.9" z="169.2" yaw="178.0"/>
         </scenario>
         <scenario name="StaticCutIn_4" type="StaticCutIn">
            <trigger_point x="5357.8" y="1416.0" z="144.1" yaw="216.5"/>
            <distance value="120"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCutIn_2" type="ParkingCutIn">
            <trigger_point x="6879.2" y="4557.5" z="180.0" yaw="0.1"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ConstructionObstacle_2" type="ConstructionObstacle">
            <trigger_point x="7007.9" y="4258.0" z="179.4" yaw="-178.8"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_7" type="ParkingCrossingPedestrian">
            <trigger_point x="3346.5" y="590.7" z="174.1" yaw="270.1"/>
            <distance value="30"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3346.6" y="557.4" z="174.1" yaw="270.1"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_6" type="VehicleOpensDoorTwoWays">
            <trigger_point x="3316.75" y="513.01" z="173.85" yaw="180.59"/>
            <distance value="80"/>
            <frequency from="45" to="80"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3200.5" y="511.8" z="172.4" yaw="180.6"/>
            <flow_speed value="14"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_7" type="VehicleOpensDoorTwoWays">
            <trigger_point x="2965.6" y="710.4" z="169.4" yaw="91.5"/>
            <distance value="75"/>
            <frequency from="25" to="65"/>
         </scenario>
         <scenario name="ParkedObstacle_1" type="ParkedObstacle">
            <trigger_point x="6745.11" y="4252.39" z="175.22" yaw="-178.78"/>
            <distance value="66"/>
            <speed value="60"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="6735.1" y="4261.9" z="175.1" yaw="1.2"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="6859.0" y="4254.8" z="177.1" yaw="-178.8"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="6906.9" y="3599.6" z="171.6" yaw="270.4"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="7040.5" y="2848.5" z="167.4" yaw="259.5"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_2" type="SignalizedJunctionLeftTurn">
            <trigger_point x="6962.6" y="2609.8" z="167.1" yaw="244.3"/>
            <flow_speed value="12"/>
            <source_dist_interval from="20" to="45"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="6908.6" y="2560.8" z="166.9" yaw="180.1"/>
            <distance value="70"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_3" type="SignalizedJunctionLeftTurn">
            <trigger_point x="6515.2" y="2515.9" z="161.8" yaw="-72.3"/>
            <flow_speed value="14"/>
            <source_dist_interval from="12" to="55"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_3" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6505.9" y="2470.8" z="161.6" yaw="-151.3"/>
            <distance value="70"/>
            <frequency from="35" to="65"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_4" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6191.3" y="2298.4" z="154.5" yaw="-151.3"/>
            <distance value="65"/>
            <frequency from="40" to="70"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="6041.4" y="2216.2" z="151.5" yaw="-151.3"/>
            <flow_speed value="9"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="4731.3" y="864.5" z="158.5" yaw="-136.0"/>
            <flow_speed value="15"/>
            <source_dist_interval from="20" to="55"/>
         </scenario>
         <scenario name="Accident_4" type="Accident">
            <trigger_point x="3851.8" y="1021.9" z="180.5" yaw="178.0"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="ParkingCutIn_7" type="ParkingCutIn">
            <trigger_point x="3471.8" y="701.4" z="175.8" yaw="0.1"/>
         </scenario>
         <scenario name="ParkingCutIn_9" type="ParkingCutIn">
            <trigger_point x="2976.9" y="378.7" z="170.2" yaw="89.4"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_8" type="ParkingCrossingPedestrian">
            <trigger_point x="2977.1" y="482.5" z="169.9" yaw="91.0"/>
            <distance value="70"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2968.3" y="988.1" z="169.4" yaw="89.1"/>
            <flow_speed value="15"/>
            <source_dist_interval from="18" to="65"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="7010.8" y="2730.5" z="167.1" yaw="252.2"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="7009.5" y="2726.5" z="167.1" yaw="251.9"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="5540.1" y="1550.7" z="145.5" yaw="216.5"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="5535.8" y="1547.5" z="145.4" yaw="216.5"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="4483.3" y="1026.6" z="166.6" yaw="195.6"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="4481.1" y="1025.9" z="166.7" yaw="195.3"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3617.7" y="921.6" z="178.2" yaw="271.0"/>
         </scenario>
      </scenarios>
   </route>
   <route id="11" town="Town13">
      <!-- Urbanization route -->
      <weathers>
      <!-- From a moderately cloudy and stormy downpour to an overcast evening with light rain, strong winds, and extremely dense fog -->
         <weather route_percentage="0"
            cloudiness="50.0" precipitation="100.0" precipitation_deposits="90.0" wetness="0.0"
            wind_intensity="100.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="7.0"/>
         <weather route_percentage="100"
            cloudiness="100.0" precipitation="30.0" precipitation_deposits="60.0" wetness="0.0"
            wind_intensity="60.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-45.0" fog_density="80.0"/>
      </weathers>
      <waypoints>
         <position x="6770.8" y="5443.4" z="178.7"/>
         <position x="6978.1" y="5225.7" z="182.8"/>
         <position x="6795.8" y="5076.4" z="180.4"/>
         <position x="6779.4" y="4423.5" z="177.3"/>
         <position x="6593.3" y="4249.2" z="172.8"/>
         <position x="5788.6" y="4198.0" z="160.0"/>
         <position x="6086.5" y="4404.0" z="165.8"/>
         <position x="5597.4" y="5066.2" z="159.0"/>
         <position x="5516.2" y="4743.4" z="157.9"/>
         <position x="6273.6" y="4510.3" z="170.1"/>
         <position x="6500.9" y="4902.1" z="176.0"/>
         <position x="6920.7" y="4359.8" z="178.9"/>
      </waypoints>
      <scenarios>
         <scenario name="ParkingExit_1" type="ParkingExit">
            <trigger_point x="6770.8" y="5443.4" z="178.7" yaw="-89.5"/>
            <direction value="right"/>
            <front_vehicle_distance value="9"/>
            <behind_vehicle_distance value="9"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="6772.3" y="5263.0" z="179.4" yaw="-89.5"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="7036.6" y="4771.9" z="183.2" yaw="270.9"/>
            <flow_speed value="15"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_2" type="SignalizedJunctionLeftTurn">
            <trigger_point x="6772.5" y="4888.2" z="180.1" yaw="90.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="14" to="60"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_2" type="SignalizedJunctionRightTurn">
            <trigger_point x="6970.5" y="4931.4" z="182.8" yaw="-0.1"/>
            <flow_speed value="15"/>
            <source_dist_interval from="17" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_3" type="SignalizedJunctionRightTurn">
            <trigger_point x="6829.9" y="5076.7" z="180.9" yaw="180.6"/>
            <flow_speed value="12"/>
            <source_dist_interval from="18" to="50"/>
         </scenario>
         <scenario name="PriorityAtJunction_1" type="PriorityAtJunction">
            <trigger_point x="6776.5" y="4769.9" z="179.8" yaw="-89.5"/>
         </scenario>
         <scenario name="PriorityAtJunction_2" type="PriorityAtJunction">
            <trigger_point x="6777.9" y="4603.8" z="178.8" yaw="-89.5"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="6780.4" y="4302.5" z="176.2" yaw="-89.5"/>
            <flow_speed value="14"/>
            <source_dist_interval from="20" to="57"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_3" type="SignalizedJunctionLeftTurn">
            <trigger_point x="7048.7" y="4026.7" z="177.8" yaw="-89.1"/>
            <flow_speed value="15"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="6618.4" y="4213.6" z="172.8" yaw="90.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="PriorityAtJunction_3" type="PriorityAtJunction">
            <trigger_point x="6164.9" y="4123.3" z="164.4" yaw="-174.3"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="6160.9" y="4123.0" z="164.3" yaw="-175.3"/>
            <direction value="left"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_4" type="SignalizedJunctionLeftTurn">
            <trigger_point x="5853.2" y="4128.3" z="160.2" yaw="179.4"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_4" type="SignalizedJunctionRightTurn">
            <trigger_point x="6026.5" y="4359.1" z="164.3" yaw="0.0"/>
            <flow_speed value="8"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="PriorityAtJunction_4" type="PriorityAtJunction">
            <trigger_point x="5776.5" y="5014.3" z="162.4" yaw="90.9"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_6" type="SignalizedJunctionRightTurn">
            <trigger_point x="5774.4" y="5152.9" z="162.2" yaw="90.9"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_7" type="SignalizedJunctionRightTurn">
            <trigger_point x="5638.5" y="5217.8" z="159.4" yaw="180.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_5" type="SignalizedJunctionLeftTurn">
            <trigger_point x="5714.9" y="5067.7" z="161.2" yaw="0.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="20" to="45"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_2" type="OppositeVehicleRunningRedLight">
            <trigger_point x="5716.3" y="4509.7" z="160.5" yaw="0.1"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_3" type="OppositeVehicleRunningRedLight">
            <trigger_point x="6027.0" y="4510.0" z="165.5" yaw="0.1"/>
            <direction value="left"/>
         </scenario>
         <scenario name="PriorityAtJunction_5" type="PriorityAtJunction">
            <trigger_point x="6034.1" y="4510.0" z="165.7" yaw="0.1"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_9" type="SignalizedJunctionRightTurn">
            <trigger_point x="6272.2" y="4510.3" z="170.1" yaw="0.1"/>
            <flow_speed value="9"/>
            <source_dist_interval from="20" to="50"/>
         </scenario>
         <scenario name="PriorityAtJunction_6" type="PriorityAtJunction">
            <trigger_point x="6320.2" y="4706.6" z="172.4" yaw="90.9"/>
         </scenario>
         <scenario name="PriorityAtJunction_7" type="PriorityAtJunction">
            <trigger_point x="6315.4" y="5010.0" z="172.6" yaw="90.9"/>
         </scenario>
         <scenario name="PriorityAtJunction_8" type="PriorityAtJunction">
            <trigger_point x="6497.2" y="5140.1" z="175.3" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="7036.4" y="4782.6" z="183.3" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="6772.5" y="4880.5" z="180.1" yaw="90.5"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="7025.9" y="5024.4" z="183.6" yaw="90.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="7048.6" y="4036.0" z="177.9" yaw="-89.1"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6665.3" y="3974.3" z="171.3" yaw="179.7"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="5575.9" y="5107.7" z="158.4" yaw="256.7"/>
         </scenario>
         <scenario name="VehicleTurningRoute_5" type="VehicleTurningRoute">
            <trigger_point x="5782.5" y="4811.7" z="162.4" yaw="270.9"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="5509.0" y="4581.2" z="158.1" yaw="261.0"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_4" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6313.0" y="5165.0" z="172.0" yaw="90.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_6" type="VehicleTurningRoute">
            <trigger_point x="6505.5" y="4605.9" z="174.8" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_6" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6735.3" y="4557.4" z="177.9" yaw="0.1"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_4" type="ParkingCrossingPedestrian">
            <trigger_point x="6999.0" y="5078.5" z="183.2" yaw="180.6"/>
            <distance value="45"/>
            <direction value="right"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="Accident_4" type="Accident">
            <trigger_point x="5516.3" y="4737.5" z="157.9" yaw="271.2"/>
            <distance value="70"/>
            <direction value="right"/>
            <speed value="55"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="6798.8" y="5225.0" z="180.0" yaw="0.2"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="7032.1" y="5045.6" z="183.6" yaw="270.9"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="6778.5" y="4527.1" z="178.3" yaw="-89.5"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="5775.4" y="5088.5" z="162.3" yaw="90.9"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="6313.9" y="5110.0" z="172.2" yaw="90.9"/>
         </scenario>
         <scenario name="HardBreakRoute_10" type="HardBreakRoute">
            <trigger_point x="6818.1" y="4359.8" z="177.4" yaw="0.0"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_1" type="ParkingCrossingPedestrian">
            <trigger_point x="6794.7" y="5225.0" z="180.0" yaw="0.2"/>
            <distance value="55"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="7030.4" y="5150.8" z="183.6" yaw="270.9"/>
         </scenario>
         <scenario name="PedestrianCrossing_2" type="PedestrianCrossing">
            <trigger_point x="7032.9" y="4996.3" z="183.6" yaw="270.9"/>
         </scenario>
         <scenario name="ParkingCutIn_3" type="ParkingCutIn">
            <trigger_point x="6773.7" y="4745.7" z="179.7" yaw="90.5"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCutIn_4" type="ParkingCutIn">
            <trigger_point x="6773.1" y="4815.8" z="179.9" yaw="90.5"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_3" type="ParkingCrossingPedestrian">
            <trigger_point x="6792.5" y="4931.7" z="180.4" yaw="-0.1"/>
            <distance value="60"/>
            <direction value="right"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_2" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6774.0" y="5060.9" z="180.1" yaw="-89.5"/>
            <distance value="70"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_3" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6775.3" y="4910.8" z="180.1" yaw="-89.5"/>
            <distance value="60"/>
            <frequency from="40" to="115"/>
         </scenario>
         <scenario name="PedestrianCrossing_3" type="PedestrianCrossing">
            <trigger_point x="6777.77" y="4616.99" z="178.93" yaw="-89.51"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_6" type="ParkingCrossingPedestrian">
            <trigger_point x="7025.8" y="3972.5" z="176.9" yaw="179.7"/>
            <distance value="60"/>
            <direction value="right"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="ParkingCutIn_7" type="ParkingCutIn">
            <trigger_point x="6880.3" y="3973.2" z="174.7" yaw="179.7"/>
            <direction value="right"/>
         </scenario>
         <scenario name="PedestrianCrossing_4" type="PedestrianCrossing">
            <trigger_point x="6698.25" y="3974.15" z="171.81" yaw="179.71"/>
         </scenario>
         <scenario name="ParkingCutIn_8" type="ParkingCutIn">
            <trigger_point x="6620.3" y="3995.5" z="170.7" yaw="90.5"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_4" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5789.4" y="4144.5" z="159.7" yaw="90.9"/>
            <distance value="70"/>
            <frequency from="35" to="110"/>
         </scenario>
         <scenario name="PedestrianCrossing_5" type="PedestrianCrossing">
            <trigger_point x="5787.21" y="4292.51" z="160.40" yaw="90.85"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_8" type="ParkingCrossingPedestrian">
            <trigger_point x="5810.5" y="4359.0" z="161.0" yaw="0.0"/>
            <distance value="70"/>
            <direction value="right"/>
            <crossing_angle value="4"/>
         </scenario>
         <scenario name="ParkingCutIn_9" type="ParkingCutIn">
            <trigger_point x="6086.7" y="4382.7" z="165.6" yaw="90.6"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_5" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5779.9" y="4785.0" z="162.3" yaw="90.9"/>
            <distance value="70"/>
            <frequency from="35" to="100"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_11" type="ParkingCrossingPedestrian">
            <trigger_point x="5777.5" y="4941.4" z="162.5" yaw="90.9"/>
            <distance value="50"/>
            <direction value="right"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="PedestrianCrossing_8" type="PedestrianCrossing">
            <trigger_point x="5776.8" y="4989.7" z="162.5" yaw="90.9"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_12" type="ParkingCrossingPedestrian">
            <trigger_point x="5775.3" y="5094.0" z="162.3" yaw="90.9"/>
            <distance value="50"/>
            <direction value="right"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_13" type="ParkingCrossingPedestrian">
            <trigger_point x="5751.3" y="5218.2" z="161.5" yaw="180.2"/>
            <distance value="45"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCutIn_11" type="ParkingCutIn">
            <trigger_point x="5593.2" y="5066.1" z="158.9" yaw="0.7"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCutIn_13" type="ParkingCutIn">
            <trigger_point x="5763.6" y="4759.3" z="162.0" yaw="180.4"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_15" type="ParkingCrossingPedestrian">
            <trigger_point x="5512.3" y="4509.4" z="158.3" yaw="0.1"/>
            <distance value="45"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_16" type="ParkingCrossingPedestrian">
            <trigger_point x="5596.5" y="4509.5" z="159.1" yaw="0.1"/>
            <distance value="50"/>
            <direction value="right"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_17" type="ParkingCrossingPedestrian">
            <trigger_point x="6113.7" y="4510.1" z="167.2" yaw="0.1"/>
            <distance value="70"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_18" type="ParkingCrossingPedestrian">
            <trigger_point x="6323.0" y="4531.7" z="171.2" yaw="90.9"/>
            <distance value="70"/>
            <direction value="right"/>
         </scenario>
         <scenario name="PedestrianCrossing_10" type="PedestrianCrossing">
            <trigger_point x="6320.31" y="4699.91" z="172.37" yaw="90.90"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_7" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6318.9" y="4789.1" z="172.7" yaw="90.9"/>
            <distance value="75"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="PedestrianCrossing_11" type="PedestrianCrossing">
            <trigger_point x="6315.52" y="5004.84" z="172.65" yaw="90.90"/>
         </scenario>
         <scenario name="ParkingCutIn_14" type="ParkingCutIn">
            <trigger_point x="6331.6" y="5223.3" z="172.0" yaw="0.2"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_5" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6429.8" y="5223.6" z="173.7" yaw="0.2"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_19" type="ParkingCrossingPedestrian">
            <trigger_point x="6496.4" y="5197.8" z="175.0" yaw="270.9"/>
            <distance value="55"/>
            <direction value="right"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="PedestrianCrossing_12" type="PedestrianCrossing">
            <trigger_point x="6497.39" y="5130.45" z="175.35" yaw="270.88"/>
         </scenario>
         <scenario name="ParkingCutIn_15" type="ParkingCutIn">
            <trigger_point x="6498.5" y="5056.1" z="175.7" yaw="270.9"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_20" type="ParkingCrossingPedestrian">
            <trigger_point x="6499.5" y="4996.8" z="175.9" yaw="270.9"/>
            <distance value="65"/>
            <direction value="right"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="PedestrianCrossing_13" type="PedestrianCrossing">
            <trigger_point x="6501.97" y="4833.18" z="175.90" yaw="270.88"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_9" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6503.3" y="4744.8" z="175.6" yaw="270.9"/>
            <distance value="55"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_10" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6524.9" y="4557.1" z="174.7" yaw="0.1"/>
            <distance value="60"/>
            <frequency from="45" to="100"/>
         </scenario>
         <scenario name="ParkingCutIn_6" type="ParkingCutIn">
            <trigger_point x="6778.5" y="4536.2" z="178.3" yaw="-89.5"/>
         </scenario>
         <scenario name="VehicleTurningRoute_4" type="VehicleTurningRoute">
            <trigger_point x="5857.6" y="4128.2" z="160.2" yaw="179.4"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="5787.0" y="4304.7" z="160.5" yaw="90.9"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_10" type="ParkingCrossingPedestrian">
            <trigger_point x="6045.3" y="4507.0" z="165.9" yaw="180.1"/>
            <distance value="70"/>
         </scenario>
         <scenario name="PedestrianCrossing_6" type="PedestrianCrossing">
            <trigger_point x="5829.00" y="4506.80" z="162.09" yaw="180.06"/>
         </scenario>
         <scenario name="ParkingCutIn_10" type="ParkingCutIn">
            <trigger_point x="5783.0" y="4574.7" z="161.7" yaw="90.9"/>
         </scenario>
         <scenario name="PedestrianCrossing_7" type="PedestrianCrossing">
            <trigger_point x="5781.12" y="4701.33" z="162.14" yaw="90.85"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_5" type="ParkingCrossingPedestrian">
            <trigger_point x="6778.8" y="4490.5" z="177.9" yaw="-89.5"/>
            <distance value="60"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="6591.4" y="4249.1" z="172.7" yaw="-178.8"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="6414.6" y="4242.3" z="169.8" yaw="-169.5"/>
            <distance value="100"/>
            <speed value="45"/>
         </scenario>
         <scenario name="Accident_2" type="Accident">
            <trigger_point x="6246.9" y="4147.5" z="165.9" yaw="-152.9"/>
            <distance value="70"/>
            <speed value="47"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_1" type="VehicleOpensDoorTwoWays">
            <trigger_point x="7012.2" y="4725.1" z="182.8" yaw="180.1"/>
            <distance value="70"/>
            <frequency from="40" to="80"/>
         </scenario>
         <scenario name="ParkingCutIn_12" type="ParkingCutIn">
            <trigger_point x="5779.0" y="5044.5" z="162.4" yaw="270.9"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_6" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5780.5" y="4947.4" z="162.5" yaw="270.9"/>
            <distance value="70"/>
            <frequency from="45" to="90"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="6770.9" y="5424.9" z="178.8" yaw="-89.5"/>
         </scenario>
         <scenario name="ParkingCutIn_2" type="ParkingCutIn">
            <trigger_point x="6886.0" y="5225.3" z="181.4" yaw="0.2"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_2" type="ParkingCrossingPedestrian">
            <trigger_point x="6922.6" y="4724.9" z="181.6" yaw="180.1"/>
            <distance value="60"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="6809.2" y="4724.6" z="180.1" yaw="180.1"/>
         </scenario>
         <scenario name="ParkingCutIn_5" type="ParkingCutIn">
            <trigger_point x="6905.3" y="4931.5" z="181.9" yaw="-0.1"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="7026.8" y="4974.2" z="183.6" yaw="90.9"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="6874.1" y="5077.2" z="181.5" yaw="180.6"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="6960.4" y="4263.5" z="178.7" yaw="1.2"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="6971.6" y="3972.8" z="176.1" yaw="179.7"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_7" type="ParkingCrossingPedestrian">
            <trigger_point x="6619.4" y="4098.4" z="171.6" yaw="90.5"/>
            <distance value="75"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_5" type="SignalizedJunctionRightTurn">
            <trigger_point x="6086.1" y="4440.1" z="166.1" yaw="90.6"/>
            <flow_speed value="13"/>
            <source_dist_interval from="19" to="50"/>
         </scenario>
         <scenario name="Accident_3" type="Accident">
            <trigger_point x="5593.9" y="5201.4" z="158.5" yaw="261.6"/>
            <distance value="65"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_14" type="ParkingCrossingPedestrian">
            <trigger_point x="5640.1" y="5066.7" z="159.8" yaw="0.7"/>
            <distance value="50"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_8" type="SignalizedJunctionRightTurn">
            <trigger_point x="5577.4" y="4758.1" z="158.8" yaw="180.4"/>
            <flow_speed value="12"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="6226.1" y="4510.2" z="169.2" yaw="0.1"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_8" type="VehicleOpensDoorTwoWays">
            <trigger_point x="6317.0" y="4909.0" z="172.8" yaw="90.9"/>
            <distance value="60"/>
            <frequency from="45" to="65"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="6503.5" y="4730.8" z="175.6" yaw="270.9"/>
         </scenario>
         <scenario name="ParkingCutIn_16" type="ParkingCutIn">
            <trigger_point x="6638.1" y="4557.2" z="176.4" yaw="0.1"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="7045.6" y="4222.2" z="179.6" yaw="-89.1"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="7034.3" y="4910.9" z="183.5" yaw="270.9"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="7034.4" y="4907.6" z="183.5" yaw="270.9"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="6820.6" y="4260.5" z="176.4" yaw="1.2"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="6824.5" y="4260.6" z="176.5" yaw="1.2"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="6059.8" y="4126.2" z="162.7" yaw="179.4"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="6027.2" y="4126.6" z="162.3" yaw="179.4"/>
         </scenario>
         <scenario name="HardBreakRoute_11" type="HardBreakRoute">
            <trigger_point x="5971.9" y="4359.1" z="163.3" yaw="0.0"/>
         </scenario>
      </scenarios>
   </route>
   <route id="12" town="Town13">
      <!-- Urbanization + residential -->
      <weathers>
      <!-- From a slightly cloudy day with gentle breeze, mild rain, and thick fog to a similarly clear day with a low sun and even denser fog -->
         <weather route_percentage="0"
            cloudiness="5.0" precipitation="10.0" precipitation_deposits="20.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="90.0" fog_density="45.0"/>
         <weather route_percentage="100"
            cloudiness="5.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="15.0" fog_density="70.0"/>
      </weathers>
      <waypoints>
         <position x="2536.1" y="2284.9" z="172.5"/>
         <position x="2990.8" y="2385.8" z="180.1"/>
         <position x="2905.5" y="2574.9" z="180.4"/>
         <position x="2898.5" y="2643.0" z="180.5"/>
         <position x="2959.3" y="2861.5" z="182.3"/>
         <position x="2862.8" y="2895.8" z="180.5"/>
         <position x="2848.2" y="2904.4" z="180.1"/>
         <position x="2642.0" y="3003.3" z="174.6"/>
         <position x="2654.6" y="3295.6" z="174.4"/>
         <position x="2922.3" y="3227.3" z="181.2"/>
         <position x="3152.7" y="3234.2" z="182.4"/>
         <position x="3268.1" y="3146.8" z="181.3"/>
         <position x="3412.6" y="3218.4" z="175.5"/>
         <position x="3038.1" y="3304.4" z="182.1"/>
         <position x="2971.1" y="3537.5" z="180.7"/>
         <position x="2810.6" y="3571.4" z="178.1"/>
         <position x="2767.1" y="3530.9" z="177.0"/>
         <position x="2632.7" y="3472.7" z="173.4"/>
         <position x="2797.3" y="3574.0" z="177.8"/>
         <position x="2933.2" y="3639.0" z="180.3"/>
         <position x="2959.1" y="3681.2" z="180.6"/>
         <position x="2920.1" y="3878.9" z="181.4"/>
         <position x="2880.1" y="3971.0" z="181.5"/>
         <position x="3065.0" y="4003.9" z="183.7"/>
         <position x="3170.9" y="4067.7" z="184.0"/>
         <position x="3513.3" y="4079.0" z="172.6"/>
         <position x="3373.3" y="4320.7" z="182.6"/>
         <position x="3095.6" y="4279.4" z="186.4"/>
         <position x="3057.4" y="4372.3" z="186.6"/>
         <position x="2926.2" y="4441.1" z="184.7"/>
         <position x="2640.4" y="4524.8" z="175.8"/>
         <position x="2005.5" y="5144.1" z="149.6"/>
         <position x="2119.5" y="5431.1" z="150.2"/>
         <position x="2282.4" y="5606.1" z="151.9"/>
         <position x="2632.3" y="5636.8" z="157.6"/>
         <position x="2792.9" y="5584.2" z="161.3"/>
      </waypoints>
      <scenarios>
         <scenario name="ParkingExit_1" type="ParkingExit">
            <trigger_point x="2536.1" y="2284.9" z="172.5" yaw="0.5"/>
            <front_vehicle_distance value="9"/>
            <behind_vehicle_distance value="9"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="2821.1" y="2287.4" z="177.8" yaw="0.5"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2974.7" y="2790.5" z="182.3" yaw="62.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="2691.2" y="2960.3" z="176.1" yaw="160.4"/>
            <flow_speed value="12"/>
            <source_dist_interval from="17" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="2637.1" y="3251.5" z="174.0" yaw="91.1"/>
            <flow_speed value="13"/>
            <source_dist_interval from="15" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_2" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="2764.3" y="3298.9" z="177.5" yaw="1.7"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="2940.7" y="3227.8" z="181.5" yaw="1.7"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3199.3" y="3187.2" z="182.2" yaw="270.8"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_2" type="SignalizedJunctionRightTurn">
            <trigger_point x="3398.4" y="3176.6" z="176.8" yaw="27.6"/>
            <flow_speed value="12"/>
            <source_dist_interval from="15" to="65"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3027.6" y="3304.1" z="182.1" yaw="181.7"/>
            <flow_speed value="9"/>
            <source_dist_interval from="17" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2971.5" y="3535.6" z="180.7" yaw="103.4"/>
            <flow_speed value="8"/>
            <source_dist_interval from="20" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2910.6" y="3575.7" z="179.9" yaw="0.9"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_2" type="SignalizedJunctionLeftTurn">
            <trigger_point x="2899.1" y="3912.6" z="181.4" yaw="126.7"/>
            <flow_speed value="12"/>
            <source_dist_interval from="17" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3064.4" y="4032.7" z="184.0" yaw="91.2"/>
            <flow_speed value="6"/>
            <source_dist_interval from="12" to="50"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="3270.6" y="4069.1" z="182.2" yaw="0.8"/>
            <direction value="left"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_6" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3471.8" y="4054.0" z="174.4" yaw="-13.6"/>
            <flow_speed value="6"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3105.1" y="4279.7" z="186.4" yaw="181.3"/>
            <flow_speed value="8"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_7" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3056.6" y="4409.7" z="186.6" yaw="91.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="20" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_6" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="2837.8" y="4439.9" z="182.7" yaw="180.7"/>
            <flow_speed value="11"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_8" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2810.5" y="4565.2" z="181.0" yaw="91.7"/>
            <flow_speed value="6"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_9" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2679.1" y="4588.1" z="176.5" yaw="181.4"/>
            <flow_speed value="10"/>
            <source_dist_interval from="17" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_7" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="1984.6" y="5181.5" z="149.2" yaw="123.5"/>
            <flow_speed value="13"/>
            <source_dist_interval from="18" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_10" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2282.0" y="5558.2" z="152.0" yaw="-90.4"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_11" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2590.1" y="5529.2" z="157.9" yaw="0.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="18" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_8" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="2997.7" y="5577.8" z="165.4" yaw="-16.8"/>
            <flow_speed value="12"/>
            <source_dist_interval from="13" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_12" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3058.2" y="5454.9" z="169.0" yaw="-89.3"/>
            <flow_speed value="9"/>
            <source_dist_interval from="20" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_9" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3204.9" y="5416.1" z="172.1" yaw="-5.5"/>
            <flow_speed value="8"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_10" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="3091.6" y="5414.6" z="170.5" yaw="180.3"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="45"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_13" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3054.8" y="5510.7" z="167.7" yaw="90.7"/>
            <flow_speed value="12"/>
            <source_dist_interval from="13" to="50"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="2584.8" y="2285.4" z="173.7" yaw="0.5"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="3015.2" y="2385.8" z="180.3" yaw="-0.0"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="2901.8" y="2579.2" z="180.3" yaw="131.5"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="3341.8" y="3313.3" z="177.3" yaw="126.7"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="2729.5" y="3677.0" z="176.3" yaw="1.0"/>
         </scenario>
         <scenario name="BlockedIntersection_5" type="BlockedIntersection">
            <trigger_point x="2764.7" y="3609.7" z="177.0" yaw="272.8"/>
         </scenario>
         <scenario name="BlockedIntersection_6" type="BlockedIntersection">
            <trigger_point x="3024.7" y="3974.2" z="183.2" yaw="1.3"/>
         </scenario>
         <scenario name="BlockedIntersection_7" type="BlockedIntersection">
            <trigger_point x="3422.2" y="4327.4" z="180.8" yaw="142.6"/>
         </scenario>
         <scenario name="VehicleTurningRoute_5" type="VehicleTurningRoute">
            <trigger_point x="2640.6" y="4482.5" z="176.2" yaw="270.3"/>
         </scenario>
         <scenario name="BlockedIntersection_8" type="BlockedIntersection">
            <trigger_point x="2060.5" y="5228.1" z="150.0" yaw="0.6"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="2120.6" y="5654.0" z="149.8" yaw="89.7"/>
         </scenario>s
         <scenario name="VehicleTurningRoute_6" type="VehicleTurningRoute">
            <trigger_point x="2991.2" y="5579.8" z="165.2" yaw="-17.2"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3234.8" y="5320.0" z="174.7" yaw="270.6"/>
         </scenario>
         <scenario name="VehicleTurningRoute_8" type="VehicleTurningRoute">
            <trigger_point x="3193.9" y="5267.2" z="175.5" yaw="181.3"/>
         </scenario>
         <scenario name="VehicleTurningRoute_9" type="VehicleTurningRoute">
            <trigger_point x="2834.5" y="5637.7" z="161.3" yaw="152.3"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_1" type="ParkingCrossingPedestrian">
            <trigger_point x="2671.5" y="2286.1" z="175.6" yaw="0.5"/>
            <distance value="50"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_2" type="ParkingCrossingPedestrian">
            <trigger_point x="2881.24" y="2385.84" z="179.04" yaw="-0.00"/>
            <distance value="50"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="3035.2" y="2423.6" z="180.8" yaw="130.6"/>
            <distance value="55"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="2884.3" y="2615.4" z="180.2" yaw="62.7"/>
            <distance value="66"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="2642.1" y="2999.4" z="174.6" yaw="91.1"/>
            <distance value="50"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="6"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="3000.7" y="3229.6" z="182.3" yaw="1.7"/>
            <distance value="50"/>
            <blocker_model value="static.prop.busstoplb"/>
            <crossing_angle value="4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_6" type="DynamicObjectCrossing">
            <trigger_point x="2984.5" y="3354.7" z="181.5" yaw="90.4"/>
            <distance value="55"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="6"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_7" type="DynamicObjectCrossing">
            <trigger_point x="2779.6" y="3573.7" z="177.4" yaw="0.9"/>
            <distance value="60"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_8" type="DynamicObjectCrossing">
            <trigger_point x="2887.9" y="3971.2" z="181.7" yaw="1.3"/>
            <distance value="60"/>
            <blocker_model value="static.prop.busstoplb"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_9" type="DynamicObjectCrossing">
            <trigger_point x="3291.9" y="4286.3" z="184.6" yaw="190.1"/>
            <distance value="50"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_10" type="DynamicObjectCrossing">
            <trigger_point x="3046.8" y="4442.6" z="186.4" yaw="180.7"/>
            <distance value="45"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_11" type="DynamicObjectCrossing">
            <trigger_point x="2813.7" y="4454.6" z="182.1" yaw="91.7"/>
            <distance value="50"/>
            <blocker_model value="static.prop.busstoplb"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_12" type="DynamicObjectCrossing">
            <trigger_point x="2119.0" y="5324.1" z="150.5" yaw="89.7"/>
            <distance value="50"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_13" type="DynamicObjectCrossing">
            <trigger_point x="2307.00" y="5525.60" z="152.54" yaw="0.7"/>
            <distance value="70"/>
            <blocker_model value="static.prop.container"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_14" type="DynamicObjectCrossing">
            <trigger_point x="2632.8" y="5541.5" z="158.6" yaw="90.3"/>
            <distance value="45"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_15" type="DynamicObjectCrossing">
            <trigger_point x="3234.0" y="5397.8" z="172.8" yaw="270.6"/>
            <distance value="45"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="6"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="2816.8" y="3224.1" z="179.0" yaw="1.7"/>
            <distance value="65"/>
            <frequency from="35" to="120"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="3170.66" y="3308.37" z="181.61" yaw="181.73"/>
            <distance value="65"/>
            <frequency from="50" to="115"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="2927.56" y="3573.23" z="180.16" yaw="180.88"/>
            <distance value="70"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="3077.7" y="4066.5" z="184.4" yaw="0.8"/>
            <distance value="70"/>
            <frequency from="50" to="110"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="3325.4" y="4069.7" z="180.7" yaw="-0.1"/>
            <distance value="70"/>
            <frequency from="55" to="120"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="3032.06" y="5561.50" z="166.19" yaw="165.54"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="3227.7" y="3146.0" z="182.1" yaw="1.1"/>
            <distance value="70"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="2767.1" y="3561.2" z="177.0" yaw="272.8"/>
            <distance value="70"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="2631.1" y="3551.8" z="173.3" yaw="91.1"/>
            <distance value="70"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="3506.2" y="4055.1" z="172.6" yaw="70.6"/>
            <distance value="60"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="2146.0" y="5698.7" z="150.0" yaw="1.0"/>
            <distance value="75"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="2539.7" y="2285.0" z="172.6" yaw="0.5"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="2817.0" y="5654.5" z="160.8" yaw="-29.2"/>
            <distance value="113"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="3006.2" y="2457.5" z="180.9" yaw="130.6"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="2640.1" y="3099.3" z="174.4" yaw="91.1"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="2639.21" y="3143.18" z="174.27" yaw="91.13"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="3265.3" y="3322.0" z="179.7" yaw="199.6"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="3039.5" y="3718.7" z="181.3" yaw="126.7"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="3476.2" y="4272.2" z="177.6" yaw="125.1"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="3347.4" y="4304.9" z="183.3" yaw="207.0"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="2971.6" y="4441.7" z="185.5" yaw="180.7"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="2964.9" y="4441.6" z="185.4" yaw="180.7"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="2895.4" y="3917.5" z="181.3" yaw="126.7"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="3064.6" y="4025.0" z="183.9" yaw="91.2"/>
         </scenario>
         <scenario name="VehicleTurningRoute_4" type="VehicleTurningRoute">
            <trigger_point x="3111.0" y="4279.8" z="186.4" yaw="181.3"/>
         </scenario>
         <scenario name="VehicleTurningRoute_7" type="VehicleTurningRoute">
            <trigger_point x="3208.6" y="5415.7" z="172.2" yaw="-6.1"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="2859.3" y="2300.9" z="178.3" yaw="90.2"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="2493.3" y="4451.4" z="170.7" yaw="126.7"/>
            <distance value="75"/>
            <speed value="50"/>
         </scenario>
         <scenario name="ConstructionObstacle_2" type="ConstructionObstacle">
            <trigger_point x="2363.7" y="4627.9" z="163.3" yaw="126.0"/>
            <distance value="75"/>
            <direction value="right"/>
            <speed value="55"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="2218.1" y="4828.7" z="155.8" yaw="125.6"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2990.3" y="3681.8" z="180.9" yaw="1.1"/>
            <flow_speed value="14"/>
            <source_dist_interval from="15" to="65"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_3" type="ParkingCrossingPedestrian">
            <trigger_point x="2968.36" y="2385.84" z="179.93" yaw="-0.00"/>
            <distance value="52"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="2995.7" y="2469.8" z="180.9" yaw="130.6"/>
            <distance value="80"/>
            <frequency from="45" to="95"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="2936.3" y="2716.1" z="181.5" yaw="62.7"/>
            <distance value="90"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="3156.2" y="3234.3" z="182.4" yaw="1.7"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="3420.9" y="3207.2" z="175.3" yaw="126.7"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="2796.1" y="4591.0" z="180.2" yaw="181.4"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="2792.4" y="4590.9" z="180.1" yaw="181.4"/>
         </scenario>
         <scenario name="HardBreakRoute_12" type="HardBreakRoute">
            <trigger_point x="2209.4" y="4840.9" z="155.4" yaw="125.4"/>
         </scenario>
         <scenario name="BlockedIntersection_9" type="BlockedIntersection">
            <trigger_point x="3144.9" y="5354.6" z="172.8" yaw="91.2"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="2847.2" y="2904.8" z="180.1" yaw="160.4"/>
            <distance value="112"/>
            <direction value="right"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="2656.2" y="5742.2" z="157.0" yaw="-22.4"/>
         </scenario>
         <scenario name="HardBreakRoute_13" type="HardBreakRoute">
            <trigger_point x="2677.5" y="5732.8" z="157.4" yaw="-25.1"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="2990.3" y="2850.5" z="182.8" yaw="160.4"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="2983.4" y="2852.9" z="182.7" yaw="160.4"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="2800.6" y="3255.8" z="178.5" yaw="270.4"/>
         </scenario>
         <scenario name="HardBreakRoute_10" type="HardBreakRoute">
            <trigger_point x="2640.2" y="4569.2" z="175.3" yaw="270.3"/>
         </scenario>
         <scenario name="HardBreakRoute_11" type="HardBreakRoute">
            <trigger_point x="2618.1" y="4437.1" z="175.7" yaw="180.7"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="2624.7" y="4437.2" z="176.0" yaw="180.7"/>
         </scenario>
      </scenarios>
   </route>
   <route id="13" town="Town13">
      <!-- Urbanization + highway -->
      <weathers>
      <!-- From a moderately cloudy day with moderate rain to a mostly cloudy evening with dry conditions and a gentle wind -->
         <weather route_percentage="0"
            cloudiness="40.0" precipitation="60.0" precipitation_deposits="60.0" wetness="0.0"
            wind_intensity="25.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="3.0"/>
         <weather route_percentage="100"
            cloudiness="80.0" precipitation="0.0" precipitation_deposits="50.0" wetness="20.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-45.0" fog_density="6.0"/>
      </weathers>
      <waypoints>
         <position x="249.8" y="1319.6" z="148.1"/>
         <position x="1051.9" y="1127.1" z="156.6"/>
         <position x="1156.7" y="1158.8" z="157.0"/>
         <position x="1171.88" y="1186.19" z="157.00"/>
         <position x="1211.61" y="1220.18" z="157.00"/>
         <position x="1238.82" y="1230.10" z="157.00"/>
         <position x="1271.54" y="1232.66" z="157.00"/>
         <position x="1313.3" y="1221.7" z="157.0"/>
         <position x="1332.30" y="1210.21" z="157.00"/>
         <position x="1348.13" y="1195.90" z="157.00"/>
         <position x="1360.14" y="1180.28" z="157.00"/>
         <position x="1374.4" y="1149.5" z="157.0"/>
         <position x="1732.3" y="1103.0" z="151.7"/>
         <position x="1785.1" y="1104.1" z="151.8"/>
         <position x="2010.1" y="1087.2" z="154.4"/>
         <position x="1965.7" y="1091.4" z="153.7"/>
         <position x="1444.0" y="1110.9" z="157.0"/>
         <position x="1371.5" y="1079.2" z="157.0"/>
         <position x="1363.53" y="1062.94" z="157.00"/>
         <position x="1352.28" y="1046.45" z="157.00"/>
         <position x="1339.12" y="1032.86" z="157.00"/>
         <position x="1326.02" y="1022.98" z="157.00"/>
         <position x="1308.7" y="1013.8" z="157.0"/>
         <position x="1279.57" y="1005.72" z="157.00"/>
         <position x="1249.25" y="1005.60" z="157.00"/>
         <position x="1221.62" y="1012.81" z="157.00"/>
         <position x="1203.81" y="1021.80" z="157.00"/>
         <position x="1193.1" y="1029.3" z="157.0"/>
         <position x="1166.28" y="1059.80" z="157.00"/>
         <position x="1150.9" y="1103.6" z="157.0"/>
         <position x="1149.86" y="1119.44" z="157.00"/>
         <position x="1151.16" y="1135.99" z="157.00"/>
         <position x="1162.19" y="1170.38" z="157.00"/>
         <position x="1173.9" y="1189.1" z="157.0"/>
         <position x="1253.4" y="1401.8" z="148.7"/>
         <position x="1255.9" y="1465.1" z="148.7"/>
         <position x="1258.9" y="1489.8" z="148.7"/>
         <position x="1256.4" y="1632.2" z="148.7"/>
         <position x="1259.6" y="1651.5" z="148.7"/>
         <position x="1257.1" y="1791.9" z="148.6"/>
         <position x="1260.3" y="1810.8" z="148.6"/>
         <position x="2043.4" y="3620.8" z="169.7"/>
         <position x="2053.7" y="3642.3" z="170.0"/>
         <position x="2063.4" y="3663.3" z="170.3"/>
         <position x="2342.3" y="4240.1" z="180.4"/>
         <position x="2355.7" y="4274.9" z="180.8"/>
         <position x="2403.2" y="5140.1" z="172.8"/>
         <position x="2398.9" y="5167.8" z="172.5"/>
         <position x="2368.4" y="6122.1" z="166.1"/>
         <position x="1999.9" y="6451.8" z="153.5"/>
         <position x="1962.3" y="6455.2" z="152.5"/>
         <position x="1944.1" y="6458.6" z="152.0"/>
         <position x="1383.7" y="6432.5" z="148.3"/>
         <position x="1361.7" y="6425.0" z="148.4"/>
         <position x="1348.3" y="6419.0" z="148.6"/>
         <position x="962.5" y="6296.4" z="156.5"/>
         <position x="210.2" y="6088.2" z="164.3"/>
         <position x="66.1" y="6087.9" z="162.5"/>
         <position x="-134.3" y="6084.4" z="159.3"/>
         <position x="-528.6" y="5921.3" z="151.9"/>
         <position x="-671.8" y="5892.0" z="149.7"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="256.8" y="1204.0" z="148.2" yaw="-86.5"/>
            <flow_speed value="8"/>
            <source_dist_interval from="13" to="55"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="1978.3" y="1094.3" z="153.9" yaw="-1.2"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2174.7" y="949.0" z="157.2" yaw="-14.2"/>
            <flow_speed value="7"/>
            <source_dist_interval from="10" to="70"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2232.7" y="1028.2" z="158.1" yaw="90.5"/>
            <flow_speed value="9"/>
            <source_dist_interval from="18" to="56"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="2111.0" y="1083.5" z="155.9" yaw="177.8"/>
         </scenario>
         <scenario name="PriorityAtJunction_1" type="PriorityAtJunction">
            <trigger_point x="2085.3" y="1084.5" z="155.5" yaw="177.8"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="2092.51" y="962.68" z="155.84" yaw="-4.70"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="2040.8" y="1004.4" z="154.9" yaw="270.5"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="2233.5" y="947.5" z="158.3" yaw="90.5"/>
         </scenario>
         <scenario name="MergerIntoSlowTraffic_1" type="MergerIntoSlowTraffic">
            <trigger_point x="1129.0" y="1128.4" z="157.0" yaw="20.9"/>
            <start_actor_flow x="1150.7" y="1104.8" z="157.0"/>
            <end_actor_flow x="1330.9" y="1211.2" z="157.0"/>
            <flow_speed value="10"/>
            <source_dist_interval from="25" to="55"/>
         </scenario>
         <scenario name="MergerIntoSlowTrafficV2_1" type="MergerIntoSlowTrafficV2">
            <trigger_point x="1317.35" y="1219.67" z="157.00" yaw="-27.88"/>
            <start_actor_flow x="1339.1" y="1245.6" z="156.6"/>
            <end_actor_flow x="1402.6" y="1118.2" z="157.0"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="30"/>
         </scenario>
         <scenario name="MergerIntoSlowTrafficV2_2" type="MergerIntoSlowTrafficV2">
            <trigger_point x="1218.7" y="1014.0" z="157.0" yaw="156.6"/>
            <start_actor_flow x="1194.1" y="988.8" z="155.9"/>
            <end_actor_flow x="1208.0" y="1270.9" z="155.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="17" to="38"/>
         </scenario>
         <scenario name="EnterActorFlow_1" type="EnterActorFlow">
            <trigger_point x="1195.3" y="1233.4" z="157.0" yaw="70.6"/>
            <start_actor_flow x="1260.0" y="1258.4" z="148.8"/>
            <end_actor_flow x="1256.6" y="1620.2" z="148.7"/>
            <flow_speed value="17"/>
            <source_dist_interval from="20" to="75"/>
         </scenario>
         <scenario name="MergerIntoSlowTraffic_2" type="MergerIntoSlowTraffic">
            <trigger_point x="-377.2" y="5990.9" z="154.6" yaw="-144.6"/>
            <start_actor_flow x="-373.2" y="5946.7" z="154.6"/>
            <end_actor_flow x="-687.3" y="5892.9" z="149.4"/>
            <flow_speed value="10"/>
            <source_dist_interval from="20" to="35"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_1" type="YieldToEmergencyVehicle">
            <trigger_point x="1257.7" y="1955.9" z="148.6" yaw="91.0"/>
            <distance value="130"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_2" type="YieldToEmergencyVehicle">
            <trigger_point x="1483.3" y="2895.1" z="164.4" yaw="60.9"/>
            <distance value="150"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="2050.8" y="964.4" z="155.2" yaw="-0.3"/>
            <distance value="50"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="322.0" y="1138.2" z="148.1" yaw="3.6"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="1723.0" y="1100.1" z="151.9" yaw="177.7"/>
            <distance value="75"/>
            <frequency from="40" to="100"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="1870.8" y="3393.1" z="167.0" yaw="49.9"/>
            <distance value="80"/>
            <speed value="55"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="2160.4" y="3822.9" z="173.0" yaw="60.6"/>
            <distance value="80"/>
            <direction value="right"/>
            <speed value="60"/>
         </scenario>
         <scenario name="Accident_2" type="Accident">
            <trigger_point x="2381.8" y="5703.1" z="168.1" yaw="91.8"/>
            <distance value="75"/>
         </scenario>
         <scenario name="Accident_3" type="Accident">
            <trigger_point x="1949.4" y="6458.6" z="152.1" yaw="-179.8"/>
            <distance value="70"/>
            <speed value="45"/>
         </scenario>
         <scenario name="ConstructionObstacle_3" type="ConstructionObstacle">
            <trigger_point x="1086.6" y="6346.8" z="153.1" yaw="-160.1"/>
            <distance value="70"/>
            <speed value="50"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="1258.1" y="1734.3" z="148.6" yaw="91.0"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="1890.4" y="3416.6" z="167.2" yaw="50.5"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="2074.3" y="3680.1" z="170.5" yaw="57.2"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="2226.4" y="3947.4" z="175.5" yaw="63.6"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="2232.7" y="3960.2" z="175.7" yaw="63.8"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="2360.2" y="4291.7" z="181.0" yaw="75.4"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="2415.0" y="4771.7" z="178.7" yaw="91.5"/>
         </scenario>
         <scenario name="HardBreakRoute_13" type="HardBreakRoute">
            <trigger_point x="1500.3" y="6448.4" z="147.9" yaw="-174.1"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="1107.1" y="6354.1" z="152.5" yaw="-160.8"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="678.1" y="6171.0" z="163.7" yaw="-161.0"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="2348.0" y="4247.1" z="180.5" yaw="73.9"/>
            <distance value="100"/>
         </scenario>
         <scenario name="StaticCutIn_3" type="StaticCutIn">
            <trigger_point x="2379.6" y="4374.6" z="181.5" yaw="78.3"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_4" type="StaticCutIn">
            <trigger_point x="2410.6" y="4593.9" z="181.0" yaw="85.6"/>
            <distance value="110"/>
            <direction value="left"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="1984.5" y="1094.2" z="154.0" yaw="-1.2"/>
         </scenario>
         <scenario name="ParkedObstacle_1" type="ParkedObstacle">
            <trigger_point x="2215.1" y="1079.5" z="157.7" yaw="177.8"/>
            <distance value="55"/>
            <speed value="45"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="609.9" y="1137.2" z="147.2" yaw="-1.1"/>
            <distance value="55"/>
            <frequency value="75"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="13"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="941.8" y="1130.1" z="153.3" yaw="-1.4"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="1467.8" y="1113.3" z="156.9" yaw="357.8"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="1804.8" y="1100.1" z="152.0" yaw="-2.3"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="2162.1" y="952.0" z="157.0" yaw="-12.7"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="1258.4" y="1717.5" z="148.6" yaw="91.0"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="1994.9" y="3551.7" z="168.7" yaw="54.0"/>
         </scenario>
         <scenario name="ParkedObstacle_2" type="ParkedObstacle">
            <trigger_point x="2295.0" y="4101.4" z="178.4" yaw="68.7"/>
            <distance value="60"/>
            <speed value="60"/>
         </scenario>
         <scenario name="ConstructionObstacle_2" type="ConstructionObstacle">
            <trigger_point x="2392.6" y="5365.5" z="170.3" yaw="91.8"/>
            <distance value="90"/>
            <speed value="60"/>
         </scenario>
         <scenario name="HardBreakRoute_11" type="HardBreakRoute">
            <trigger_point x="2372.1" y="6008.3" z="166.6" yaw="91.8"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="2363.2" y="6225.5" z="165.7" yaw="99.0"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="378.7" y="6100.8" z="165.5" yaw="-172.6"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="1490.7" y="1112.4" z="156.5" yaw="357.8"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="1991.3" y="1090.8" z="154.1" yaw="178.8"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="2064.9" y="3665.7" z="170.3" yaw="56.8"/>
         </scenario>
         <scenario name="HardBreakRoute_10" type="HardBreakRoute">
            <trigger_point x="2411.5" y="4882.5" z="176.8" yaw="91.8"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="2399.2" y="5157.2" z="172.6" yaw="91.8"/>
         </scenario>
         <scenario name="HighwayCutIn_1" type="HighwayCutIn">
            <trigger_point x="340.4" y="6096.3" z="165.3" yaw="-174.0"/>
            <other_actor_location x="337.3" y="6062.3" z="165.2"/>
         </scenario>
         <scenario name="HardBreakRoute_12" type="HardBreakRoute">
            <trigger_point x="2302.1" y="6353.0" z="162.5" yaw="132.3"/>
         </scenario>
         <scenario name="Accident_4" type="Accident">
            <trigger_point x="613.3" y="6150.3" z="164.6" yaw="-163.6"/>
            <distance value="90"/>
         </scenario>
      </scenarios>
   </route>
   <route id="14" town="Town13">
      <!-- Interurban + highway -->
      <weathers>
      <!-- From a moderately cloudy day with a gentle breeze and light fog to a similarly cloudy night with light rain -->
         <weather route_percentage="0"
            cloudiness="40.0" precipitation="0.0" precipitation_deposits="50.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="15.0" fog_density="2.0"/>
         <weather route_percentage="100"
            cloudiness="40.0" precipitation="30.0" precipitation_deposits="50.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-90.0" fog_density="2.0"/>
      </weathers>
      <waypoints>
         <position x="1835.7" y="5826.3" z="147.7"/>
         <position x="1806.8" y="6018.1" z="147.8"/>
         <position x="1556.8" y="6012.5" z="147.8"/>
         <position x="1523.0" y="6015.0" z="147.9"/>
         <position x="1116.1" y="6007.9" z="154.9"/>
         <position x="1088.6" y="5982.8" z="155.8"/>
         <position x="1111.6" y="4688.4" z="165.6"/>
         <position x="1110.1" y="4582.2" z="166.1"/>
         <position x="1064.4" y="4556.6" z="167.7"/>
         <position x="379.7" y="4864.5" z="163.8"/>
         <position x="62.3" y="4941.8" z="155.8"/>
         <position x="-371.9" y="5127.1" z="151.1"/>
         <position x="-785.5" y="4868.1" z="146.1"/>
         <position x="-951.0" y="4792.1" z="146.2"/>
         <position x="-990.0" y="4792.8" z="146.4"/>
         <position x="-1700.0" y="5411.8" z="153.9"/>
         <position x="-2096.3" y="5819.7" z="153.3"/>
         <position x="-2176.1" y="5901.0" z="153.4"/>
         <position x="-3024.5" y="4696.3" z="179.1"/>
         <position x="-2388.5" y="4626.9" z="174.5"/>
         <position x="-1715.7" y="5420.5" z="154.1"/>
         <position x="-1001.2" y="4764.1" z="146.4"/>
         <position x="-1417.71" y="4416.02" z="151.38"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="1833.9" y="5899.8" z="147.8" yaw="91.3"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3703.6" y="5213.6" z="165.4" yaw="270.8"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3699.1" y="4901.7" z="167.8" yaw="270.8"/>
            <flow_speed value="6"/>
            <source_dist_interval from="10" to="60"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3437.9" y="4764.6" z="173.0" yaw="-9.4"/>
            <direction value="left"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-3593.0" y="5895.3" z="168.8" yaw="-179.4"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="-2534.2" y="4615.2" z="176.5" yaw="-9.4"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-1783.0" y="5334.7" z="157.2" yaw="62.5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-3713.3" y="5875.2" z="170.5" yaw="270.8"/>
            <distance value="60"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="-3711.0" y="5720.5" z="168.8" yaw="270.8"/>
            <distance value="65"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="1807.17" y="6018.08" z="147.80" yaw="-178.72"/>
            <distance value="60"/>
            <speed value="45"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_5" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3186.8" y="4723.1" z="177.2" yaw="-9.4"/>
            <distance value="70"/>
            <frequency from="40" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_3" type="ParkedObstacleTwoWays">
            <trigger_point x="-3001.4" y="4692.5" z="179.2" yaw="-9.4"/>
            <distance value="65"/>
            <frequency from="50" to="100"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="-2346.1" y="4743.4" z="173.7" yaw="64.5"/>
            <distance value="70"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="-2148.5" y="4983.8" z="169.8" yaw="36.7"/>
            <distance value="80"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_6" type="InvadingTurn">
            <trigger_point x="-1644.1" y="5397.5" z="153.0" yaw="-17.8"/>
            <distance value="90"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_4" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1075.98" y="4728.33" z="146.95" yaw="205.57"/>
            <distance value="88"/>
            <frequency value="40"/>
            <bicycle_drive_distance value="95"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="AccidentTwoWays_5" type="AccidentTwoWays">
            <trigger_point x="-1210.0" y="5188.7" z="147.4" yaw="-47.5"/>
            <distance value="70"/>
            <frequency from="40" to="95"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="1426.2" y="6012.8" z="148.8" yaw="-178.7"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-3709.2" y="5594.9" z="167.5" yaw="270.8"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-3706.8" y="5429.4" z="166.1" yaw="270.8"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-3706.7" y="5422.2" z="166.1" yaw="270.8"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="-3655.9" y="4799.2" z="169.9" yaw="-7.6"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="-3660.8" y="4799.8" z="169.8" yaw="-7.5"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="-2790.9" y="4657.6" z="179.3" yaw="-9.4"/>
         </scenario>
         <scenario name="InterurbanAdvancedActorFlow_1" type="InterurbanAdvancedActorFlow">
            <trigger_point x="-1078.5" y="4972.1" z="146.8" yaw="-62.7"/>
            <start_actor_flow x="-1109.0" y="4716.4" z="147.2"/>
            <end_actor_flow x="-861.5" y="4835.0" z="146.1"/>
            <flow_speed value="16"/>
            <source_dist_interval from="17" to="65"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="1300.7" y="6010.9" z="150.6" yaw="180.6"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-3712.8" y="5841.1" z="170.2" yaw="270.8"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_4" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3705.2" y="5320.6" z="165.6" yaw="270.8"/>
            <distance value="80"/>
            <frequency from="20" to="70"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-3701.3" y="5054.4" z="166.1" yaw="270.8"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="1122.5" y="6008.0" z="154.7" yaw="-179.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-286.9" y="5116.4" z="152.2" yaw="141.2"/>
            <flow_speed value="12"/>
            <source_dist_interval from="10" to="65"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-2097.3" y="5831.1" z="153.2" yaw="94.2"/>
            <flow_speed value="15"/>
            <source_dist_interval from="13" to="80"/>
         </scenario>
         <scenario name="InterurbanActorFlow_1" type="InterurbanActorFlow">
            <trigger_point x="1111.5" y="4697.0" z="165.6" yaw="271.1"/>
            <start_actor_flow x="1110.6" y="4464.2" z="166.4"/>
            <end_actor_flow x="1107.9" y="4703.1" z="165.5"/>
            <flow_speed value="17"/>
            <source_dist_interval from="25" to="95"/>
         </scenario>
         <scenario name="InterurbanActorFlow_2" type="InterurbanActorFlow">
            <trigger_point x="-861.0" y="4831.3" z="146.1" yaw="205.8"/>
            <start_actor_flow x="-1085.2" y="4727.8" z="147.0"/>
            <end_actor_flow x="-852.2" y="4839.5" z="146.1"/>
            <flow_speed value="18"/>
            <source_dist_interval from="30" to="100"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="1089.7" y="5914.5" z="156.1" yaw="270.9"/>
            <distance value="80"/>
            <frequency from="35" to="95"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="1093.3" y="5692.6" z="157.1" yaw="270.9"/>
            <distance value="88"/>
            <frequency from="25" to="89"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="1100.9" y="5270.1" z="160.3" yaw="271.1"/>
            <distance value="70"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="13"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="786.1" y="4569.2" z="173.2" yaw="163.5"/>
            <distance value="120"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="206.8" y="4909.4" z="158.9" yaw="167.6"/>
            <distance value="70"/>
            <frequency from="28" to="100"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-66.0" y="4984.9" z="153.9" yaw="157.2"/>
            <distance value="70"/>
            <frequency from="25" to="110"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-375.1" y="5124.0" z="151.0" yaw="224.3"/>
            <distance value="75"/>
            <frequency value="77"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="-577.2" y="4971.9" z="147.6" yaw="209.7"/>
            <distance value="75"/>
            <frequency from="20" to="95"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_3" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1017.2" y="4845.6" z="146.5" yaw="117.3"/>
            <distance value="75"/>
            <frequency value="88"/>
            <bicycle_drive_distance value="89"/>
            <bicycle_speed value="9"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-1209.5" y="5182.9" z="147.3" yaw="132.0"/>
            <distance value="110"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="AccidentTwoWays_4" type="AccidentTwoWays">
            <trigger_point x="-1474.0" y="5339.0" z="150.2" yaw="162.2"/>
            <distance value="76"/>
            <frequency from="25" to="98"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-1897.5" y="5492.1" z="156.0" yaw="146.8"/>
            <distance value="100"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="-2143.2" y="5897.8" z="153.1" yaw="173.3"/>
            <distance value="75"/>
            <frequency from="10" to="110"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-2606.9" y="5901.9" z="157.6" yaw="-179.7"/>
            <distance value="80"/>
            <frequency from="35" to="100"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="-2941.7" y="5900.0" z="161.2" yaw="-179.7"/>
            <distance value="75"/>
            <frequency from="25" to="99"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="443.9" y="4833.6" z="165.8" yaw="150.3"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="-1308.74" y="4566.17" z="149.97" yaw="226.34"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-1154.5" y="4688.1" z="147.7" yaw="210.3"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="1088.7" y="5974.9" z="155.8" yaw="270.9"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="1050.2" y="4556.4" z="168.2" yaw="180.9"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="247.7" y="4900.4" z="160.0" yaw="167.6"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-1203.7" y="5176.4" z="147.3" yaw="131.0"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-1777.8" y="5436.9" z="155.0" yaw="162.2"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-2924.2" y="5900.1" z="161.0" yaw="-179.7"/>
         </scenario>
      </scenarios>
   </route>
   <route id="15" town="Town13">
      <!-- Rural + highway -->
      <weathers>
      <!-- From heavy rain with overcast skies and strong winds to mostly cloudy with low-hanging fog and a gentle breeze -->
         <weather route_percentage="0"
            cloudiness="40.0" precipitation="100.0" precipitation_deposits="90.0" wetness="0.0"
            wind_intensity="100.0" sun_azimuth_angle="-1.0" sun_altitude_angle="0.0" fog_density="7.0"/>
         <weather route_percentage="100"
            cloudiness="90.0" precipitation="40.0" precipitation_deposits="60.0" wetness="0.0"
            wind_intensity="60.0" sun_azimuth_angle="-1.0" sun_altitude_angle="90.0" fog_density="35.0"/>
      </weathers>
      <waypoints>
         <position x="-1720.9" y="-781.7" z="153.8"/>
         <position x="-2473.2" y="-712.4" z="165.2"/>
         <position x="-2152.0" y="-561.8" z="162.7"/>
         <position x="-2638.4" y="-1009.2" z="162.1"/>
         <position x="-4174.7" y="-1707.4" z="170.8"/>
         <position x="-3804.0" y="-2803.3" z="163.9"/>
         <position x="-3686.7" y="-2850.8" z="161.8"/>
         <position x="-3555.0" y="-2854.8" z="160.3"/>
         <position x="-3432.0" y="-2858.3" z="159.1"/>
         <position x="-2426.2" y="-2737.1" z="154.5"/>
         <position x="-2005.3" y="-2531.2" z="154.2"/>
         <position x="-1764.1" y="-2361.2" z="155.3"/>
         <position x="-1652.1" y="-2269.9" z="155.9"/>
         <position x="-1489.9" y="-2137.6" z="156.9"/>
         <position x="-1387.5" y="-2047.7" z="157.7"/>
         <position x="-618.9" y="-1544.6" z="162.6"/>
         <position x="-464.5" y="-1405.8" z="162.0"/>
         <position x="-154.8" y="-1473.0" z="168.0"/>
         <position x="992.3" y="-2310.6" z="154.6"/>
         <position x="899.7" y="-2439.1" z="155.5"/>
         <position x="719.1" y="-2549.2" z="157.3"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-1942.8" y="-731.2" z="157.0" yaw="89.9"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-2052.5" y="-574.8" z="160.7" yaw="89.4"/>
            <flow_speed value="12"/>
            <source_dist_interval from="17" to="60"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="-2101.5" y="-511.1" z="162.4" yaw="179.9"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-2269.9" y="-511.4" z="165.5" yaw="-179.8"/>
            <flow_speed value="9"/>
            <source_dist_interval from="12" to="48"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-2315.9" y="-717.2" z="163.0" yaw="-90.2"/>
         </scenario>
         <scenario name="InterurbanAdvancedActorFlow_1" type="InterurbanAdvancedActorFlow">
            <trigger_point x="-2151.7" y="-894.2" z="158.2" yaw="270.1"/>
            <start_actor_flow x="-2294.9" y="-1003.0" z="158.6"/>
            <end_actor_flow x="-1974.5" y="-1000.4" z="154.9"/>
            <flow_speed value="18"/>
            <source_dist_interval from="25" to="75"/>
         </scenario>
         <scenario name="EnterActorFlow_1" type="EnterActorFlow">
            <trigger_point x="-3808.4" y="-2796.7" z="164.1" yaw="301.3"/>
            <start_actor_flow x="-3820.2" y="-2851.2" z="163.8"/>
            <end_actor_flow x="-3472.5" y="-2851.3" z="159.5"/>
            <flow_speed value="18"/>
            <source_dist_interval from="15" to="70"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_1" type="YieldToEmergencyVehicle">
            <trigger_point x="-3212.1" y="-2858.4" z="157.6" yaw="-0.0"/>
            <distance value="150"/>
         </scenario>
         <scenario name="HighwayCutIn_1" type="HighwayCutIn">
            <trigger_point x="-1834.1" y="-2417.0" z="154.9" yaw="37.4"/>
            <other_actor_location x="-1804.1" y="-2452.4" z="155.4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-2452.3" y="-509.4" z="168.1" yaw="0.2"/>
            <distance value="60"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="-2298.4" y="-508.8" z="165.9" yaw="0.2"/>
            <distance value="60"/>
            <blocker_model value="static.prop.haybalelb"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-2315.2" y="-523.1" z="166.0" yaw="-90.2"/>
            <distance value="60"/>
            <frequency from="19" to="60"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="-2341.6" y="-778.2" z="162.4" yaw="179.6"/>
            <distance value="130"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="-2524.8" y="-1008.3" z="161.1" yaw="180.5"/>
            <distance value="80"/>
            <frequency from="10" to="55"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-2908.1" y="-1011.3" z="163.6" yaw="180.5"/>
            <distance value="90"/>
            <frequency from="15" to="50"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-3127.4" y="-1013.1" z="163.8" yaw="180.5"/>
            <distance value="80"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="120"/>
            <bicycle_speed value="13"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-3571.4" y="-1016.6" z="163.3" yaw="180.5"/>
            <distance value="80"/>
            <frequency value="75"/>
            <bicycle_drive_distance value="120"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_3" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-4272.8" y="-1172.7" z="164.8" yaw="269.6"/>
            <distance value="100"/>
            <frequency value="85"/>
            <bicycle_drive_distance value="95"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-3996.9" y="-1899.4" z="170.5" yaw="313.2"/>
            <distance value="170"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="-2152.1" y="-528.0" z="163.2" yaw="270.1"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="-2152.0" y="-561.8" z="162.7" yaw="270.1"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-2366.6" y="-1007.0" z="159.4" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-3656.3" y="-1017.3" z="163.4" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="-3508.7" y="-2858.3" z="159.8" yaw="-0.0"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-3450.69" y="-2858.31" z="159.28" yaw="-0.02"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-1949.1" y="-2499.2" z="154.3" yaw="33.7"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="-1600.8" y="-2228.0" z="156.3" yaw="39.2"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4150.8" y="-1021.1" z="164.6" yaw="180.4"/>
            <flow_speed value="12"/>
            <source_dist_interval from="13" to="55"/>
         </scenario>
         <scenario name="HighwayExit_1" type="HighwayExit">
            <trigger_point x="-642.5" y="-1559.6" z="162.7" yaw="32.5"/>
            <start_actor_flow x="-645.1" y="-1557.3" z="162.7"/>
            <end_actor_flow x="-492.0" y="-1443.0" z="160.6"/>
            <flow_speed value="13"/>
            <source_dist_interval from="10" to="78"/>
         </scenario>
         <scenario name="MergerIntoSlowTraffic_1" type="MergerIntoSlowTraffic">
            <trigger_point x="-426.5" y="-1367.2" z="165.6" yaw="44.0"/>
            <start_actor_flow x="-363.5" y="-1348.9" z="168.0"/>
            <end_actor_flow x="-314.4" y="-1230.5" z="168.0"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="48"/>
         </scenario>
         <scenario name="MergerIntoSlowTrafficV2_1" type="MergerIntoSlowTrafficV2">
            <trigger_point x="-149.4" y="-1297.7" z="168.0" yaw="-74.8"/>
            <start_actor_flow x="-113.1" y="-1300.8" z="167.8"/>
            <end_actor_flow x="-175.4" y="-1430.1" z="168.0"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="35"/>
         </scenario>
         <scenario name="InterurbanAdvancedActorFlow_2" type="InterurbanAdvancedActorFlow">
            <trigger_point x="969.8" y="-2271.7" z="154.6" yaw="-59.6"/>
            <start_actor_flow x="901.6" y="-2433.8" z="155.5"/>
            <end_actor_flow x="1117.6" y="-2304.3" z="154.0"/>
            <flow_speed value="18"/>
            <source_dist_interval from="23" to="88"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_2" type="YieldToEmergencyVehicle">
            <trigger_point x="-1461.0" y="-2105.6" z="157.2" yaw="38.4"/>
            <distance value="140"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="-1168.1" y="-1894.6" z="160.0" yaw="32.5"/>
            <distance value="80"/>
            <speed value="60"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="-973.8" y="-1770.7" z="162.1" yaw="32.5"/>
            <distance value="88"/>
            <speed value="57"/>
         </scenario>
         <scenario name="ParkedObstacle_1" type="ParkedObstacle">
            <trigger_point x="-812.8" y="-1668.1" z="163.0" yaw="32.5"/>
            <distance value="70"/>
            <speed value="49"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="20.6" y="-1718.5" z="168.8" yaw="-46.6"/>
            <distance value="77"/>
            <frequency from="10" to="99"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="134.3" y="-1818.1" z="170.4" yaw="-35.8"/>
            <distance value="100"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_4" type="HazardAtSideLaneTwoWays">
            <trigger_point x="263.3" y="-1893.7" z="169.9" yaw="-25.0"/>
            <distance value="75"/>
            <frequency value="82"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="InvadingTurn_6" type="InvadingTurn">
            <trigger_point x="567.3" y="-1974.9" z="162.2" yaw="-13.2"/>
            <distance value="100"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="782.5" y="-2070.3" z="156.8" yaw="-34.6"/>
            <distance value="70"/>
            <frequency from="30" to="105"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="-146.6" y="-1485.5" z="168.0" yaw="-56.8"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="-982.9" y="-1776.5" z="162.0" yaw="32.5"/>
         </scenario>
         <scenario name="ControlLoss_14" type="ControlLoss">
            <trigger_point x="-157.0" y="-1469.6" z="168.0" yaw="-56.8"/>
         </scenario>
         <scenario name="ControlLoss_15" type="ControlLoss">
            <trigger_point x="-70.4" y="-1602.2" z="168.3" yaw="-56.8"/>
         </scenario>
         <scenario name="ControlLoss_16" type="ControlLoss">
            <trigger_point x="955.6" y="-2404.9" z="155.1" yaw="211.4"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="-2367.6" y="-2711.8" z="154.3" yaw="21.1"/>
            <distance value="100"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="-1896.1" y="-781.9" z="155.9" yaw="180.1"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-2004.5" y="-680.0" z="158.6" yaw="179.7"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-2474.1" y="-580.2" z="167.3" yaw="89.8"/>
            <flow_speed value="13"/>
            <source_dist_interval from="10" to="88"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="-1738.7" y="-781.7" z="154.0" yaw="180.1"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="-2179.8" y="-511.1" z="163.9" yaw="-179.8"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="-2443.4" y="-509.3" z="168.0" yaw="0.2"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="-2224.9" y="-508.5" z="164.7" yaw="0.2"/>
            <flow_speed value="12"/>
            <source_dist_interval from="17" to="66"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="-2151.9" y="-698.9" z="160.8" yaw="270.1"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-4272.3" y="-1416.2" z="166.9" yaw="274.3"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="-4271.1" y="-1430.2" z="167.1" yaw="275.6"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-4256.9" y="-1514.6" z="168.3" yaw="283.5"/>
            <distance value="95"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="-4189.8" y="-1682.6" z="170.6" yaw="300.1"/>
            <distance value="85"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-3883.3" y="-2066.7" z="168.7" yaw="295.1"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-3872.5" y="-2091.1" z="168.4" yaw="292.7"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="-3842.9" y="-2179.3" z="167.6" yaw="284.4"/>
            <distance value="95"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3822.8" y="-2348.9" z="166.6" yaw="270.3"/>
            <distance value="90"/>
            <frequency from="45" to="120"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-3821.9" y="-2514.3" z="165.9" yaw="270.3"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-3821.3" y="-2614.0" z="165.3" yaw="270.3"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="-2138.9" y="-2607.9" z="154.0" yaw="27.8"/>
         </scenario>
      </scenarios>
   </route>
   <route id="16" town="Town13">
      <!-- Rural + interurban -->
      <weathers>
      <!-- From dense fog with a low-hanging mist to heavy rain and fog with overcast skies -->
         <weather route_percentage="0"
            cloudiness="5.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-90.0" fog_density="25.0" />
         <weather route_percentage="100"
            cloudiness="80.0" precipitation="80.0" precipitation_deposits="80.0" wetness="80.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-90.0" fog_density="60.0"/>
      </weathers>
      <waypoints>
         <position x="-1589.0" y="-10.5" z="156.8"/>
         <position x="-1506.8" y="-143.8" z="155.0"/>
         <position x="-1616.9" y="127.4" z="157.7"/>
         <position x="-1270.1" y="205.1" z="153.2"/>
         <position x="-1188.3" y="383.9" z="152.4"/>
         <position x="-1092.5" y="461.5" z="151.5"/>
         <position x="-1166.4" y="545.9" z="151.9"/>
         <position x="-1340.9" y="517.1" z="153.7"/>
         <position x="-1402.1" y="543.2" z="154.4"/>
         <position x="-1438.9" y="630.5" z="154.6"/>
         <position x="-1422.7" y="668.8" z="154.3"/>
         <position x="-1226.6" y="712.0" z="151.8"/>
         <position x="-1112.6" y="765.5" z="150.7"/>
         <position x="-1088.9" y="554.4" z="151.3"/>
         <position x="-784.6" y="644.3" z="150.1"/>
         <position x="-855.9" y="1189.5" z="148.4"/>
         <position x="-1613.1" y="1946.2" z="150.1"/>
         <position x="-814.8" y="2146.2" z="146.5"/>
         <position x="-530.3" y="1769.0" z="147.6"/>
         <position x="-687.0" y="1634.4" z="147.6"/>
         <position x="-1147.4" y="2084.6" z="147.1"/>
         <position x="-1033.3" y="2275.8" z="146.6"/>
         <position x="-1427.7" y="2628.9" z="148.0"/>
         <position x="-1503.9" y="2733.8" z="148.1"/>
         <position x="-1625.2" y="2834.0" z="148.6"/>
         <position x="-2284.2" y="2962.1" z="153.6"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1493.8" y="-10.5" z="155.4" yaw="0.0"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-1331.0" y="127.7" z="153.7" yaw="0.0"/>
            <flow_speed value="14"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1724.9" y="1942.3" z="151.4" yaw="185.3"/>
            <flow_speed value="11"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-1453.1" y="-111.6" z="154.5" yaw="269.2"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="-1526.4" y="127.5" z="156.3" yaw="0.0"/>
            <direction value="left"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="-457.7" y="863.3" z="149.8" yaw="32.2"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="-874.2" y="1207.8" z="148.3" yaw="132.5"/>
         </scenario>
         <scenario name="VehicleTurningRoute_4" type="VehicleTurningRoute">
            <trigger_point x="-1031.2" y="2272.4" z="146.6" yaw="-107.3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-1580.6" y="-10.5" z="156.6" yaw="0.0"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="-1606.2" y="127.4" z="157.6" yaw="0.0"/>
            <distance value="113"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="-1423.7" y="127.6" z="154.8" yaw="0.0"/>
            <distance value="105"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="-1523.0" y="-143.7" z="155.2" yaw="179.8"/>
            <distance value="120"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="-1274.1" y="141.5" z="153.2" yaw="84.0"/>
            <distance value="70"/>
            <frequency from="25" to="100"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-1226.5" y="695.3" z="151.8" yaw="90.1"/>
            <distance value="70"/>
            <frequency from="25" to="75"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="-1090.2" y="554.3" z="151.3" yaw="5.7"/>
            <distance value="75"/>
            <frequency from="20" to="88"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="-836.0" y="620.0" z="150.2" yaw="23.3"/>
            <distance value="90"/>
            <frequency from="19" to="90"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-420.6" y="937.2" z="149.7" yaw="125.1"/>
            <distance value="80"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="AccidentTwoWays_4" type="AccidentTwoWays">
            <trigger_point x="-743.0" y="1123.8" z="148.8" yaw="162.2"/>
            <distance value="70"/>
            <frequency from="15" to="85"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-950.6" y="1241.2" z="148.3" yaw="192.8"/>
            <distance value="70"/>
            <frequency value="75"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="11"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-1194.8" y="1185.8" z="149.3" yaw="192.8"/>
            <distance value="180"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="-1490.4" y="1607.5" z="149.8" yaw="62.4"/>
            <distance value="180"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="-1799.4" y="2101.0" z="151.8" yaw="95.2"/>
            <distance value="150"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="AccidentTwoWays_5" type="AccidentTwoWays">
            <trigger_point x="-1702.4" y="2450.0" z="150.3" yaw="53.0"/>
            <distance value="75"/>
            <frequency from="25" to="88"/>
         </scenario>
         <scenario name="InvadingTurn_6" type="InvadingTurn">
            <trigger_point x="-1420.7" y="2633.7" z="147.9" yaw="13.2"/>
            <distance value="170"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_3" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1110.6" y="2569.7" z="146.4" yaw="-42.9"/>
            <distance value="65"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="9"/>
         </scenario>
         <scenario name="InvadingTurn_7" type="InvadingTurn">
            <trigger_point x="-1034.2" y="2206.1" z="146.6" yaw="-33.2"/>
            <distance value="180"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_8" type="InvadingTurn">
            <trigger_point x="-667.0" y="2269.7" z="146.4" yaw="65.9"/>
            <distance value="230"/>
            <offset value="0.2"/>
         </scenario>
         <scenario name="InvadingTurn_9" type="InvadingTurn">
            <trigger_point x="-288.5" y="2316.9" z="146.6" yaw="-86.2"/>
            <distance value="100"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="-1452.3" y="-31.1" z="154.8" yaw="270.3"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="-1649.4" y="0.6" z="157.8" yaw="95.0"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-1673.2" y="1947.1" z="150.8" yaw="185.3"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="-1515.3" y="-143.8" z="155.1" yaw="179.8"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-787.8" y="642.6" z="150.1" yaw="26.9"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-647.1" y="1100.9" z="149.0" yaw="163.2"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-1235.5" y="1176.6" z="149.6" yaw="192.8"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-1534.3" y="1290.6" z="151.9" yaw="118.6"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-1444.0" y="1761.3" z="148.9" yaw="94.1"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="-1797.3" y="2078.5" z="151.9" yaw="95.2"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-1240.2" y="2639.0" z="146.8" yaw="-13.4"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="-516.7" y="1978.2" z="147.1" yaw="-110.6"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-1138.4" y="390.7" z="152.0" yaw="7.0"/>
            <flow_speed value="9"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="-1293.2" y="678.9" z="152.6" yaw="0.5"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-1227.1" y="908.0" z="150.8" yaw="90.1"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_6" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1405.6" y="2634.0" z="147.8" yaw="191.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="18" to="45"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="-1212.72" y="380.14" z="152.61" yaw="9.27"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="-1073.6" y="412.6" z="151.5" yaw="105.7"/>
            <distance value="98"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_6" type="DynamicObjectCrossing">
            <trigger_point x="-1395.3" y="529.0" z="154.4" yaw="115.6"/>
            <distance value="50"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="-1137.7" y="547.4" z="151.6" yaw="182.5"/>
            <distance value="70"/>
            <frequency from="45" to="90"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-1206.9" y="953.7" z="150.4" yaw="0.8"/>
            <distance value="140"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-524.0" y="1698.1" z="147.8" yaw="201.3"/>
            <distance value="80"/>
            <frequency from="45" to="100"/>
         </scenario>
         <scenario name="InvadingTurn_10" type="InvadingTurn">
            <trigger_point x="-1002.5" y="1522.4" z="147.6" yaw="185.3"/>
            <distance value="130"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_4" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1280.6" y="1705.9" z="148.1" yaw="107.9"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="InvadingTurn_11" type="InvadingTurn">
            <trigger_point x="-1046.3" y="2241.0" z="146.6" yaw="65.8"/>
            <distance value="150"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="-1072.5" y="2521.3" z="146.3" yaw="124.8"/>
            <distance value="75"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="InvadingTurn_12" type="InvadingTurn">
            <trigger_point x="-1599.3" y="2822.7" z="148.4" yaw="152.9"/>
            <distance value="160"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_5" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-1812.5" y="2776.5" z="150.1" yaw="238.4"/>
            <distance value="75"/>
            <frequency value="85"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="8"/>
         </scenario>
         <scenario name="InvadingTurn_13" type="InvadingTurn">
            <trigger_point x="-1932.3" y="2420.1" z="152.1" yaw="233.9"/>
            <distance value="160"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="InvadingTurn_14" type="InvadingTurn">
            <trigger_point x="-2180.9" y="2440.0" z="153.6" yaw="111.7"/>
            <distance value="130"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-1146.2" y="2086.4" z="147.1" yaw="55.8"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-1469.0" y="2643.0" z="148.2" yaw="106.4"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="-1284.2" y="366.7" z="153.3" yaw="12.0"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="-1423.4" y="668.7" z="154.3" yaw="14.1"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="-1112.2" y="665.8" z="151.1" yaw="-89.8"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="-867.1" y="1564.1" z="147.5" yaw="201.3"/>
         </scenario>
         <scenario name="ControlLoss_14" type="ControlLoss">
            <trigger_point x="-1151.9" y="2078.0" z="147.1" yaw="55.8"/>
         </scenario>
         <scenario name="ControlLoss_15" type="ControlLoss">
            <trigger_point x="-1468.0" y="2639.6" z="148.2" yaw="106.4"/>
         </scenario>
         <scenario name="ControlLoss_16" type="ControlLoss">
            <trigger_point x="-2126.0" y="2868.7" z="152.1" yaw="125.1"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1298.4" y="304.6" z="153.5" yaw="108.1"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="85"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_2" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1658.7" y="75.2" z="158.3" yaw="97.5"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="78"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-1429.0" y="602.6" z="154.6" yaw="111.3"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="69"/>
         </scenario>
      </scenarios>
   </route>
   <route id="17" town="Town13">
      <!-- Rural + interurban -->
      <weathers>
      <!-- From heavy rain with low-hanging fog and strong winds to a severe thunderstorm with dense fog and strong winds -->
         <weather route_percentage="0"
            cloudiness="80.0" precipitation="40.0" precipitation_deposits="60.0" wetness="80.0"
            wind_intensity="60.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-90.0" fog_density="60.0"/>
         <weather route_percentage="100"
            cloudiness="100.0" precipitation="100.0" precipitation_deposits="90.0" wetness="100.0"
            wind_intensity="100.0" sun_azimuth_angle="-1.0" sun_altitude_angle="0.0" fog_density="100.0"/>
      </weathers>
      <waypoints>
         <position x="-4228.0" y="4329.5" z="174.5"/>
         <position x="-4341.9" y="4042.7" z="178.7"/>
         <position x="-4165.2" y="3829.3" z="184.7"/>
         <position x="-3919.1" y="3821.4" z="185.8"/>
         <position x="-3857.0" y="4047.4" z="183.4"/>
         <position x="-4101.4" y="4014.4" z="182.7"/>
         <position x="-3941.4" y="3932.9" z="184.9"/>
         <position x="-3833.8" y="3933.5" z="184.7"/>
         <position x="-3694.8" y="4008.4" z="183.2"/>
         <position x="-3653.2" y="4158.1" z="181.6"/>
         <position x="-3257.0" y="4041.7" z="180.2"/>
         <position x="-3291.7" y="3932.7" z="179.5"/>
         <position x="-3386.6" y="3620.7" z="176.5"/>
         <position x="-3343.6" y="3789.7" z="178.4"/>
         <position x="-3469.7" y="3788.5" z="180.1"/>
         <position x="-3541.8" y="3717.1" z="180.7"/>
         <position x="-3644.0" y="3614.7" z="181.8"/>
         <position x="-3700.3" y="3510.0" z="181.9"/>
         <position x="-3761.7" y="3105.6" z="177.5"/>
         <position x="-3787.5" y="2169.0" z="158.5"/>
         <position x="-4281.0" y="269.3" z="172.0"/>
         <position x="-4221.4" y="113.8" z="172.7"/>
         <position x="-4133.9" y="16.2" z="173.2"/>
         <position x="-4061.1" y="-220.4" z="171.8"/>
         <position x="-4776.2" y="-351.3" z="164.2"/>
         <position x="-4772.5" y="-41.2" z="163.8"/>
         <position x="-4605.6" y="-77.9" z="166.2"/>
         <position x="-4548.0" y="-213.3" z="166.9"/>
         <position x="-4456.86" y="-157.66" z="168.41"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4262.5" y="4152.0" z="177.8" yaw="180.3"/>
            <flow_speed value="11"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="-4365.1" y="3972.5" z="179.7" yaw="255.1"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4173.1" y="3931.7" z="183.3" yaw="0.3"/>
            <flow_speed value="9"/>
            <source_dist_interval from="13" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4176.4" y="3675.0" z="185.9" yaw="275.3"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4001.8" y="3613.5" z="186.6" yaw="0.6"/>
            <flow_speed value="12"/>
            <source_dist_interval from="20" to="60"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3874.1" y="3884.5" z="185.3" yaw="60.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3858.3" y="4102.4" z="182.6" yaw="91.4"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="-4039.3" y="4153.2" z="180.6" yaw="180.3"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4105.3" y="3998.8" z="182.9" yaw="255.2"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="53"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="-3764.7" y="4333.2" z="178.6" yaw="0.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="OppositeVehicleRunningRedLight_1" type="OppositeVehicleRunningRedLight">
            <trigger_point x="-3602.1" y="4158.4" z="181.5" yaw="0.3"/>
            <direction value="left"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-3321.4" y="4159.8" z="180.9" yaw="0.3"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_6" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3256.2" y="3983.2" z="179.8" yaw="270.8"/>
            <flow_speed value="10"/>
            <source_dist_interval from="15" to="50"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_6" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3376.1" y="3932.5" z="180.2" yaw="180.2"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="48"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3418.7" y="3859.7" z="180.1" yaw="270.7"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="-3416.6" y="3685.3" z="178.0" yaw="270.6"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="-3255.4" y="3750.4" z="176.8" yaw="91.4"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_7" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3492.2" y="3788.3" z="180.5" yaw="180.5"/>
            <flow_speed value="7"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-3541.3" y="3673.7" z="180.2" yaw="270.7"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="-3639.0" y="3614.8" z="181.7" yaw="180.7"/>
            <flow_speed value="14"/>
            <source_dist_interval from="20" to="66"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_8" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4133.9" y="25.1" z="173.2" yaw="270.0"/>
            <flow_speed value="8"/>
            <source_dist_interval from="14" to="48"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_9" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3995.7" y="-134.7" z="172.7" yaw="271.1"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="50"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_3" type="OppositeVehicleTakingPriority">
            <trigger_point x="-4178.6" y="-255.3" z="171.0" yaw="179.9"/>
         </scenario>
         <scenario name="VehicleTurningRoute_3" type="VehicleTurningRoute">
            <trigger_point x="-4736.9" y="26.9" z="164.2" yaw="76.7"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_11" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4626.8" y="113.1" z="165.8" yaw="0.1"/>
            <flow_speed value="10"/>
            <source_dist_interval from="18" to="53"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_10" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4619.9" y="-112.9" z="166.0" yaw="245.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="47"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_11" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4564.7" y="-208.3" z="166.7" yaw="-17.0"/>
            <flow_speed value="7"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-4221.44" y="4329.60" z="174.63" yaw="0.45"/>
            <distance value="58"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="-3855.0" y="3968.4" z="184.4" yaw="91.4"/>
            <distance value="75"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="-4088.9" y="4111.0" z="181.0" yaw="268.2"/>
            <distance value="75"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="-3834.5" y="3933.5" z="184.7" yaw="0.3"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_6" type="DynamicObjectCrossing">
            <trigger_point x="-3860.2" y="4179.4" z="181.2" yaw="91.4"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_7" type="DynamicObjectCrossing">
            <trigger_point x="-3671.4" y="4158.0" z="181.7" yaw="0.3"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_8" type="DynamicObjectCrossing">
            <trigger_point x="-3531.31" y="4158.72" z="181.28" yaw="0.30"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_9" type="DynamicObjectCrossing">
            <trigger_point x="-3419.48" y="3916.49" z="180.52" yaw="270.74"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_10" type="DynamicObjectCrossing">
            <trigger_point x="-3272.6" y="3790.4" z="177.6" yaw="180.5"/>
            <distance value="44"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_11" type="DynamicObjectCrossing">
            <trigger_point x="-3445.36" y="3788.75" z="179.75" yaw="180.54"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_12" type="DynamicObjectCrossing">
            <trigger_point x="-4133.9" y="86.5" z="173.6" yaw="270.0"/>
            <distance value="68"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_13" type="DynamicObjectCrossing">
            <trigger_point x="-3996.7" y="-77.5" z="173.2" yaw="271.1"/>
            <distance value="64"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_15" type="DynamicObjectCrossing">
            <trigger_point x="-4573.3" y="84.8" z="166.7" yaw="267.8"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-4114.76" y="4152.79" z="179.83" yaw="180.33"/>
            <distance value="66"/>
            <frequency from="38" to="58"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="-4312.7" y="4134.1" z="177.4" yaw="256.9"/>
            <distance value="115"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-4139.2" y="3906.6" z="184.0" yaw="246.5"/>
            <distance value="125"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="-4142.48" y="3612.10" z="186.44" yaw="0.59"/>
            <distance value="75"/>
            <frequency from="40" to="83"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-3934.6" y="3758.8" z="186.2" yaw="88.6"/>
            <distance value="110"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="-3891.96" y="4154.07" z="181.6" yaw="180.33"/>
            <distance value="70"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="-4096.69" y="3932.08" z="184.09" yaw="0.31"/>
            <distance value="75"/>
            <frequency from="40" to="110"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="-3694.4" y="3963.6" z="183.5" yaw="90.5"/>
            <distance value="70"/>
            <frequency from="40" to="100"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="-3258.18" y="4134.73" z="180.77" yaw="270.76"/>
            <distance value="70"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="InvadingTurn_4" type="InvadingTurn">
            <trigger_point x="-3373.4" y="3620.9" z="176.3" yaw="0.7"/>
            <distance value="120"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3705.9" y="3451.0" z="181.4" yaw="264.0"/>
            <distance value="75"/>
            <frequency from="45" to="110"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-3763.5" y="3096.7" z="177.4" yaw="258.6"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="12"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-3786.1" y="2178.2" z="158.6" yaw="261.6"/>
            <distance value="70"/>
            <frequency value="78"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3890.8" y="1903.2" z="158.5" yaw="244.7"/>
            <distance value="75"/>
            <frequency from="50" to="100"/>
         </scenario>
         <scenario name="AccidentTwoWays_4" type="AccidentTwoWays">
            <trigger_point x="-4270.5" y="1100.0" z="166.8" yaw="246.3"/>
            <distance value="75"/>
            <frequency from="40" to="90"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_3" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-4339.1" y="707.3" z="169.1" yaw="273.9"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="11"/>
         </scenario>
         <scenario name="AccidentTwoWays_5" type="AccidentTwoWays">
            <trigger_point x="-4293.7" y="400.6" z="171.6" yaw="276.8"/>
            <distance value="70"/>
            <frequency from="45" to="99"/>
         </scenario>
         <scenario name="InvadingTurn_5" type="InvadingTurn">
            <trigger_point x="-4022.0" y="-202.4" z="172.0" yaw="192.3"/>
            <distance value="120"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="InvadingTurn_6" type="InvadingTurn">
            <trigger_point x="-4322.3" y="-253.8" z="169.7" yaw="177.1"/>
            <distance value="100"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="AccidentTwoWays_6" type="AccidentTwoWays">
            <trigger_point x="-4861.1" y="-323.9" z="163.6" yaw="79.5"/>
            <distance value="70"/>
            <frequency from="35" to="88"/>
         </scenario>
         <scenario name="InvadingTurn_8" type="InvadingTurn">
            <trigger_point x="-4800.0" y="-75.4" z="163.6" yaw="57.3"/>
            <distance value="115"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="-3933.2" y="3635.7" z="186.3" yaw="90.7"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="-3733.9" y="4155.0" z="181.8" yaw="180.3"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-3834.1" y="4332.7" z="178.2" yaw="0.5"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-3396.49" y="4159.42" z="180.97" yaw="0.30"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-3275.1" y="3932.8" z="179.4" yaw="180.2"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="-3417.5" y="3764.7" z="179.1" yaw="270.7"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="-3694.8" y="3586.9" z="182.6" yaw="266.6"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="-3828.4" y="2522.2" z="165.1" yaw="278.7"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="-4037.9" y="1592.7" z="161.9" yaw="244.7"/>
         </scenario>
         <scenario name="HardBreakRoute_10" type="HardBreakRoute">
            <trigger_point x="-4243.6" y="113.7" z="172.4" yaw="0.1"/>
         </scenario>
         <scenario name="HardBreakRoute_11" type="HardBreakRoute">
            <trigger_point x="-4112.2" y="-59.4" z="172.9" yaw="0.1"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="-4169.4" y="4330.0" z="175.3" yaw="0.5"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="-4340.3" y="3930.8" z="180.9" yaw="0.3"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="-3933.4" y="3651.4" z="186.3" yaw="90.7"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="-4115.5" y="3931.9" z="183.9" yaw="0.3"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-3741.3" y="4154.9" z="181.8" yaw="180.3"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-3825.6" y="4332.7" z="178.3" yaw="0.5"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-3693.5" y="4280.7" z="179.8" yaw="270.5"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-3508.3" y="4158.8" z="181.2" yaw="0.3"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-3284.8" y="3932.7" z="179.5" yaw="180.2"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-3440.6" y="3788.8" z="179.7" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="-3565.7" y="3615.7" z="180.1" yaw="180.7"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="-3830.6" y="2536.9" z="165.5" yaw="277.7"/>
         </scenario>
         <scenario name="ControlLoss_14" type="ControlLoss">
            <trigger_point x="-3781.5" y="2291.2" z="159.9" yaw="273.8"/>
         </scenario>
         <scenario name="ControlLoss_15" type="ControlLoss">
            <trigger_point x="-4033.7" y="1601.5" z="161.7" yaw="244.7"/>
         </scenario>
         <scenario name="ControlLoss_16" type="ControlLoss">
            <trigger_point x="-4261.1" y="1120.7" z="166.8" yaw="244.8"/>
         </scenario>
         <scenario name="ControlLoss_17" type="ControlLoss">
            <trigger_point x="-4339.0" y="704.7" z="169.2" yaw="274.1"/>
         </scenario>
         <scenario name="ControlLoss_18" type="ControlLoss">
            <trigger_point x="-4245.7" y="113.7" z="172.3" yaw="0.1"/>
         </scenario>
         <scenario name="ControlLoss_19" type="ControlLoss">
            <trigger_point x="-4104.9" y="-59.4" z="172.9" yaw="0.1"/>
         </scenario>
         <scenario name="ControlLoss_20" type="ControlLoss">
            <trigger_point x="-4016.7" y="-201.4" z="172.0" yaw="189.2"/>
         </scenario>
         <scenario name="ControlLoss_21" type="ControlLoss">
            <trigger_point x="-4724.1" y="-350.9" z="164.6" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_22" type="ControlLoss">
            <trigger_point x="-4706.34" y="112.92" z="164.45" yaw="0.11"/>
         </scenario>
         <scenario name="ControlLoss_23" type="ControlLoss">
            <trigger_point x="-4637.4" y="-185.2" z="165.7" yaw="-17.7"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_9" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4399.3" y="-246.8" z="168.8" yaw="172.7"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="60"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_14" type="DynamicObjectCrossing">
            <trigger_point x="-4475.9" y="-254.0" z="167.8" yaw="252.1"/>
            <distance value="75"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="InvadingTurn_7" type="InvadingTurn">
            <trigger_point x="-4545.7" y="-397.7" z="166.3" yaw="158.5"/>
            <distance value="100"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4145.4" y="4330.2" z="175.6" yaw="0.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="12" to="73"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_2" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4089.9" y="4208.2" z="178.9" yaw="270.9"/>
            <flow_speed value="9"/>
            <source_dist_interval from="17" to="62"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3803.5" y="4154.6" z="181.8" yaw="180.3"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3862.3" y="4261.6" z="179.6" yaw="91.4"/>
            <flow_speed value="8"/>
            <source_dist_interval from="12" to="62"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_7" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4200.5" y="113.8" z="172.9" yaw="0.1"/>
            <flow_speed value="12"/>
            <source_dist_interval from="15" to="78"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_8" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4082.3" y="-59.3" z="173.0" yaw="0.1"/>
            <flow_speed value="13"/>
            <source_dist_interval from="16" to="80"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_10" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4775.9" y="-351.3" z="164.2" yaw="180.5"/>
            <flow_speed value="9"/>
            <source_dist_interval from="12" to="79"/>
         </scenario>
      </scenarios>
   </route>
   <route id="18" town="Town13">
      <!-- Rural + Highway -->
      <weathers>
      <!-- From dense fog with overcast skies and a gentle breeze to partly cloudy -->
         <weather route_percentage="0"
            cloudiness="80.0" precipitation="0.0" precipitation_deposits="0.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="45.0" fog_density="70.0"/>
         <weather route_percentage="100"
            cloudiness="5.0" precipitation="0.0" precipitation_deposits="35.0" wetness="0.0"
            wind_intensity="10.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-90.0" fog_density="90.0"/>
      </weathers>
      <waypoints>
         <position x="-2077.8" y="5729.8" z="154.2"/>
         <position x="-2161.9" y="5899.8" z="153.3"/>
         <position x="-2180.6" y="5904.9" z="153.5"/>
         <position x="-2618.9" y="5931.2" z="157.7"/>
         <position x="-2861.1" y="6006.9" z="161.2"/>
         <position x="-2924.79" y="6006.16" z="162.01"/>
         <position x="-3098.68" y="6004.32" z="164.00"/>
         <position x="-3316.7" y="5957.7" z="165.8"/>
         <position x="-3564.8" y="5899.1" z="168.3"/>
         <position x="-3682.4" y="5894.4" z="170.2"/>
         <position x="-3713.1" y="5861.8" z="170.4"/>
         <position x="-3758.9" y="5740.1" z="169.8"/>
         <position x="-3867.2" y="5621.0" z="170.0"/>
         <position x="-4163.6" y="5441.6" z="170.9"/>
         <position x="-4596.9" y="5882.1" z="182.3"/>
         <position x="-5401.9" y="5828.6" z="189.5"/>
         <position x="-5555.3" y="5719.8" z="191.4"/>
         <position x="-5603.3" y="5667.1" z="191.8"/>
         <position x="-5736.3" y="5388.5" z="192.4"/>
         <position x="-5753.6" y="5264.0" z="191.9"/>
         <position x="-5756.08" y="5167.66" z="191.20"/>
         <position x="-5750.3" y="5037.4" z="190.2"/>
         <position x="-5478.4" y="4903.4" z="183.3"/>
         <position x="-4538.8" y="4891.4" z="167.3"/>
         <position x="-4412.1" y="5198.7" z="169.1"/>
         <position x="-4106.8" y="5326.5" z="168.6"/>
         <position x="-4095.3" y="5153.0" z="166.6"/>
         <position x="-4314.4" y="4954.7" z="166.5"/>
         <position x="-4373.4" y="4836.6" z="166.6"/>
         <position x="-5512.7" y="4899.9" z="183.9"/>
         <position x="-5742.6" y="4727.7" z="186.9"/>
         <position x="-5742.6" y="4550.2" z="184.3"/>
         <position x="-5744.1" y="4449.6" z="182.5"/>
         <position x="-5781.5" y="2978.0" z="173.1"/>
      </waypoints>
      <scenarios>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-2098.1" y="5844.7" z="153.1" yaw="92.5"/>
            <flow_speed value="15"/>
            <source_dist_interval from="18" to="66"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="-3652.1" y="5894.7" z="169.7" yaw="-179.4"/>
         </scenario>
         <scenario name="SignalizedJunctionLeftTurn_1" type="SignalizedJunctionLeftTurn">
            <trigger_point x="-3707.9" y="5508.1" z="166.7" yaw="270.8"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3760.2" y="5444.8" z="166.7" yaw="180.5"/>
            <flow_speed value="11"/>
            <source_dist_interval from="18" to="58"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3806.1" y="5394.4" z="166.7" yaw="-90.2"/>
            <flow_speed value="11"/>
            <source_dist_interval from="13" to="60"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3763.3" y="5339.8" z="166.0" yaw="359.9"/>
            <flow_speed value="15"/>
            <source_dist_interval from="20" to="66"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="-3714.1" y="5692.4" z="168.5" yaw="90.8"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3849.6" y="5739.4" z="171.3" yaw="180.5"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_6" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-3906.8" y="5669.8" z="171.3" yaw="269.8"/>
            <flow_speed value="9"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_1" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-3805.7" y="5498.5" z="167.7" yaw="-90.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="16" to="54"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3851.5" y="5444.0" z="167.7" yaw="180.5"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="-3947.6" y="5443.1" z="168.7" yaw="180.5"/>
            <direction value="left"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_3" type="OppositeVehicleTakingPriority">
            <trigger_point x="-4039.4" y="5442.2" z="169.6" yaw="180.6"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_7" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4292.3" y="5546.7" z="173.9" yaw="106.0"/>
            <flow_speed value="7"/>
            <source_dist_interval from="10" to="45"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="-4357.6" y="5611.4" z="175.6" yaw="180.8"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="-4409.6" y="5679.1" z="177.3" yaw="90.6"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="-4491.7" y="5734.0" z="179.0" yaw="180.5"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_8" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4555.2" y="5813.8" z="180.8" yaw="91.1"/>
            <flow_speed value="13"/>
            <source_dist_interval from="18" to="55"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="-4478.9" y="5079.6" z="168.2" yaw="0.5"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="-4341.1" y="5344.8" z="170.7" yaw="54.8"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="-4195.2" y="5340.3" z="169.4" yaw="359.9"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_4" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4095.3" y="5155.6" z="166.6" yaw="270.6"/>
            <flow_speed value="9"/>
            <source_dist_interval from="13" to="48"/>
         </scenario>
         <scenario name="BlockedIntersection_5" type="BlockedIntersection">
            <trigger_point x="-4228.4" y="5078.9" z="166.7" yaw="180.5"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_5" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4314.1" y="4943.5" z="166.5" yaw="271.6"/>
            <flow_speed value="15"/>
            <source_dist_interval from="18" to="65"/>
         </scenario>
         <scenario name="BlockedIntersection_6" type="BlockedIntersection">
            <trigger_point x="-4531.2" y="4614.3" z="167.5" yaw="271.0"/>
         </scenario>
         <scenario name="BlockedIntersection_7" type="BlockedIntersection">
            <trigger_point x="-4386.5" y="4558.8" z="168.7" yaw="0.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_9" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4310.3" y="4643.5" z="168.1" yaw="91.2"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="48"/>
         </scenario>
         <scenario name="VehicleTurningRoute_2" type="VehicleTurningRoute">
            <trigger_point x="-4464.4" y="4722.9" z="167.0" yaw="180.2"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_10" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="-4537.0" y="4790.9" z="167.0" yaw="91.0"/>
            <flow_speed value="16"/>
            <source_dist_interval from="20" to="65"/>
         </scenario>
         <scenario name="MergerIntoSlowTraffic_1" type="MergerIntoSlowTraffic">
            <trigger_point x="-3353.9" y="5936.9" z="166.0" yaw="-153.8"/>
            <start_actor_flow x="-3347.1" y="5901.2" z="165.5"/>
            <end_actor_flow x="-3603.2" y="5898.7" z="168.9"/>
            <flow_speed value="9"/>
            <source_dist_interval from="15" to="45"/>
         </scenario>
         <scenario name="EnterActorFlow_1" type="EnterActorFlow">
            <trigger_point x="-5362.0" y="5843.5" z="189.1" yaw="-162.2"/>
            <start_actor_flow x="-5375.3" y="5866.8" z="189.5"/>
            <end_actor_flow x="-5590.5" y="5678.0" z="191.7"/>
            <flow_speed value="17"/>
            <source_dist_interval from="15" to="65"/>
         </scenario>
         <scenario name="EnterActorFlow_2" type="EnterActorFlow">
            <trigger_point x="-5716.6" y="4856.6" z="187.6" yaw="240.2"/>
            <start_actor_flow x="-5749.4" y="4860.4" z="188.5"/>
            <end_actor_flow x="-5741.6" y="4502.3" z="183.4"/>
            <flow_speed value="18"/>
            <source_dist_interval from="15" to="70"/>
         </scenario>
         <scenario name="YieldToEmergencyVehicle_1" type="YieldToEmergencyVehicle">
            <trigger_point x="-5745.3" y="4512.1" z="183.6" yaw="-88.9"/>
            <distance value="130"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="-3806.0" y="5435.9" z="167.1" yaw="-92.6"/>
            <distance value="50"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="-3736.5" y="5740.3" z="169.4" yaw="180.5"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="-3906.61" y="5722.80" z="171.99" yaw="269.81"/>
            <distance value="65"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="-3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="-4096.0" y="5215.1" z="167.2" yaw="270.6"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="-4308.9" y="4572.4" z="169.1" yaw="91.2"/>
            <distance value="70"/>
            <blocker_model value="static.prop.haybalelb"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="-3710.9" y="5713.8" z="168.7" yaw="270.8"/>
            <distance value="75"/>
            <frequency from="40" to="90"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="-3710.9" y="5474.6" z="166.4" yaw="90.8"/>
            <distance value="75"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="-3873.1" y="5620.9" z="170.1" yaw="0.8"/>
            <distance value="110"/>
            <offset value="0.3"/>
         </scenario>
         <scenario name="HazardAtSideLane_1" type="HazardAtSideLane">
            <trigger_point x="-4592.2" y="5882.1" z="182.2" yaw="-179.2"/>
            <distance value="75"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="10"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_1" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-5581.2" y="4903.5" z="185.3" yaw="-0.1"/>
            <distance value="75"/>
            <frequency value="75"/>
            <bicycle_drive_distance value="90"/>
            <bicycle_speed value="11"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="-5174.1" y="4895.3" z="177.1" yaw="-3.2"/>
            <distance value="80"/>
            <frequency from="35" to="99"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="-4875.5" y="4873.1" z="171.0" yaw="-4.5"/>
            <distance value="75"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="-4538.5" y="4879.2" z="167.2" yaw="91.2"/>
            <distance value="75"/>
            <frequency from="35" to="85"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="-4427.0" y="5100.8" z="168.1" yaw="87.9"/>
            <distance value="120"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="-4287.5" y="5358.1" z="170.5" yaw="335.7"/>
            <distance value="110"/>
            <offset value="0.6"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="-4118.7" y="5079.9" z="166.3" yaw="180.5"/>
            <distance value="70"/>
            <frequency from="35" to="85"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="-4317.3" y="5057.7" z="167.0" yaw="271.6"/>
            <distance value="75"/>
            <frequency from="25" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_3" type="ParkedObstacleTwoWays">
            <trigger_point x="-4532.9" y="4710.1" z="167.0" yaw="271.0"/>
            <distance value="75"/>
            <frequency from="35" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_4" type="ParkedObstacleTwoWays">
            <trigger_point x="-4333.7" y="4723.4" z="167.2" yaw="180.2"/>
            <distance value="75"/>
            <frequency from="35" to="95"/>
         </scenario>
         <scenario name="HazardAtSideLaneTwoWays_2" type="HazardAtSideLaneTwoWays">
            <trigger_point x="-4881.8" y="4870.1" z="171.1" yaw="175.5"/>
            <distance value="75"/>
            <frequency value="80"/>
            <bicycle_drive_distance value="100"/>
            <bicycle_speed value="15"/>
         </scenario>
         <scenario name="StaticCutIn_3" type="StaticCutIn">
            <trigger_point x="-5733.9" y="3310.5" z="170.4" yaw="-94.4"/>
            <distance value="120"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="-2188.3" y="5905.4" z="153.5" yaw="176.2"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="-3713.0" y="5854.8" z="170.3" yaw="270.8"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="-3709.2" y="5355.0" z="165.7" yaw="90.8"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="-5117.1" y="5871.7" z="186.8" yaw="-177.3"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="-4534.9" y="4829.1" z="167.0" yaw="271.0"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="-4506.2" y="4558.8" z="168.0" yaw="0.0"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="-5745.6" y="4704.9" z="186.6" yaw="-88.9"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="-2183.9" y="5905.1" z="153.5" yaw="175.9"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="-3713.1" y="5864.6" z="170.4" yaw="270.8"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="-3747.9" y="5444.9" z="166.6" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_4" type="ControlLoss">
            <trigger_point x="-3826.6" y="5444.2" z="167.4" yaw="180.5"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="-4018.4" y="5442.4" z="169.4" yaw="-179.3"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="-4125.1" y="5441.4" z="170.5" yaw="180.6"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="-4610.5" y="5881.9" z="182.4" yaw="-179.2"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="-5100.3" y="5872.4" z="186.7" yaw="-177.5"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="-5668.2" y="5565.1" z="192.4" yaw="-118.0"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="-5622.9" y="4903.6" z="186.1" yaw="-360.1"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="-4534.7" y="4816.9" z="167.0" yaw="271.0"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="-4863.7" y="4868.7" z="170.8" yaw="175.5"/>
         </scenario>
         <scenario name="ControlLoss_14" type="ControlLoss">
            <trigger_point x="-5745.7" y="4709.6" z="186.7" yaw="-88.9"/>
         </scenario>
         <scenario name="HighwayCutIn_1" type="HighwayCutIn">
            <trigger_point x="-2768.1" y="5999.7" z="160.0" yaw="167.3"/>
            <other_actor_location x="-2766.5" y="6011.6" z="160.0"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_2" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4351.2" y="5611.4" z="175.6" yaw="180.8"/>
            <flow_speed value="8"/>
            <source_dist_interval from="10" to="88"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="-5584.7" y="5690.9" z="191.6" yaw="-129.1"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="-5675.9" y="5550.4" z="192.4" yaw="-116.8"/>
            <distance value="102"/>
            <direction value="right"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="-5737.1" y="5370.1" z="192.4" yaw="-102.8"/>
            <distance value="72"/>
            <speed value="50"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="-5727.8" y="3613.7" z="169.8" yaw="-88.9"/>
         </scenario>
         <scenario name="ControlLoss_15" type="ControlLoss">
            <trigger_point x="-5728.6" y="3659.1" z="170.0" yaw="-88.9"/>
         </scenario>
         <scenario name="ControlLoss_16" type="ControlLoss">
            <trigger_point x="-5727.0" y="3447.9" z="169.8" yaw="-91.4"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="-4577.7" y="4847.0" z="167.3" yaw="176.4"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="-4585.0" y="4847.5" z="167.4" yaw="176.4"/>
         </scenario>
         <scenario name="NonSignalizedJunctionLeftTurn_3" type="NonSignalizedJunctionLeftTurn">
            <trigger_point x="-4541.7" y="5031.9" z="168.3" yaw="91.0"/>
            <flow_speed value="7"/>
            <source_dist_interval from="15" to="66"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="-5223.6" y="4894.4" z="178.1" yaw="177.3"/>
            <distance value="88"/>
            <frequency from="15" to="80"/>
         </scenario>
      </scenarios>
   </route>
   <route id="19" town="Town13">
      <!-- Urban + residential -->
      <weathers>
      <!-- From mostly cloudy with low-hanging fog to heavy rain at midnight -->
         <weather route_percentage="0"
            cloudiness="60.0" precipitation="0.0" precipitation_deposits="50.0" wetness="60.0"
            wind_intensity="30.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-90.0" fog_density="3.0"/>
         <weather route_percentage="100"
            cloudiness="100.0" precipitation="75.0" precipitation_deposits="90.0" wetness="100.0"
            wind_intensity="100.0" sun_azimuth_angle="-1.0" sun_altitude_angle="-90.0" fog_density="3.0"/>
      </weathers>
      <waypoints>
         <position x="5581.6" y="3556.7" z="158.0"/>
         <position x="5492.2" y="3788.6" z="159.0"/>
         <position x="5738.5" y="4126.5" z="159.3"/>
         <position x="5224.0" y="4342.8" z="157.8"/>
         <position x="5222.2" y="3689.5" z="160.4"/>
         <position x="5153.6" y="3561.0" z="159.7"/>
         <position x="5047.9" y="3333.4" z="156.6"/>
         <position x="5220.9" y="3195.3" z="155.7"/>
         <position x="5153.0" y="3073.4" z="153.8"/>
         <position x="4645.9" y="3090.0" z="149.3"/>
         <position x="4641.3" y="3135.1" z="149.7"/>
         <position x="4439.7" y="3845.4" z="150.6"/>
         <position x="4398.1" y="4407.9" z="148.0"/>
         <position x="4401.2" y="4439.1" z="147.9"/>
         <position x="3948.2" y="5479.6" z="156.2"/>
         <position x="3913.7" y="5488.6" z="157.3"/>
         <position x="3879.9" y="5468.0" z="158.8"/>
         <position x="3849.9" y="5274.5" z="161.6"/>
         <position x="3808.0" y="5225.9" z="163.8"/>
         <position x="3653.5" y="5254.9" z="170.3"/>
         <position x="3469.1" y="5373.5" z="172.9"/>
         <position x="3339.2" y="5378.4" z="173.8"/>
         <position x="3234.3" y="5372.6" z="173.4"/>
         <position x="3144.5" y="5374.4" z="172.3"/>
         <position x="2883.4" y="5513.2" z="164.4"/>
         <position x="2795.8" y="5409.8" z="164.2"/>
         <position x="2734.0" y="5232.6" z="165.8"/>
         <position x="2916.8" y="4976.5" z="176.5"/>
         <position x="3078.6" y="5151.5" z="176.3"/>
         <position x="3152.9" y="5115.1" z="178.3"/>
         <position x="3228.5" y="4982.9" z="181.9"/>
         <position x="3616.4" y="4990.8" z="175.0"/>
         <position x="3672.0" y="4971.1" z="172.5"/>
         <position x="3619.5" y="4905.4" z="175.2"/>
         <position x="3409.7" y="4861.4" z="182.7"/>
         <position x="3383.8" y="4809.0" z="183.5"/>
         <position x="3155.9" y="4602.5" z="186.4"/>
         <position x="3689.2" y="4643.2" z="170.4"/>
         <position x="3816.6" y="4720.6" z="163.4"/>
         <position x="3758.2" y="4991.0" z="167.8"/>
         <position x="3631.1" y="5104.3" z="173.2"/>
      </waypoints>
      <scenarios>
         <scenario name="ParkingExit_1" type="ParkingExit">
            <trigger_point x="5581.6" y="3556.7" z="158.0" yaw="179.4"/>
            <direction value="right"/>
            <front_vehicle_distance value="9"/>
            <behind_vehicle_distance value="9"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_1" type="VehicleTurningRoutePedestrian">
            <trigger_point x="5471.3" y="3557.8" z="158.5" yaw="179.4"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_1" type="SignalizedJunctionRightTurn">
            <trigger_point x="5755.6" y="3786.1" z="158.2" yaw="359.4"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="PedestrianCrossing_1" type="PedestrianCrossing">
            <trigger_point x="5791.9" y="3913.6" z="158.8" yaw="90.6"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_2" type="SignalizedJunctionRightTurn">
            <trigger_point x="5790.0" y="4080.5" z="159.4" yaw="90.6"/>
            <flow_speed value="11"/>
            <source_dist_interval from="15" to="58"/>
         </scenario>
         <scenario name="BlockedIntersection_1" type="BlockedIntersection">
            <trigger_point x="5285.1" y="4358.4" z="157.9" yaw="168.8"/>
         </scenario>
         <scenario name="PedestrianCrossing_2" type="PedestrianCrossing">
            <trigger_point x="5223.2" y="4050.5" z="160.0" yaw="269.8"/>
         </scenario>
         <scenario name="PriorityAtJunction_1" type="PriorityAtJunction">
            <trigger_point x="5223.2" y="4045.8" z="160.0" yaw="269.8"/>
         </scenario>
         <scenario name="PriorityAtJunction_2" type="PriorityAtJunction">
            <trigger_point x="5222.7" y="3854.5" z="160.7" yaw="269.8"/>
         </scenario>
         <scenario name="PedestrianCrossing_3" type="PedestrianCrossing">
            <trigger_point x="5222.7" y="3852.9" z="160.7" yaw="269.8"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_3" type="SignalizedJunctionRightTurn">
            <trigger_point x="5024.5" y="3562.3" z="159.3" yaw="179.4"/>
            <flow_speed value="11"/>
            <source_dist_interval from="16" to="56"/>
         </scenario>
         <scenario name="SignalizedJunctionRightTurn_4" type="SignalizedJunctionRightTurn">
            <trigger_point x="4969.9" y="3404.6" z="157.0" yaw="269.5"/>
            <flow_speed value="14"/>
            <source_dist_interval from="19" to="65"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_3" type="VehicleTurningRoutePedestrian">
            <trigger_point x="5164.2" y="3333.2" z="157.2" yaw="-0.1"/>
         </scenario>
         <scenario name="PedestrianCrossing_4" type="PedestrianCrossing">
            <trigger_point x="4625.0" y="3492.5" z="152.9" yaw="101.1"/>
         </scenario>
         <scenario name="BlockedIntersection_2" type="BlockedIntersection">
            <trigger_point x="3937.5" y="5480.2" z="156.5" yaw="159.5"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_4" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3850.9" y="5288.6" z="161.4" yaw="265.1"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_5" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3720.6" y="5224.9" z="167.9" yaw="180.6"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_1" type="OppositeVehicleTakingPriority">
            <trigger_point x="3472.5" y="5373.6" z="172.9" yaw="180.9"/>
         </scenario>
         <scenario name="BlockedIntersection_3" type="BlockedIntersection">
            <trigger_point x="3287.2" y="5395.1" z="173.2" yaw="160.6"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_6" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3235.0" y="5304.1" z="175.1" yaw="270.6"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_7" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3180.8" y="5266.9" z="175.3" yaw="181.3"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_1" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3144.7" y="5364.1" z="172.5" yaw="91.2"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="55"/>
         </scenario>
         <scenario name="PedestrianCrossing_6" type="PedestrianCrossing">
            <trigger_point x="3101.6" y="5414.6" z="170.6" yaw="180.3"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_2" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="2855.8" y="5522.6" z="163.6" yaw="165.4"/>
            <flow_speed value="10"/>
            <source_dist_interval from="10" to="58"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_8" type="VehicleTurningRoutePedestrian">
            <trigger_point x="2797.9" y="5281.5" z="166.6" yaw="-89.1"/>
         </scenario>
         <scenario name="BlockedIntersection_4" type="BlockedIntersection">
            <trigger_point x="2689.7" y="5232.1" z="164.6" yaw="180.6"/>
         </scenario>
         <scenario name="BlockedIntersection_5" type="BlockedIntersection">
            <trigger_point x="2638.0" y="5030.6" z="166.8" yaw="270.3"/>
         </scenario>
         <scenario name="BlockedIntersection_6" type="BlockedIntersection">
            <trigger_point x="2983.7" y="4977.8" z="178.1" yaw="1.2"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_9" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3039.7" y="5098.4" z="176.7" yaw="90.3"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_10" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3116.2" y="5152.3" z="177.0" yaw="1.2"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_3" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3154.3" y="5048.4" z="179.8" yaw="-88.8"/>
            <flow_speed value="11"/>
            <source_dist_interval from="14" to="59"/>
         </scenario>
         <scenario name="PedestrianCrossing_7" type="PedestrianCrossing">
            <trigger_point x="3351.4" y="4985.4" z="181.9" yaw="1.2"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_11" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3662.2" y="4944.3" z="173.1" yaw="-117.1"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_4" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3451.0" y="4902.2" z="181.4" yaw="181.1"/>
            <flow_speed value="11"/>
            <source_dist_interval from="10" to="60"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_12" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3409.9" y="4852.3" z="182.7" yaw="271.1"/>
         </scenario>
         <scenario name="BlockedIntersection_7" type="BlockedIntersection">
            <trigger_point x="3191.0" y="4806.4" z="184.4" yaw="180.8"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_5" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3137.3" y="4669.3" z="185.7" yaw="270.7"/>
            <flow_speed value="12"/>
            <source_dist_interval from="15" to="65"/>
         </scenario>
         <scenario name="OppositeVehicleTakingPriority_2" type="OppositeVehicleTakingPriority">
            <trigger_point x="3434.6" y="4611.2" z="182.7" yaw="4.2"/>
         </scenario>
         <scenario name="NonSignalizedJunctionRightTurn_6" type="NonSignalizedJunctionRightTurn">
            <trigger_point x="3692.7" y="4643.3" z="170.2" yaw="0.8"/>
            <flow_speed value="12"/>
            <source_dist_interval from="15" to="62"/>
         </scenario>
         <scenario name="BlockedIntersection_8" type="BlockedIntersection">
            <trigger_point x="3869.7" y="4948.1" z="161.5" yaw="81.7"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_1" type="ParkingCrossingPedestrian">
            <trigger_point x="5568.9" y="3556.8" z="158.1" yaw="179.4"/>
            <distance value="73"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_2" type="ParkingCrossingPedestrian">
            <trigger_point x="5416.7" y="3584.6" z="158.9" yaw="90.3"/>
            <distance value="80"/>
            <crossing_angle value="-2"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_3" type="ParkingCrossingPedestrian">
            <trigger_point x="5432.9" y="3789.2" z="159.4" yaw="359.4"/>
            <distance value="75"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_1" type="DynamicObjectCrossing">
            <trigger_point x="5793.1" y="3803.2" z="158.4" yaw="90.6"/>
            <distance value="75"/>
            <blocker_model value="static.prop.advertisement"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_2" type="DynamicObjectCrossing">
            <trigger_point x="5790.9" y="3996.1" z="159.1" yaw="90.6"/>
            <distance value="75"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_4" type="ParkingCrossingPedestrian">
            <trigger_point x="5223.4" y="4121.5" z="159.6" yaw="269.8"/>
            <distance value="75"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_5" type="ParkingCrossingPedestrian">
            <trigger_point x="5222.4" y="3746.2" z="160.6" yaw="269.8"/>
            <distance value="78"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_6" type="ParkingCrossingPedestrian">
            <trigger_point x="4971.0" y="3543.0" z="158.7" yaw="269.5"/>
            <distance value="75"/>
         </scenario>
         <scenario name="ParkingCrossingPedestrian_7" type="ParkingCrossingPedestrian">
            <trigger_point x="5220.9" y="3193.5" z="155.7" yaw="269.8"/>
            <distance value="78"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_3" type="DynamicObjectCrossing">
            <trigger_point x="3550.0" y="5366.7" z="171.5" yaw="162.3"/>
            <distance value="78"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_4" type="DynamicObjectCrossing">
            <trigger_point x="3308.9" y="5387.1" z="173.5" yaw="160.3"/>
            <distance value="50"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="-1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_6" type="DynamicObjectCrossing">
            <trigger_point x="2777.4" y="5233.1" z="167.1" yaw="180.6"/>
            <distance value="70"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="5"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_7" type="DynamicObjectCrossing">
            <trigger_point x="2658.2" y="4971.2" z="168.7" yaw="1.2"/>
            <distance value="65"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_8" type="DynamicObjectCrossing">
            <trigger_point x="3040.3" y="4994.8" z="179.0" yaw="90.3"/>
            <distance value="70"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="2"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_9" type="DynamicObjectCrossing">
            <trigger_point x="3054.7" y="5151.0" z="175.8" yaw="1.2"/>
            <distance value="65"/>
            <blocker_model value="static.prop.foodcart"/>
            <crossing_angle value="3"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_10" type="DynamicObjectCrossing">
            <trigger_point x="3427.0" y="4986.9" z="181.0" yaw="1.2"/>
            <distance value="70"/>
            <blocker_model value="static.prop.container"/>
            <crossing_angle value="1"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_11" type="DynamicObjectCrossing">
            <trigger_point x="3149.4" y="4602.4" z="186.4" yaw="1.4"/>
            <distance value="50"/>
            <blocker_model value="static.prop.foodcart"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_1" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5537.6" y="3788.2" z="158.8" yaw="359.4"/>
            <distance value="75"/>
            <frequency from="20" to="75"/>
         </scenario>
         <scenario name="ConstructionObstacle_1" type="ConstructionObstacle">
            <trigger_point x="5766.54" y="4125.88" z="159.48" yaw="179.44"/>
            <distance value="80"/>
            <speed value="49"/>
         </scenario>
         <scenario name="Accident_1" type="Accident">
            <trigger_point x="5590.5" y="4165.9" z="158.9" yaw="153.3"/>
            <distance value="75"/>
            <speed value="50"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_2" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5224.0" y="4350.3" z="157.8" yaw="269.8"/>
            <distance value="75"/>
            <frequency from="15" to="85"/>
         </scenario>
         <scenario name="VehicleOpensDoorTwoWays_3" type="VehicleOpensDoorTwoWays">
            <trigger_point x="5194.6" y="3560.6" z="159.7" yaw="179.4"/>
            <distance value="77"/>
            <frequency from="25" to="90"/>
         </scenario>
         <scenario name="ParkedObstacle_1" type="ParkedObstacle">
            <trigger_point x="4642.6" y="3094.4" z="149.3" yaw="91.7"/>
            <distance value="70"/>
         </scenario>
         <scenario name="Accident_2" type="Accident">
            <trigger_point x="4634.9" y="3348.7" z="151.7" yaw="91.7"/>
            <distance value="70"/>
         </scenario>
         <scenario name="ConstructionObstacle_3" type="ConstructionObstacle">
            <trigger_point x="4400.33" y="4017.47" z="149.58" yaw="92.77"/>
            <distance value="80"/>
            <speed value="45"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_1" type="ParkedObstacleTwoWays">
            <trigger_point x="3882.2" y="5481.7" z="158.6" yaw="260.3"/>
            <distance value="75"/>
            <frequency from="25" to="90"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_2" type="ParkedObstacleTwoWays">
            <trigger_point x="3829.4" y="5226.1" z="162.8" yaw="180.6"/>
            <distance value="70"/>
            <frequency from="15" to="100"/>
         </scenario>
         <scenario name="InvadingTurn_1" type="InvadingTurn">
            <trigger_point x="3655.3" y="5246.0" z="170.4" yaw="99.9"/>
            <distance value="100"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="DynamicObjectCrossing_5" type="DynamicObjectCrossing">
            <trigger_point x="3234.0" y="5396.0" z="172.8" yaw="270.6"/>
            <distance value="75"/>
         </scenario>
         <scenario name="InvadingTurn_2" type="InvadingTurn">
            <trigger_point x="3038.5" y="5420.5" z="169.4" yaw="166.2"/>
            <distance value="110"/>
            <offset value="0.4"/>
         </scenario>
         <scenario name="AccidentTwoWays_1" type="AccidentTwoWays">
            <trigger_point x="2794.4" y="5492.2" z="162.8" yaw="-89.1"/>
            <distance value="75"/>
            <frequency from="25" to="110"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_1" type="ConstructionObstacleTwoWays">
            <trigger_point x="2637.1" y="5206.4" z="163.5" yaw="270.3"/>
            <distance value="77"/>
            <frequency from="35" to="115"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_3" type="ParkedObstacleTwoWays">
            <trigger_point x="2822.1" y="4974.5" z="173.9" yaw="1.2"/>
            <distance value="78"/>
            <frequency from="18" to="99"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_4" type="ParkedObstacleTwoWays">
            <trigger_point x="3152.4" y="5137.3" z="177.8" yaw="-88.8"/>
            <distance value="74"/>
            <frequency from="15" to="95"/>
         </scenario>
         <scenario name="AccidentTwoWays_2" type="AccidentTwoWays">
            <trigger_point x="3188.9" y="4982.1" z="181.5" yaw="1.2"/>
            <distance value="74"/>
            <frequency from="20" to="98"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_2" type="ConstructionObstacleTwoWays">
            <trigger_point x="3623.3" y="4905.5" z="175.1" yaw="181.1"/>
            <distance value="78"/>
            <frequency from="20" to="105"/>
         </scenario>
         <scenario name="ConstructionObstacleTwoWays_3" type="ConstructionObstacleTwoWays">
            <trigger_point x="3394.9" y="4809.1" z="183.3" yaw="180.8"/>
            <distance value="76"/>
            <frequency from="25" to="99"/>
         </scenario>
         <scenario name="InvadingTurn_3" type="InvadingTurn">
            <trigger_point x="3506.8" y="4618.3" z="179.9" yaw="6.9"/>
            <distance value="110"/>
            <offset value="0.5"/>
         </scenario>
         <scenario name="AccidentTwoWays_3" type="AccidentTwoWays">
            <trigger_point x="3800.0" y="4652.1" z="163.7" yaw="76.4"/>
            <distance value="77"/>
            <frequency from="18" to="89"/>
         </scenario>
         <scenario name="ParkedObstacleTwoWays_5" type="ParkedObstacleTwoWays">
            <trigger_point x="3861.3" y="4993.1" z="162.1" yaw="181.2"/>
            <distance value="75"/>
            <frequency from="18" to="90"/>
         </scenario>
         <scenario name="ParkingCutIn_1" type="ParkingCutIn">
            <trigger_point x="5223.0" y="3962.6" z="160.4" yaw="269.8"/>
         </scenario>
         <scenario name="ParkingCutIn_2" type="ParkingCutIn">
            <trigger_point x="4970.3" y="3453.7" z="157.6" yaw="269.5"/>
         </scenario>
         <scenario name="ParkingCutIn_3" type="ParkingCutIn">
            <trigger_point x="5012.2" y="3333.5" z="156.3" yaw="-0.1"/>
         </scenario>
         <scenario name="ParkingCutIn_4" type="ParkingCutIn">
            <trigger_point x="5221.2" y="3303.3" z="157.1" yaw="269.8"/>
         </scenario>
         <scenario name="StaticCutIn_3" type="StaticCutIn">
            <trigger_point x="4401.2" y="4442.0" z="147.9" yaw="90.3"/>
            <distance value="100"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_4" type="StaticCutIn">
            <trigger_point x="4400.5" y="4603.4" z="147.5" yaw="90.3"/>
            <distance value="109"/>
            <direction value="right"/>
         </scenario>
         <scenario name="HardBreakRoute_3" type="HardBreakRoute">
            <trigger_point x="3381.7" y="5372.7" z="173.8" yaw="177.2"/>
         </scenario>
         <scenario name="HardBreakRoute_4" type="HardBreakRoute">
            <trigger_point x="3575.1" y="4990.0" z="176.7" yaw="1.2"/>
         </scenario>
         <scenario name="HardBreakRoute_5" type="HardBreakRoute">
            <trigger_point x="3136.1" y="4773.2" z="184.4" yaw="270.7"/>
         </scenario>
         <scenario name="HardBreakRoute_6" type="HardBreakRoute">
            <trigger_point x="3229.4" y="4604.4" z="186.4" yaw="1.4"/>
         </scenario>
         <scenario name="HardBreakRoute_7" type="HardBreakRoute">
            <trigger_point x="3314.4" y="4606.4" z="185.6" yaw="1.4"/>
         </scenario>
         <scenario name="HardBreakRoute_8" type="HardBreakRoute">
            <trigger_point x="3842.6" y="4827.8" z="162.6" yaw="76.4"/>
         </scenario>
         <scenario name="HardBreakRoute_9" type="HardBreakRoute">
            <trigger_point x="3671.7" y="5011.4" z="172.3" yaw="96.2"/>
         </scenario>
         <scenario name="ControlLoss_1" type="ControlLoss">
            <trigger_point x="5416.7" y="3580.1" z="158.9" yaw="90.3"/>
         </scenario>
         <scenario name="ControlLoss_2" type="ControlLoss">
            <trigger_point x="5613.3" y="4155.4" z="158.9" yaw="157.1"/>
         </scenario>
         <scenario name="ControlLoss_3" type="ControlLoss">
            <trigger_point x="4597.8" y="3579.9" z="153.0" yaw="113.5"/>
         </scenario>
         <scenario name="ControlLoss_5" type="ControlLoss">
            <trigger_point x="3373.9" y="5373.2" z="173.8" yaw="175.5"/>
         </scenario>
         <scenario name="ControlLoss_6" type="ControlLoss">
            <trigger_point x="3146.4" y="5286.3" z="174.4" yaw="91.2"/>
         </scenario>
         <scenario name="ControlLoss_7" type="ControlLoss">
            <trigger_point x="2794.1" y="5512.1" z="162.5" yaw="-89.1"/>
         </scenario>
         <scenario name="ControlLoss_8" type="ControlLoss">
            <trigger_point x="2653.0" y="4971.1" z="168.5" yaw="1.2"/>
         </scenario>
         <scenario name="ControlLoss_9" type="ControlLoss">
            <trigger_point x="3572.6" y="4989.9" z="176.8" yaw="1.2"/>
         </scenario>
         <scenario name="ControlLoss_10" type="ControlLoss">
            <trigger_point x="3135.8" y="4790.9" z="184.1" yaw="270.7"/>
         </scenario>
         <scenario name="ControlLoss_11" type="ControlLoss">
            <trigger_point x="3235.1" y="4604.5" z="186.4" yaw="1.4"/>
         </scenario>
         <scenario name="ControlLoss_12" type="ControlLoss">
            <trigger_point x="3310.3" y="4606.3" z="185.6" yaw="1.4"/>
         </scenario>
         <scenario name="ControlLoss_13" type="ControlLoss">
            <trigger_point x="3842.2" y="4826.2" z="162.6" yaw="76.4"/>
         </scenario>
         <scenario name="ConstructionObstacle_2" type="ConstructionObstacle">
            <trigger_point x="5377.6" y="4326.7" z="158.3" yaw="153.4"/>
            <distance value="78"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_2" type="VehicleTurningRoutePedestrian">
            <trigger_point x="5222.1" y="3630.8" z="160.1" yaw="269.8"/>
         </scenario>
         <scenario name="StaticCutIn_1" type="StaticCutIn">
            <trigger_point x="5187.9" y="3073.5" z="154.0" yaw="180.3"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_2" type="StaticCutIn">
            <trigger_point x="4949.6" y="3072.4" z="152.0" yaw="180.3"/>
            <distance value="120"/>
            <direction value="right"/>
         </scenario>
         <scenario name="HazardAtSideLane_1" type="HazardAtSideLane">
            <trigger_point x="4540.6" y="3675.1" z="152.3" yaw="126.7"/>
            <distance value="70"/>
            <bicycle_drive_distance value="80"/>
            <bicycle_speed value="11"/>
         </scenario>
         <scenario name="PedestrianCrossing_5" type="PedestrianCrossing">
            <trigger_point x="4420.7" y="3910.2" z="150.1" yaw="105.3"/>
         </scenario>
         <scenario name="HardBreakRoute_1" type="HardBreakRoute">
            <trigger_point x="4398.9" y="4237.4" z="148.7" yaw="90.3"/>
         </scenario>
         <scenario name="HardBreakRoute_2" type="HardBreakRoute">
            <trigger_point x="4398.7" y="4853.6" z="147.3" yaw="92.8"/>
         </scenario>
         <scenario name="StaticCutIn_5" type="StaticCutIn">
            <trigger_point x="4345.0" y="5092.1" z="147.6" yaw="112.6"/>
            <distance value="105"/>
            <direction value="right"/>
         </scenario>
         <scenario name="StaticCutIn_6" type="StaticCutIn">
            <trigger_point x="4220.8" y="5289.9" z="149.2" yaw="131.6"/>
            <distance value="110"/>
            <direction value="right"/>
         </scenario>
         <scenario name="VehicleTurningRoutePedestrian_13" type="VehicleTurningRoutePedestrian">
            <trigger_point x="3727.2" y="4990.4" z="169.5" yaw="181.2"/>
         </scenario>
         <scenario name="VehicleTurningRoute_1" type="VehicleTurningRoute">
            <trigger_point x="3632.4" y="4991.2" z="174.3" yaw="1.2"/>
         </scenario>
      </scenarios>
   </route>
</routes>
