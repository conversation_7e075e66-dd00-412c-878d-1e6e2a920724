#!/bin/bash

# --------------------------------------
# liruiqin: 这个脚本是用来进行端到端模型闭环测试用的.
# 初衷是用来跑我们的预设路径，并用来横向比较和我们其他同学所采集的轨迹间风格上的差异。
# --------------------------------------

# 设置CARLA相关环境变量
export CARLA_ROOT=/home/<USER>/Carla
export CARLA_SERVER=${CARLA_ROOT}/CarlaUE4.sh
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI/carla
export PYTHONPATH=$PYTHONPATH:$CARLA_ROOT/PythonAPI/carla/dist/carla-0.9.15-py3.7-linux-x86_64.egg

# 设置其他相关环境变量
export PYTHONPATH=$PYTHONPATH:leaderboard
export PYTHONPATH=$PYTHONPATH:leaderboard/team_code
export PYTHONPATH=$PYTHONPATH:scenario_runner
export PYTHONPATH=$PYTHONPATH:nav_utils

export LEADERBOARD_ROOT=leaderboard

# 设置重复次数（这里设置为1，表示每条路线只跑一次）
export REPETITIONS=1

# 设置调试挑战级别（这里设置为2，具体含义需根据代码逻辑确定）
export DEBUG_CHALLENGE=2

# 设置挑战赛道代号（这里设置为SENSORS，可能表示使用传感器赛道，具体含义需根据代码逻辑确定）
export CHALLENGE_TRACK_CODENAME=SENSORS

# 设置检查点端点路径（用于保存结果的JSON文件路径）
export CHECKPOINT_ENDPOINT="${LEADERBOARD_ROOT}/results.json"

# 设置记录路径（用于保存测试过程中的相关记录）
export RECORD_PATH=/home/<USER>/Desktop/Evaluation/Bench2Drive/record

# 设置是否恢复（这里为空，表示不恢复之前的测试）
export RESUME=

# 设置团队配置（这里设置为human，可能表示使用人类驾驶配置或特定配置，具体含义需根据代码逻辑确定）
export TEAM_CONFIG=human

# 设置场景运行器根目录
export SCENARIO_RUNNER_ROOT=scenario_runner

# 获取当前时间并格式化为字符串
export current_t=$(date "+%Y%m%d%H%M%S")

# 选择要使用的智能体脚本
# export TEAM_AGENT=$LEADERBOARD_ROOT/leaderboard/autoagents/npc_agent_v2.py
# export agent_name=mock_npc_debug
export TEAM_AGENT=$LEADERBOARD_ROOT/team_code/vad_b2d_agent.py
# export agent_name=vad_agent_debug
# export TEAM_AGENT=/home/<USER>/Desktop/Evaluation/Bench2DriveZoo/team_code/diffusion_drive_agent.py
export agent_name=vad_agent_debug

# 定义路线文件数组
routes=(
    # "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town04_extended.xml"
    # "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town05_extended.xml"
    # "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town05_extended.xml"
    # "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town05_extended.xml"
    # "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town05_extended.xml"

    # # 修改了town05的天气，全都改成大晴天，没有雾，不下雨
    # "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town05_extended_nice_weather.xml"

    # 更精简的debug路径切片
    /home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town05_extended_nice_weather_slice01.xml
)

# 定义对应的路线子集数组（与上面的路线文件一一对应）
route_subsets=(
    # 108
    # 103
    # 103
    # 103
    # 103
    103
)

# 遍历并执行测试
for ((i=0; i<${#routes[@]}; i++)); do
    route=${routes[$i]}
    subset=${route_subsets[$i]}

    export ROUTES=$route
    export ROUTES_SUBSET=$subset
    
    echo "Running test for route: $route, subset: $subset"
    
    # 运行leaderboard评估脚本（使用当前的路线和子集）
    python3 ${LEADERBOARD_ROOT}/leaderboard/leaderboard_evaluator.py \
    --routes="$route" \
    --routes-subset="$subset" \
    --repetitions="$REPETITIONS" \
    --track="$CHALLENGE_TRACK_CODENAME" \
    --checkpoint="$CHECKPOINT_ENDPOINT" \
    --agent="$TEAM_AGENT" \
    --agent-config="$TEAM_CONFIG" \
    --debug="$DEBUG_CHALLENGE" \
    --record="$RECORD_PATH" \
    --resume="$RESUME" \
    --time="$current_t" \
    --person="$agent_name"
done
