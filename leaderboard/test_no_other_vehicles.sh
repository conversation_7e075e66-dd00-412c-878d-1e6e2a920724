 #!/bin/bash
# export PYTHONPATH=$PYTHONPATH:/home/<USER>/Desktop/Evaluation/Bench2Drive/scenario_runner
# export PYTHONPATH=$PYTHONPATH:/home/<USER>/Desktop/Evaluation/Bench2Drive/scenario_runner/srunner/tests/carla_mocks

export CARLA_ROOT=/home/<USER>/Carla/

export CARLA_SERVER=${CARLA_ROOT}/CarlaUE4.sh
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI/carla
export PYTHONPATH=$PYTHONPATH:$CARLA_ROOT/PythonAPI/carla/dist/carla-0.9.15-py3.7-linux-x86_64.egg
export PYTHONPATH=$PYTHONPATH:leaderboard
export PYTHONPATH=$PYTHONPATH:leaderboard/team_code
export PYTHONPATH=$PYTHONPATH:scenario_runner
export PYTHONPATH=$PYTHONPATH:nav_utils

export LEADERBOARD_ROOT=leaderboard

export TEAM_AGENT=$LEADERBOARD_ROOT/leaderboard/autoagents/human_agent.py

# export ROUTES=$LEADERBOARD_ROOT/data/ours_train.xml
# export ROUTES=$LEADERBOARD_ROOT/data/ours_train_extend_1000_50.xml
# export ROUTES=$LEADERBOARD_ROOT/data/ours/Town10HD_expanded_routes_1000.xml
export ROUTES=/home/<USER>/wangjm/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town04_extended.xml
export ROUTES_SUBSET=1
export REPETITIONS=1

export DEBUG_CHALLENGE=2
export CHALLENGE_TRACK_CODENAME=SENSORS
export CHECKPOINT_ENDPOINT="${LEADERBOARD_ROOT}/results.json"
export RECORD_PATH=/home/<USER>/Desktop/Evaluation/Bench2Drive/record
export RESUME=
export TEAM_CONFIG=human
export SCENARIO_RUNNER_ROOT=scenario_runner

#!/bin/bash
export current_t=$(date "+%Y%m%d%H%M%S")


python3 ${LEADERBOARD_ROOT}/leaderboard/leaderboard_evaluator.py \
--routes=${ROUTES} \
--routes-subset=${ROUTES_SUBSET} \
--repetitions=${REPETITIONS} \
--track=${CHALLENGE_TRACK_CODENAME} \
--checkpoint=${CHECKPOINT_ENDPOINT} \
--debug-checkpoint=${DEBUG_CHECKPOINT_ENDPOINT} \
--agent=${TEAM_AGENT} \
--agent-config=${TEAM_CONFIG} \
--debug=${DEBUG_CHALLENGE} \
--record=${RECORD_PATH} \
--resume=${RESUME} \
--person=none \
--no-other-vehicles \
--time=${current_t} "$@"

#bash leaderboard/run_leaderboard.sh --person try --routes-subset 108