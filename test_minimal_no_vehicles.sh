#!/bin/bash

# Minimal test script with reduced memory usage
export CARLA_ROOT=/home/<USER>/Carla/
export CARLA_SERVER=${CARLA_ROOT}/CarlaUE4.sh
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI/carla
export PYTHONPATH=$PYTHONPATH:$CARLA_ROOT/PythonAPI/carla/dist/carla-0.9.15-py3.7-linux-x86_64.egg
export PYTHONPATH=$PYTHONPATH:leaderboard
export PYTHONPATH=$PYTHONPATH:scenario_runner

export LEADERBOARD_ROOT=leaderboard
export TEAM_AGENT=$LEADERBOARD_ROOT/leaderboard/autoagents/human_agent.py
export ROUTES=/home/<USER>/wangjm/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town04_extended.xml
export ROUTES_SUBSET=108
export REPETITIONS=1
export DEBUG_CHALLENGE=0  # Reduced debug level to save memory
export CHALLENGE_TRACK_CODENAME=SENSORS
export CHECKPOINT_ENDPOINT="${LEADERBOARD_ROOT}/results_minimal.json"
export TEAM_CONFIG=human
export SCENARIO_RUNNER_ROOT=scenario_runner

export current_t=$(date "+%Y%m%d%H%M%S")

echo "Running minimal test with --no-other-vehicles flag..."
echo "Debug level reduced to save memory"
echo "BackgroundBehavior will be disabled via environment variable"

# Run with minimal options to reduce memory usage
python3 ${LEADERBOARD_ROOT}/leaderboard/leaderboard_evaluator.py \
--routes=${ROUTES} \
--routes-subset=${ROUTES_SUBSET} \
--repetitions=${REPETITIONS} \
--track=${CHALLENGE_TRACK_CODENAME} \
--checkpoint=${CHECKPOINT_ENDPOINT} \
--agent=${TEAM_AGENT} \
--agent-config=${TEAM_CONFIG} \
--debug=${DEBUG_CHALLENGE} \
--person=test_minimal \
--no-other-vehicles \
--time=${current_t} \
"$@"
