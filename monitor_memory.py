#!/usr/bin/env python3

"""
Simple memory monitoring script
"""

import psutil
import time
import sys

def monitor_memory(duration=60):
    """Monitor memory usage for a given duration"""
    print("=== Memory Monitor ===")
    print(f"Monitoring for {duration} seconds...")
    
    start_time = time.time()
    max_memory = 0
    
    while time.time() - start_time < duration:
        # Get system memory info
        memory = psutil.virtual_memory()
        used_gb = memory.used / (1024**3)
        total_gb = memory.total / (1024**3)
        percent = memory.percent
        
        max_memory = max(max_memory, used_gb)
        
        print(f"Memory: {used_gb:.1f}GB / {total_gb:.1f}GB ({percent:.1f}%)")
        
        # Check if memory is getting too high
        if percent > 90:
            print("⚠️  WARNING: Memory usage is very high!")
        
        time.sleep(5)
    
    print(f"\nMax memory used: {max_memory:.1f}GB")
    
    # Check for processes using a lot of memory
    print("\nTop memory-consuming processes:")
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
        try:
            processes.append((proc.info['pid'], proc.info['name'], proc.info['memory_info'].rss))
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    # Sort by memory usage
    processes.sort(key=lambda x: x[2], reverse=True)
    
    for i, (pid, name, memory) in enumerate(processes[:10]):
        memory_mb = memory / (1024**2)
        print(f"{i+1}. {name} (PID: {pid}): {memory_mb:.1f}MB")

if __name__ == '__main__':
    duration = int(sys.argv[1]) if len(sys.argv) > 1 else 60
    monitor_memory(duration)
