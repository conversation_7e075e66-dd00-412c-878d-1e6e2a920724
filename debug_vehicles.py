#!/usr/bin/env python3

"""
Debug script to check how many vehicles are in the CARLA world
"""

import carla
import time

def main():
    # Connect to CARLA
    client = carla.Client('localhost', 2000)
    client.set_timeout(10.0)
    
    try:
        world = client.get_world()
        
        print("=== CARLA World Vehicle Debug ===")
        print(f"Current map: {world.get_map().name}")
        
        # Get all actors
        all_actors = world.get_actors()
        print(f"Total actors in world: {len(all_actors)}")
        
        # Get vehicles
        vehicles = all_actors.filter('vehicle.*')
        print(f"Total vehicles: {len(vehicles)}")
        
        # Categorize vehicles
        ego_vehicles = []
        other_vehicles = []
        
        for vehicle in vehicles:
            role_name = vehicle.attributes.get('role_name', 'unknown')
            print(f"Vehicle ID: {vehicle.id}, Type: {vehicle.type_id}, Role: {role_name}")
            
            if role_name == 'hero':
                ego_vehicles.append(vehicle)
            else:
                other_vehicles.append(vehicle)
        
        print(f"\nEgo vehicles: {len(ego_vehicles)}")
        print(f"Other vehicles: {len(other_vehicles)}")
        
        # Get static meshes that might be vehicles
        static_meshes = all_actors.filter('*static.prop.mesh*')
        vehicle_meshes = []
        
        for mesh in static_meshes:
            mesh_path = mesh.attributes.get('mesh_path', '')
            if any(vehicle_type in mesh_path for vehicle_type in ['Car', 'Truck', 'Bus', 'Motorcycle', 'Bicycle']):
                vehicle_meshes.append(mesh)
                print(f"Static vehicle mesh: {mesh.id}, Path: {mesh_path}")
        
        print(f"Static vehicle meshes: {len(vehicle_meshes)}")
        
        # Get walkers
        walkers = all_actors.filter('walker.*')
        print(f"Walkers (pedestrians): {len(walkers)}")
        
        # Get walker controllers
        walker_controllers = all_actors.filter('controller.ai.walker')
        print(f"Walker controllers: {len(walker_controllers)}")
        
        print("\n=== Summary ===")
        print(f"Ego vehicles: {len(ego_vehicles)}")
        print(f"Other vehicles: {len(other_vehicles)}")
        print(f"Static vehicle meshes: {len(vehicle_meshes)}")
        print(f"Walkers: {len(walkers)}")
        print(f"Walker controllers: {len(walker_controllers)}")
        
        total_moving_objects = len(other_vehicles) + len(vehicle_meshes) + len(walkers)
        print(f"Total non-ego moving objects: {total_moving_objects}")
        
        if total_moving_objects == 0:
            print("✅ SUCCESS: Only ego vehicle(s) present!")
        else:
            print("❌ ISSUE: Other vehicles/objects are present")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    main()
