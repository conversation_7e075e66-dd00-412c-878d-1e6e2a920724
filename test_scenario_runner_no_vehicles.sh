#!/bin/bash

# Test script to run scenario_runner with --no-other-vehicles flag

export SCENARIO_RUNNER_ROOT=/home/<USER>/wangjm/Bench2Drive/scenario_runner

echo "Testing scenario_runner with --no-other-vehicles flag..."
echo "This should only spawn the ego vehicle and no other vehicles or scenarios."

# Test with a simple route scenario
python3 ${SCENARIO_RUNNER_ROOT}/scenario_runner.py \
--route ${SCENARIO_RUNNER_ROOT}/srunner/examples/routes_training.xml \
--route-id 0 \
--agent ${SCENARIO_RUNNER_ROOT}/srunner/autoagents/human_agent.py \
--debug \
--no-other-vehicles \
"$@"
