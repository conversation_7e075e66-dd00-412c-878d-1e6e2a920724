Failed to stop the scenario, the statistics might be empty:

Traceback (most recent call last):
  File "leaderboard/leaderboard/leaderboard_evaluator.py", line 463, in _load_and_run_scenario
    self.manager.stop_scenario()
  File "/home/<USER>/wangjm/Bench2Drive/leaderboard/leaderboard/scenarios/scenario_manager.py", line 268, in stop_scenario
    self.scenario.terminate()
  File "/home/<USER>/wangjm/Bench2Drive/scenario_runner/srunner/scenarios/basic_scenario.py", line 311, in terminate
    node.terminate(py_trees.common.Status.INVALID)
  File "/home/<USER>/wangjm/Bench2Drive/scenario_runner/srunner/scenarios/background_activity.py", line 365, in terminate
    all_actors = list(self._actors_speed_perc)
AttributeError: 'BackgroundBehavior' object has no attribute '_actors_speed_perc'
