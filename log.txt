/home/<USER>/Desktop/carla0915/CarlaUE4.sh -RenderOffScreen -nosound -carla-rpc-port=2003 -graphicsadapter=0 None
load_world success , attempts=0
traffic_manager init success, try_time=0
pygame 2.6.1 (SDL 2.28.4, Python 3.8.20)
Hello from the pygame community. https://www.pygame.org/contribute.html

[1m========= Preparing RouteScenario_1 (repetition 0) =========[0m
[1m> Loading the world[0m
0 interpolate_trajectory 52
1 interpolate_trajectory 51
2 interpolate_trajectory 49
3 interpolate_trajectory 50
4 interpolate_trajectory 51
5 interpolate_trajectory 50
6 interpolate_trajectory 49
7 interpolate_trajectory 50
8 interpolate_trajectory 51
[93mSkipping scenario 'None' due to setup error: 'None'

Traceback (most recent call last):
  File "/home/<USER>/Desktop/Evaluation/Bench2Drive/leaderboard/leaderboard/scenarios/route_scenario.py", line 317, in build_scenarios
    scenario_class = self.all_scenario_classes[scenario_config.type]
KeyError: 'None'

[0m[1m> Setting up the agent[0m
self.route_scenario.route: [(<carla.libcarla.Transform object at 0x729255f04f40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x729255f04ec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x729255f04e40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x729255f04d40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x729255f04dc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x729255f04bc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x729255f04c40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x729255f04cc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x729255f04ac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72927067bcc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72927067bec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72926bdf7940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72926bdf7840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb0c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb1c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72927063a240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x729255f04b40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb2c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb3c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb4c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb5c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb6c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb7c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb8c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfb9c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfba40>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbac0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbb40>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbbc0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbc40>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbcc0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbd40>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbdc0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbe40>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbec0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfbf40>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfc040>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfc0c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfc140>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfc1c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfc240>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfc2c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdfc340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc3c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc4c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc5c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc6c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc7c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc8c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfc9c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfca40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfcac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfcb40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfcbc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfcc40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfccc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfcd40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfcdc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfce40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfcec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfcf40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdff040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdff0c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdff140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdff1c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdff240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdff2c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdff340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdff3c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff440>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff4c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff540>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff5c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff640>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff6c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff740>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff7c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff840>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff8c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff940>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdff9c0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdffa40>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdffac0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdffb40>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdffbc0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdffc40>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdffcc0>, <RoadOption.RIGHT: 2>), (<carla.libcarla.Transform object at 0x72924bdffd40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdffdc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdffe40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdffec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfff40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa0c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa1c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa2c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa3c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa4c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa5c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa6c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa7c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa8c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfa9c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfaa40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfaac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfab40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfabc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfac40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfacc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfad40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfadc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfae40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfaec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdfaf40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef0c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef1c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef2c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef3c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef4c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef5c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef6c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef7c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef8c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdef9c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefa40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefb40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefbc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefc40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefcc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefd40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefdc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefe40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdefec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdeff40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf80c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf81c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf82c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf83c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf84c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf85c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf86c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf87c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf88c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf89c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8a40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8ac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8b40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8bc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8c40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8cc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8d40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8dc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8e40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdf8ec0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdf8f40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdd8040>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdd80c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd81c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd82c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd83c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd84c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd85c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd86c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd87c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd88c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd89c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8a40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8ac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8b40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8bc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8c40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8cc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8d40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8dc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8e40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8ec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd8f40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde80c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde81c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde82c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde83c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde84c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde85c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde86c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde87c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde88c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde89c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8a40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8ac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8b40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8bc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8c40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8cc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8d40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8dc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8e40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8ec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde8f40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde40c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde41c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde42c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde43c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde44c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde45c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde46c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde47c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde48c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde49c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4a40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4ac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4b40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4bc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4c40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4cc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4d40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4dc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4e40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4ec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bde4f40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc90c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc91c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc92c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc93c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc94c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc95c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc96c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc97c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc98c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc99c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9a40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9ac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9b40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9bc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9c40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9cc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9d40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9dc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9e40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9ec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdc9f40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce0c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce1c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce2c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce3c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce4c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce5c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce6c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce7c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce8c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdce9c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdcea40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdceac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdceb40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdcebc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdcec40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdcecc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdced40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdcedc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdcee40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdceec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdcef40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd60c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd61c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd62c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd63c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd64c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd65c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd66c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd67c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd68c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd69c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6a40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6ac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6b40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6bc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6c40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6cc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6d40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6dc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6e40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6ec0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd6f40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9040>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd90c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9140>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd91c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9240>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd92c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9340>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd93c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9440>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd94c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9540>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd95c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9640>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd96c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9740>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd97c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9840>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd98c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9940>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd99c0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9a40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9ac0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9b40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9bc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9c40>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9cc0>, <RoadOption.LANEFOLLOW: 4>), (<carla.libcarla.Transform object at 0x72924bdd9d40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdd9dc0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdd9e40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdd9ec0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdd9f40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf040>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf0c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf140>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf1c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf240>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf2c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf340>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf3c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf440>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf4c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf540>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf5c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf640>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf6c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf740>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf7c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf840>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf8c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf940>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcf9c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfa40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfac0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfb40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfbc0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfc40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfcc0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfd40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfdc0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfe40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcfec0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdcff40>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdc2040>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdc20c0>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdc2140>, <RoadOption.STRAIGHT: 3>), (<carla.libcarla.Transform object at 0x72924bdc21c0>, <RoadOption.STRAIGHT: 3>)]
save_name RouteScenario_1_rep0_Town10HD_None_8_05_09_11_05_33
args.agent_config human+RouteScenario_1_rep0_Town10HD_None_8_05_09_11_05_33
[1m> Running the route[0m
start recording
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 448
self._waypoints_queue: 447
self._waypoints_queue: 447
self._waypoints_queue: 447
self._waypoints_queue: 447
self._waypoints_queue: 447
self._waypoints_queue: 447
self._waypoints_queue: 447
self._waypoints_queue: 446
self._waypoints_queue: 446
self._waypoints_queue: 446
self._waypoints_queue: 446
self._waypoints_queue: 446
self._waypoints_queue: 446
self._waypoints_queue: 445
self._waypoints_queue: 445
self._waypoints_queue: 445
self._waypoints_queue: 445
self._waypoints_queue: 444
self._waypoints_queue: 444
self._waypoints_queue: 444
self._waypoints_queue: 444
self._waypoints_queue: 443
self._waypoints_queue: 443
self._waypoints_queue: 443
self._waypoints_queue: 443
self._waypoints_queue: 442
self._waypoints_queue: 442
self._waypoints_queue: 442
self._waypoints_queue: 442
self._waypoints_queue: 441
self._waypoints_queue: 441
self._waypoints_queue: 441
self._waypoints_queue: 441
self._waypoints_queue: 441
self._waypoints_queue: 440
self._waypoints_queue: 440
self._waypoints_queue: 440
self._waypoints_queue: 440
self._waypoints_queue: 439
self._waypoints_queue: 439
self._waypoints_queue: 439
self._waypoints_queue: 439
self._waypoints_queue: 439
self._waypoints_queue: 438
self._waypoints_queue: 438
self._waypoints_queue: 438
self._waypoints_queue: 438
self._waypoints_queue: 438
self._waypoints_queue: 437
self._waypoints_queue: 437
self._waypoints_queue: 437
self._waypoints_queue: 437
self._waypoints_queue: 437
self._waypoints_queue: 437
self._waypoints_queue: 435
self._waypoints_queue: 435
self._waypoints_queue: 435
self._waypoints_queue: 435
self._waypoints_queue: 434
self._waypoints_queue: 434
self._waypoints_queue: 434
self._waypoints_queue: 434
self._waypoints_queue: 434
self._waypoints_queue: 433
self._waypoints_queue: 433
self._waypoints_queue: 433
self._waypoints_queue: 433
self._waypoints_queue: 432
self._waypoints_queue: 432
self._waypoints_queue: 432
self._waypoints_queue: 432
self._waypoints_queue: 431
self._waypoints_queue: 431
self._waypoints_queue: 431
self._waypoints_queue: 431
self._waypoints_queue: 431
self._waypoints_queue: 430
self._waypoints_queue: 430
self._waypoints_queue: 430
self._waypoints_queue: 430
self._waypoints_queue: 429
self._waypoints_queue: 429
self._waypoints_queue: 429
self._waypoints_queue: 429
self._waypoints_queue: 428
self._waypoints_queue: 428
self._waypoints_queue: 428
self._waypoints_queue: 428
self._waypoints_queue: 427
self._waypoints_queue: 427
self._waypoints_queue: 427
self._waypoints_queue: 427
self._waypoints_queue: 426
self._waypoints_queue: 426
self._waypoints_queue: 426
self._waypoints_queue: 426
self._waypoints_queue: 426
self._waypoints_queue: 425
self._waypoints_queue: 425
self._waypoints_queue: 425
self._waypoints_queue: 425
self._waypoints_queue: 424
self._waypoints_queue: 424
self._waypoints_queue: 424
self._waypoints_queue: 424
self._waypoints_queue: 424
self._waypoints_queue: 424
self._waypoints_queue: 422
self._waypoints_queue: 422
self._waypoints_queue: 422
self._waypoints_queue: 422
self._waypoints_queue: 421
self._waypoints_queue: 421
self._waypoints_queue: 421
self._waypoints_queue: 421
self._waypoints_queue: 420
self._waypoints_queue: 420
self._waypoints_queue: 420
self._waypoints_queue: 420
self._waypoints_queue: 420
self._waypoints_queue: 420
self._waypoints_queue: 420
self._waypoints_queue: 420
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 418
self._waypoints_queue: 417
self._waypoints_queue: 417
self._waypoints_queue: 417
self._waypoints_queue: 417
self._waypoints_queue: 417
self._waypoints_queue: 417
self._waypoints_queue: 417
self._waypoints_queue: 416
self._waypoints_queue: 416
self._waypoints_queue: 416
self._waypoints_queue: 416
self._waypoints_queue: 416
self._waypoints_queue: 415
self._waypoints_queue: 415
self._waypoints_queue: 415
self._waypoints_queue: 414
self._waypoints_queue: 414
self._waypoints_queue: 414
self._waypoints_queue: 413
self._waypoints_queue: 413
self._waypoints_queue: 413
self._waypoints_queue: 412
self._waypoints_queue: 412
self._waypoints_queue: 411
self._waypoints_queue: 411
self._waypoints_queue: 411
self._waypoints_queue: 410
self._waypoints_queue: 410
self._waypoints_queue: 410
self._waypoints_queue: 409
self._waypoints_queue: 409
self._waypoints_queue: 408
self._waypoints_queue: 408
self._waypoints_queue: 408
self._waypoints_queue: 407
self._waypoints_queue: 407
self._waypoints_queue: 406
self._waypoints_queue: 406
self._waypoints_queue: 405
self._waypoints_queue: 404
self._waypoints_queue: 404
self._waypoints_queue: 403
self._waypoints_queue: 402
self._waypoints_queue: 402
self._waypoints_queue: 401
self._waypoints_queue: 401
self._waypoints_queue: 400
self._waypoints_queue: 399
self._waypoints_queue: 399
self._waypoints_queue: 398
self._waypoints_queue: 397
self._waypoints_queue: 397
self._waypoints_queue: 396
self._waypoints_queue: 395
self._waypoints_queue: 395
self._waypoints_queue: 394
self._waypoints_queue: 394
self._waypoints_queue: 393
self._waypoints_queue: 393
self._waypoints_queue: 393
self._waypoints_queue: 391
self._waypoints_queue: 391
self._waypoints_queue: 391
self._waypoints_queue: 390
self._waypoints_queue: 390
self._waypoints_queue: 390
self._waypoints_queue: 389
self._waypoints_queue: 389
self._waypoints_queue: 389
self._waypoints_queue: 388
self._waypoints_queue: 388
self._waypoints_queue: 388
self._waypoints_queue: 388
self._waypoints_queue: 387
self._waypoints_queue: 387
self._waypoints_queue: 387
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 386
self._waypoints_queue: 385
self._waypoints_queue: 385
self._waypoints_queue: 385
self._waypoints_queue: 385
self._waypoints_queue: 385
self._waypoints_queue: 385
self._waypoints_queue: 385
self._waypoints_queue: 384
self._waypoints_queue: 384
self._waypoints_queue: 384
self._waypoints_queue: 384
self._waypoints_queue: 383
self._waypoints_queue: 383
self._waypoints_queue: 383
self._waypoints_queue: 383
self._waypoints_queue: 382
self._waypoints_queue: 382
self._waypoints_queue: 382
self._waypoints_queue: 381
self._waypoints_queue: 381
self._waypoints_queue: 381
self._waypoints_queue: 381
self._waypoints_queue: 381
self._waypoints_queue: 379
self._waypoints_queue: 379
self._waypoints_queue: 379
self._waypoints_queue: 379
self._waypoints_queue: 378
self._waypoints_queue: 378
self._waypoints_queue: 378
self._waypoints_queue: 378
self._waypoints_queue: 377
self._waypoints_queue: 377
self._waypoints_queue: 376
self._waypoints_queue: 376
self._waypoints_queue: 376
self._waypoints_queue: 375
self._waypoints_queue: 375
self._waypoints_queue: 374
self._waypoints_queue: 374
self._waypoints_queue: 373
self._waypoints_queue: 373
self._waypoints_queue: 373
self._waypoints_queue: 372
self._waypoints_queue: 372
self._waypoints_queue: 371
self._waypoints_queue: 371
self._waypoints_queue: 370
self._waypoints_queue: 370
self._waypoints_queue: 370
self._waypoints_queue: 370
self._waypoints_queue: 368
self._waypoints_queue: 368
self._waypoints_queue: 367
self._waypoints_queue: 367
self._waypoints_queue: 366
self._waypoints_queue: 366
self._waypoints_queue: 365
self._waypoints_queue: 365
self._waypoints_queue: 364
self._waypoints_queue: 364
self._waypoints_queue: 364
self._waypoints_queue: 363
self._waypoints_queue: 363
self._waypoints_queue: 362
self._waypoints_queue: 362
self._waypoints_queue: 361
self._waypoints_queue: 361
self._waypoints_queue: 361
self._waypoints_queue: 360
self._waypoints_queue: 360
self._waypoints_queue: 359
self._waypoints_queue: 359
self._waypoints_queue: 359
self._waypoints_queue: 358
self._waypoints_queue: 358
self._waypoints_queue: 357
self._waypoints_queue: 357
self._waypoints_queue: 357
self._waypoints_queue: 356
self._waypoints_queue: 356
self._waypoints_queue: 355
self._waypoints_queue: 355
self._waypoints_queue: 355
self._waypoints_queue: 354
self._waypoints_queue: 354
self._waypoints_queue: 354
self._waypoints_queue: 353
self._waypoints_queue: 353
self._waypoints_queue: 352
self._waypoints_queue: 352
self._waypoints_queue: 351
self._waypoints_queue: 351
self._waypoints_queue: 350
self._waypoints_queue: 350
self._waypoints_queue: 350
self._waypoints_queue: 349
self._waypoints_queue: 349
self._waypoints_queue: 348
self._waypoints_queue: 347
self._waypoints_queue: 346
self._waypoints_queue: 345
self._waypoints_queue: 343
self._waypoints_queue: 342
self._waypoints_queue: 341
self._waypoints_queue: 341
self._waypoints_queue: 340
self._waypoints_queue: 340
self._waypoints_queue: 338
self._waypoints_queue: 337
self._waypoints_queue: 337
self._waypoints_queue: 336
self._waypoints_queue: 335
self._waypoints_queue: 334
self._waypoints_queue: 334
self._waypoints_queue: 333
self._waypoints_queue: 332
self._waypoints_queue: 332
self._waypoints_queue: 331
self._waypoints_queue: 330
self._waypoints_queue: 330
self._waypoints_queue: 329
self._waypoints_queue: 328
self._waypoints_queue: 328
self._waypoints_queue: 327
self._waypoints_queue: 326
self._waypoints_queue: 326
self._waypoints_queue: 325
self._waypoints_queue: 324
self._waypoints_queue: 324
self._waypoints_queue: 323
self._waypoints_queue: 322
self._waypoints_queue: 322
self._waypoints_queue: 321
self._waypoints_queue: 321
self._waypoints_queue: 320
self._waypoints_queue: 319
self._waypoints_queue: 319
self._waypoints_queue: 318
self._waypoints_queue: 318
self._waypoints_queue: 317
self._waypoints_queue: 317
self._waypoints_queue: 316
self._waypoints_queue: 316
self._waypoints_queue: 315
self._waypoints_queue: 315
self._waypoints_queue: 314
self._waypoints_queue: 314
self._waypoints_queue: 314
self._waypoints_queue: 313
self._waypoints_queue: 313
self._waypoints_queue: 313
self._waypoints_queue: 312
self._waypoints_queue: 312
self._waypoints_queue: 312
self._waypoints_queue: 312
self._waypoints_queue: 311
self._waypoints_queue: 311
self._waypoints_queue: 310
self._waypoints_queue: 310
self._waypoints_queue: 310
self._waypoints_queue: 309
self._waypoints_queue: 309
self._waypoints_queue: 308
self._waypoints_queue: 308
self._waypoints_queue: 307
self._waypoints_queue: 307
self._waypoints_queue: 307
self._waypoints_queue: 306
self._waypoints_queue: 306
self._waypoints_queue: 305
self._waypoints_queue: 304
self._waypoints_queue: 304
self._waypoints_queue: 303
self._waypoints_queue: 303
self._waypoints_queue: 302
self._waypoints_queue: 302
self._waypoints_queue: 301
self._waypoints_queue: 301
self._waypoints_queue: 301
self._waypoints_queue: 300
self._waypoints_queue: 300
self._waypoints_queue: 299
self._waypoints_queue: 298
self._waypoints_queue: 298
self._waypoints_queue: 297
self._waypoints_queue: 297
self._waypoints_queue: 296
self._waypoints_queue: 296
self._waypoints_queue: 295
self._waypoints_queue: 295
self._waypoints_queue: 294
self._waypoints_queue: 294
self._waypoints_queue: 293
self._waypoints_queue: 293
self._waypoints_queue: 292
self._waypoints_queue: 292
self._waypoints_queue: 292
self._waypoints_queue: 291
self._waypoints_queue: 291
self._waypoints_queue: 290
self._waypoints_queue: 290
self._waypoints_queue: 290
self._waypoints_queue: 289
self._waypoints_queue: 289
self._waypoints_queue: 289
self._waypoints_queue: 288
self._waypoints_queue: 288
self._waypoints_queue: 288
self._waypoints_queue: 288
self._waypoints_queue: 288
self._waypoints_queue: 288
self._waypoints_queue: 288
self._waypoints_queue: 288
self._waypoints_queue: 288
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 287
self._waypoints_queue: 286
self._waypoints_queue: 286
self._waypoints_queue: 286
self._waypoints_queue: 286
self._waypoints_queue: 285
self._waypoints_queue: 285
self._waypoints_queue: 285
self._waypoints_queue: 284
self._waypoints_queue: 284
self._waypoints_queue: 283
self._waypoints_queue: 283
self._waypoints_queue: 283
self._waypoints_queue: 282
self._waypoints_queue: 282
self._waypoints_queue: 282
self._waypoints_queue: 281
self._waypoints_queue: 281
self._waypoints_queue: 280
self._waypoints_queue: 280
self._waypoints_queue: 280
self._waypoints_queue: 280
self._waypoints_queue: 280
self._waypoints_queue: 279
self._waypoints_queue: 279
self._waypoints_queue: 279
self._waypoints_queue: 279
self._waypoints_queue: 278
self._waypoints_queue: 277
self._waypoints_queue: 277
self._waypoints_queue: 276
self._waypoints_queue: 276
self._waypoints_queue: 275
self._waypoints_queue: 275
self._waypoints_queue: 274
self._waypoints_queue: 274
self._waypoints_queue: 273
self._waypoints_queue: 273
self._waypoints_queue: 272
self._waypoints_queue: 272
self._waypoints_queue: 271
self._waypoints_queue: 270
self._waypoints_queue: 270
self._waypoints_queue: 269
self._waypoints_queue: 269
self._waypoints_queue: 268
self._waypoints_queue: 267
self._waypoints_queue: 267
self._waypoints_queue: 266
self._waypoints_queue: 265
self._waypoints_queue: 265
self._waypoints_queue: 264
self._waypoints_queue: 263
self._waypoints_queue: 263
self._waypoints_queue: 262
self._waypoints_queue: 262
self._waypoints_queue: 262
self._waypoints_queue: 261
self._waypoints_queue: 260
self._waypoints_queue: 260
self._waypoints_queue: 259
self._waypoints_queue: 258
self._waypoints_queue: 258
self._waypoints_queue: 257
self._waypoints_queue: 256
self._waypoints_queue: 255
self._waypoints_queue: 255
self._waypoints_queue: 253
self._waypoints_queue: 253
self._waypoints_queue: 252
self._waypoints_queue: 251
self._waypoints_queue: 251
self._waypoints_queue: 250
self._waypoints_queue: 250
self._waypoints_queue: 249
self._waypoints_queue: 249
self._waypoints_queue: 248
self._waypoints_queue: 248
self._waypoints_queue: 247
self._waypoints_queue: 247
self._waypoints_queue: 246
self._waypoints_queue: 246
self._waypoints_queue: 246
self._waypoints_queue: 245
self._waypoints_queue: 245
self._waypoints_queue: 245
self._waypoints_queue: 244
self._waypoints_queue: 244
self._waypoints_queue: 244
self._waypoints_queue: 243
self._waypoints_queue: 243
self._waypoints_queue: 243
self._waypoints_queue: 242
self._waypoints_queue: 242
self._waypoints_queue: 242
self._waypoints_queue: 241
self._waypoints_queue: 241
self._waypoints_queue: 241
self._waypoints_queue: 240
self._waypoints_queue: 240
self._waypoints_queue: 240
self._waypoints_queue: 239
self._waypoints_queue: 239
self._waypoints_queue: 239
self._waypoints_queue: 239
self._waypoints_queue: 238
self._waypoints_queue: 238
self._waypoints_queue: 238
self._waypoints_queue: 237
self._waypoints_queue: 237
self._waypoints_queue: 236
self._waypoints_queue: 236
self._waypoints_queue: 236
self._waypoints_queue: 235
self._waypoints_queue: 235
self._waypoints_queue: 234
self._waypoints_queue: 234
self._waypoints_queue: 234
self._waypoints_queue: 233
self._waypoints_queue: 233
self._waypoints_queue: 232
self._waypoints_queue: 232
self._waypoints_queue: 231
self._waypoints_queue: 231
self._waypoints_queue: 230
self._waypoints_queue: 230
self._waypoints_queue: 229
self._waypoints_queue: 229
self._waypoints_queue: 228
self._waypoints_queue: 228
self._waypoints_queue: 227
self._waypoints_queue: 227
self._waypoints_queue: 226
self._waypoints_queue: 226
self._waypoints_queue: 225
self._waypoints_queue: 225
self._waypoints_queue: 224
self._waypoints_queue: 224
self._waypoints_queue: 223
self._waypoints_queue: 223
self._waypoints_queue: 222
self._waypoints_queue: 222
self._waypoints_queue: 221
self._waypoints_queue: 220
self._waypoints_queue: 219
self._waypoints_queue: 219
self._waypoints_queue: 218
self._waypoints_queue: 218
self._waypoints_queue: 217
self._waypoints_queue: 216
self._waypoints_queue: 216
self._waypoints_queue: 215
self._waypoints_queue: 215
self._waypoints_queue: 213
self._waypoints_queue: 213
self._waypoints_queue: 212
self._waypoints_queue: 212
self._waypoints_queue: 211
self._waypoints_queue: 211
self._waypoints_queue: 210
self._waypoints_queue: 210
self._waypoints_queue: 209
self._waypoints_queue: 209
self._waypoints_queue: 208
self._waypoints_queue: 208
self._waypoints_queue: 207
self._waypoints_queue: 207
self._waypoints_queue: 206
self._waypoints_queue: 205
self._waypoints_queue: 205
self._waypoints_queue: 204
self._waypoints_queue: 204
self._waypoints_queue: 203
self._waypoints_queue: 202
self._waypoints_queue: 202
self._waypoints_queue: 201
self._waypoints_queue: 201
self._waypoints_queue: 200
self._waypoints_queue: 199
self._waypoints_queue: 199
self._waypoints_queue: 198
self._waypoints_queue: 197
self._waypoints_queue: 197
self._waypoints_queue: 196
self._waypoints_queue: 196
self._waypoints_queue: 195
self._waypoints_queue: 194
self._waypoints_queue: 194
self._waypoints_queue: 193
self._waypoints_queue: 193
self._waypoints_queue: 192
self._waypoints_queue: 192
self._waypoints_queue: 192
self._waypoints_queue: 190
self._waypoints_queue: 190
self._waypoints_queue: 189
self._waypoints_queue: 189
self._waypoints_queue: 188
self._waypoints_queue: 187
self._waypoints_queue: 187
self._waypoints_queue: 186
self._waypoints_queue: 186
self._waypoints_queue: 185
self._waypoints_queue: 185
self._waypoints_queue: 184
self._waypoints_queue: 184
self._waypoints_queue: 183
self._waypoints_queue: 183
self._waypoints_queue: 183
self._waypoints_queue: 182
self._waypoints_queue: 182
self._waypoints_queue: 181
self._waypoints_queue: 181
self._waypoints_queue: 180
self._waypoints_queue: 180
self._waypoints_queue: 179
self._waypoints_queue: 179
self._waypoints_queue: 179
self._waypoints_queue: 179
self._waypoints_queue: 177
self._waypoints_queue: 177
self._waypoints_queue: 176
self._waypoints_queue: 176
self._waypoints_queue: 175
self._waypoints_queue: 175
self._waypoints_queue: 174
self._waypoints_queue: 173
self._waypoints_queue: 173
self._waypoints_queue: 172
self._waypoints_queue: 172
self._waypoints_queue: 171
self._waypoints_queue: 170
self._waypoints_queue: 170
self._waypoints_queue: 169
self._waypoints_queue: 168
self._waypoints_queue: 167
self._waypoints_queue: 167
self._waypoints_queue: 166
self._waypoints_queue: 165
self._waypoints_queue: 165
self._waypoints_queue: 164
self._waypoints_queue: 163
self._waypoints_queue: 162
self._waypoints_queue: 161
self._waypoints_queue: 161
self._waypoints_queue: 160
self._waypoints_queue: 159
self._waypoints_queue: 158
self._waypoints_queue: 157
self._waypoints_queue: 157
self._waypoints_queue: 156
self._waypoints_queue: 155
self._waypoints_queue: 154
self._waypoints_queue: 153
self._waypoints_queue: 152
self._waypoints_queue: 152
self._waypoints_queue: 151
self._waypoints_queue: 150
self._waypoints_queue: 150
self._waypoints_queue: 149
self._waypoints_queue: 148
self._waypoints_queue: 147
self._waypoints_queue: 147
self._waypoints_queue: 146
self._waypoints_queue: 145
self._waypoints_queue: 144
self._waypoints_queue: 143
self._waypoints_queue: 143
self._waypoints_queue: 142
self._waypoints_queue: 141
self._waypoints_queue: 140
self._waypoints_queue: 140
self._waypoints_queue: 139
self._waypoints_queue: 138
self._waypoints_queue: 137
self._waypoints_queue: 137
self._waypoints_queue: 136
self._waypoints_queue: 135
self._waypoints_queue: 134
self._waypoints_queue: 134
self._waypoints_queue: 133
self._waypoints_queue: 132
self._waypoints_queue: 132
self._waypoints_queue: 131
self._waypoints_queue: 130
self._waypoints_queue: 130
self._waypoints_queue: 129
self._waypoints_queue: 128
self._waypoints_queue: 127
self._waypoints_queue: 127
self._waypoints_queue: 126
self._waypoints_queue: 125
self._waypoints_queue: 125
self._waypoints_queue: 124
self._waypoints_queue: 123
self._waypoints_queue: 122
self._waypoints_queue: 122
self._waypoints_queue: 121
self._waypoints_queue: 120
self._waypoints_queue: 119
self._waypoints_queue: 118
self._waypoints_queue: 117
self._waypoints_queue: 116
self._waypoints_queue: 115
self._waypoints_queue: 115
self._waypoints_queue: 114
self._waypoints_queue: 113
self._waypoints_queue: 112
self._waypoints_queue: 111
self._waypoints_queue: 110
self._waypoints_queue: 109
self._waypoints_queue: 108
self._waypoints_queue: 107
self._waypoints_queue: 106
self._waypoints_queue: 105
self._waypoints_queue: 104
self._waypoints_queue: 103
self._waypoints_queue: 102
self._waypoints_queue: 101
self._waypoints_queue: 101
self._waypoints_queue: 99
self._waypoints_queue: 98
self._waypoints_queue: 97
self._waypoints_queue: 96
self._waypoints_queue: 95
self._waypoints_queue: 94
self._waypoints_queue: 93
self._waypoints_queue: 92
self._waypoints_queue: 91
self._waypoints_queue: 90
self._waypoints_queue: 88
self._waypoints_queue: 87
self._waypoints_queue: 86
self._waypoints_queue: 85
self._waypoints_queue: 84
self._waypoints_queue: 83
self._waypoints_queue: 82
self._waypoints_queue: 80
self._waypoints_queue: 79
self._waypoints_queue: 78
self._waypoints_queue: 77
self._waypoints_queue: 76
self._waypoints_queue: 75
self._waypoints_queue: 74
self._waypoints_queue: 73
self._waypoints_queue: 72
self._waypoints_queue: 71
self._waypoints_queue: 70
self._waypoints_queue: 69
self._waypoints_queue: 68
self._waypoints_queue: 67
self._waypoints_queue: 66
self._waypoints_queue: 65
self._waypoints_queue: 64
self._waypoints_queue: 63
self._waypoints_queue: 62
self._waypoints_queue: 62
self._waypoints_queue: 61
self._waypoints_queue: 60
self._waypoints_queue: 58
self._waypoints_queue: 58
self._waypoints_queue: 57
self._waypoints_queue: 56
self._waypoints_queue: 55
self._waypoints_queue: 54
self._waypoints_queue: 53
self._waypoints_queue: 53
self._waypoints_queue: 52
self._waypoints_queue: 51
self._waypoints_queue: 50
self._waypoints_queue: 49
self._waypoints_queue: 48
self._waypoints_queue: 47
self._waypoints_queue: 47
self._waypoints_queue: 46
self._waypoints_queue: 44
self._waypoints_queue: 43
self._waypoints_queue: 42
self._waypoints_queue: 41
self._waypoints_queue: 41
self._waypoints_queue: 39
self._waypoints_queue: 38
self._waypoints_queue: 37
self._waypoints_queue: 36
self._waypoints_queue: 35
self._waypoints_queue: 34
self._waypoints_queue: 33
self._waypoints_queue: 32
self._waypoints_queue: 31
self._waypoints_queue: 30
self._waypoints_queue: 29
self._waypoints_queue: 28
self._waypoints_queue: 27
self._waypoints_queue: 26
self._waypoints_queue: 25
self._waypoints_queue: 24
self._waypoints_queue: 23
self._waypoints_queue: 22
self._waypoints_queue: 21
self._waypoints_queue: 20
self._waypoints_queue: 19
self._waypoints_queue: 18
self._waypoints_queue: 17
self._waypoints_queue: 16
self._waypoints_queue: 15
self._waypoints_queue: 14
self._waypoints_queue: 13
self._waypoints_queue: 11
self._waypoints_queue: 10
self._waypoints_queue: 9
self._waypoints_queue: 8
self._waypoints_queue: 7
self._waypoints_queue: 6
self._waypoints_queue: 5
self._waypoints_queue: 4
self._waypoints_queue: 3
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
self._waypoints_queue: 1
********************
[1m> Stopping the route[0m

[1m========= Results of RouteScenario_1 (repetition 0) ------ [91mFAILURE[0m [1m=========[0m

╒═══════════════════════╤═════════════════════╕
│ Start Time            │ 2025-05-09 11:05:37 │
├───────────────────────┼─────────────────────┤
│ End Time              │ 2025-05-09 11:06:48 │
├───────────────────────┼─────────────────────┤
│ System Time           │ 71.1s               │
├───────────────────────┼─────────────────────┤
│ Game Time             │ 49.6s               │
├───────────────────────┼─────────────────────┤
│ Ratio (Game / System) │ 0.698               │
╘═══════════════════════╧═════════════════════╛

╒═══════════════════════╤═════════╤══════════╕
│ Criterion             │ Result  │ Value    │
├───────────────────────┼─────────┼──────────┤
│ RouteCompletionTest   │ [92mSUCCESS[0m │ 100 %    │
├───────────────────────┼─────────┼──────────┤
│ OutsideRouteLanesTest │ [91mFAILURE[0m │ 39.25 %  │
├───────────────────────┼─────────┼──────────┤
│ CollisionTest         │ [91mFAILURE[0m │ 1 times  │
├───────────────────────┼─────────┼──────────┤
│ RunningRedLightTest   │ [92mSUCCESS[0m │ 0 times  │
├───────────────────────┼─────────┼──────────┤
│ RunningStopTest       │ [92mSUCCESS[0m │ 0 times  │
├───────────────────────┼─────────┼──────────┤
│ MinSpeedTest          │ [91mFAILURE[0m │ 366.92 % │
├───────────────────────┼─────────┼──────────┤
│ InRouteTest           │ [92mSUCCESS[0m │          │
├───────────────────────┼─────────┼──────────┤
│ AgentBlockedTest      │ [92mSUCCESS[0m │          │
├───────────────────────┼─────────┼──────────┤
│ Timeout               │ [92mSUCCESS[0m │          │
╘═══════════════════════╧═════════╧══════════╛

[1m> Registering the route statistics[0m
False
cost time=75.4092926979065
[1m> Registering the global statistics[0m
leaderboard/leaderboard/leaderboard_evaluator.py:21: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
  import pkg_resources
leaderboard/leaderboard/leaderboard_evaluator.py:119: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
  if LooseVersion(dist.version) < LooseVersion('0.9.10'):
