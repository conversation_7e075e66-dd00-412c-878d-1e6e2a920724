#!/bin/bash

# Safe test script with memory checks
echo "=== Safe No-Other-Vehicles Test ==="

# Check available memory
available_memory=$(free -g | awk 'NR==2{printf "%.1f", $7}')
total_memory=$(free -g | awk 'NR==2{printf "%.1f", $2}')

echo "Available memory: ${available_memory}GB / ${total_memory}GB"

# Check if we have enough memory (at least 4GB available)
if (( $(echo "$available_memory < 4.0" | bc -l) )); then
    echo "⚠️  WARNING: Low memory available (${available_memory}GB). Consider closing other applications."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Aborted."
        exit 1
    fi
fi

# Set environment variables
export CARLA_ROOT=/home/<USER>/Carla/
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI/carla
export PYTHONPATH=$PYTHONPATH:$CARLA_ROOT/PythonAPI/carla/dist/carla-0.9.15-py3.7-linux-x86_64.egg
export PYTHONPATH=$PYTHONPATH:leaderboard
export PYTHONPATH=$PYTHONPATH:scenario_runner

export LEADERBOARD_ROOT=leaderboard
export TEAM_AGENT=$LEADERBOARD_ROOT/leaderboard/autoagents/human_agent.py
export ROUTES=/home/<USER>/wangjm/Bench2Drive/leaderboard/data/bench2drive_extend/ours_train_extend_1000_50/Town04_extended.xml
export ROUTES_SUBSET=108
export REPETITIONS=1
export DEBUG_CHALLENGE=0  # Minimal debug to save memory
export CHALLENGE_TRACK_CODENAME=SENSORS
export CHECKPOINT_ENDPOINT="${LEADERBOARD_ROOT}/results_safe.json"
export TEAM_CONFIG=human

export current_t=$(date "+%Y%m%d%H%M%S")

echo "Starting leaderboard with --no-other-vehicles..."
echo "Process will be monitored for memory usage."

# Start memory monitoring in background
python3 monitor_memory.py 300 > memory_log_${current_t}.txt &
MONITOR_PID=$!

# Run the main process
python3 ${LEADERBOARD_ROOT}/leaderboard/leaderboard_evaluator.py \
--routes=${ROUTES} \
--routes-subset=${ROUTES_SUBSET} \
--repetitions=${REPETITIONS} \
--track=${CHALLENGE_TRACK_CODENAME} \
--checkpoint=${CHECKPOINT_ENDPOINT} \
--agent=${TEAM_AGENT} \
--agent-config=${TEAM_CONFIG} \
--debug=${DEBUG_CHALLENGE} \
--person=safe_test \
--no-other-vehicles \
--time=${current_t} \
"$@"

# Stop memory monitoring
kill $MONITOR_PID 2>/dev/null

echo "Test completed. Check memory_log_${current_t}.txt for memory usage details."
